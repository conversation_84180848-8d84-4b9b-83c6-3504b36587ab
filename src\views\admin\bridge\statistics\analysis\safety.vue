<template>
  <div class="bridge-safety-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <div class="stats-row">
        <div class="stats-card score-card">
          <div class="card-icon">
            <svg class="icon-svg" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" 
                    fill="#FFD700" stroke="#FFD700" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-header">
              <h3>最高评分</h3>
            </div>
            <div class="score-display">
              <div class="main-number">{{ scoreStatistics.maxScore || 0 }}</div>
              <div class="score-label">分</div>
            </div>
          </div>
        </div>

        <div class="stats-card score-card">
          <div class="card-icon">
            <svg class="icon-svg" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    stroke="#F56C6C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-header">
              <h3>最低评分</h3>
            </div>
            <div class="score-display">
              <div class="main-number">{{ scoreStatistics.minScore || 0 }}</div>
              <div class="score-label">分</div>
            </div>
          </div>
        </div>

        <div class="stats-card score-card primary">
          <div class="card-icon">
            <svg class="icon-svg" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 3V21H21M7 12L10 9L13 12L20 5" 
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="7" cy="12" r="2" fill="white"/>
              <circle cx="10" cy="9" r="2" fill="white"/>
              <circle cx="13" cy="12" r="2" fill="white"/>
              <circle cx="20" cy="5" r="2" fill="white"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-header">
              <h3>平均评分</h3>
            </div>
            <div class="score-display">
              <div class="main-number">{{ scoreStatistics.avgScore || 0 }}</div>
              <div class="score-label">分</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和表格区域 -->
    <div class="content-section">
      <!-- 左侧：企业评分排名表格 -->
      <div class="table-card">
        <div class="table-header">
          <h3>企业评分排名</h3>
        </div>
        <div class="table-content">
          <el-table :data="enterpriseTableData" stripe class="ranking-table" v-loading="enterpriseLoading">
            <el-table-column prop="index" label="排名" width="80" align="center">
              <template #default="{ $index }">
                <span class="rank-number" :class="getRankClass($index)">{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="manageUnitName" label="企业" min-width="200" show-overflow-tooltip />
            <el-table-column prop="totalScore" label="分值" width="100" align="center">
              <template #default="{ row }">
                <span class="score-badge" :class="getScoreClass(row.totalScore)">{{ row.totalScore || 0 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧：企业评分分布图 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>企业评分分布</h3>
          <div class="chart-info">
            <span class="total-count">得分数量：{{ totalEnterpriseCount }}</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="distributionChartRef" class="chart-container"></div>
          <div class="chart-legend-right">
            <div v-for="item in distributionData" :key="item.code" class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: getScoreRangeColor(item.code) }"></span>
              <span class="legend-text">{{ item.name }}</span>
              <span class="legend-count">{{ item.count }}个</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getSecurityAssessmentScoreStatistics,
  getSecurityAssessmentEnterpriseStatistics,
  getSecurityAssessmentDistributionStatistics
} from '@/api/bridge'

// 响应式数据
const loading = ref(false)
const enterpriseLoading = ref(false)

// 统计数据
const scoreStatistics = reactive({
  avgScore: 0,
  maxScore: 0,
  minScore: 0,
  statisticsList: []
})

const enterpriseTableData = ref([])
const distributionData = ref([])

// 图表引用
const distributionChartRef = ref(null)
let distributionChart = null

// 计算属性
const totalEnterpriseCount = computed(() => {
  return distributionData.value.reduce((total, item) => total + (item.count || 0), 0)
})

// 获取排名样式
const getRankClass = (index) => {
  switch (index) {
    case 0: return 'rank-first'
    case 1: return 'rank-second'
    case 2: return 'rank-third'
    default: return ''
  }
}

// 获取分数样式
const getScoreClass = (score) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  return 'score-poor'
}

// 获取分数区间颜色
const getScoreRangeColor = (code) => {
  const colorMap = {
    '1': '#67c23a',
    '2': '#409eff',
    '3': '#ff9f43',
    '4': '#f56c6c'
  }
  return colorMap[code] || '#909399'
}

// 加载分数统计数据
const loadScoreStatistics = async () => {
  try {
    const response = await getSecurityAssessmentScoreStatistics()
    if (response.code === 200 && response.data) {
      Object.assign(scoreStatistics, response.data)
    }
  } catch (error) {
    console.error('加载分数统计数据失败:', error)
    ElMessage.error('加载分数统计数据失败')
  }
}

// 加载企业排名数据
const loadEnterpriseRanking = async () => {
  try {
    enterpriseLoading.value = true
    const response = await getSecurityAssessmentEnterpriseStatistics()
    if (response.code === 200 && response.data) {
      // 如果返回的是数组，直接使用；如果是对象，转换为数组
      enterpriseTableData.value = Array.isArray(response.data) ? response.data : [response.data]
      // 按分数降序排序
      enterpriseTableData.value.sort((a, b) => (b.totalScore || 0) - (a.totalScore || 0))
    }
  } catch (error) {
    console.error('加载企业排名数据失败:', error)
    ElMessage.error('加载企业排名数据失败')
  } finally {
    enterpriseLoading.value = false
  }
}

// 加载分布数据
const loadDistributionData = async () => {
  try {
    const response = await getSecurityAssessmentDistributionStatistics()
    if (response.code === 200 && response.data) {
      distributionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderDistributionChart()
    }
  } catch (error) {
    console.error('加载分布数据失败:', error)
    ElMessage.error('加载分布数据失败')
  }
}

// 渲染分布图表
const renderDistributionChart = () => {
  if (!distributionChart) {
    distributionChart = echarts.init(distributionChartRef.value)
  }

  const data = distributionData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getScoreRangeColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '得分数量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  distributionChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  if (distributionChartRef.value) {
    distributionChart = echarts.init(distributionChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    distributionChart?.resize()
  })
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadScoreStatistics(),
      loadEnterpriseRanking(),
      loadDistributionData()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 初始化图表
  await initCharts()

  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (distributionChart) {
    distributionChart.dispose()
    distributionChart = null
  }
  window.removeEventListener('resize', () => {
    distributionChart?.resize()
  })
})
</script>

<style scoped>
.bridge-safety-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.score-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
}

.score-card.primary .card-icon {
  background: rgba(255, 255, 255, 0.2);
}

.icon-svg {
  width: 32px;
  height: 32px;
}

.card-content {
  flex: 1;
}

.card-header h3 {
  font-size: 16px;
  color: #606266;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.score-card.primary .card-header h3 {
  color: white;
}

.score-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.main-number {
  font-size: 36px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.score-card.primary .main-number {
  color: white;
}

.score-label {
  font-size: 16px;
  color: #909399;
  font-weight: 500;
}

.score-card.primary .score-label {
  color: rgba(255, 255, 255, 0.8);
}

/* 内容区域 */
.content-section {
  display: flex;
  gap: 20px;
}

.table-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.table-header, .chart-header {
  padding: 24px 24px 0 24px;
  flex-shrink: 0;
}

.table-header h3, .chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-count {
  font-size: 14px;
  color: #909399;
}

.table-content {
  padding: 24px;
  flex: 1;
  overflow: auto;
}

.chart-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.chart-container {
  height: 350px;
  flex: 1;
  min-width: 300px;
}

.chart-legend-right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 14px;
  color: #606266;
  min-width: 60px;
}

.legend-count {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 排名样式 */
.rank-number {
  display: inline-block;
  width: 28px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  border-radius: 50%;
  background: #f0f2f5;
  color: #606266;
  font-weight: bold;
}

.rank-number.rank-first {
  background: #ffd700;
  color: #fff;
}

.rank-number.rank-second {
  background: #c0c0c0;
  color: #fff;
}

.rank-number.rank-third {
  background: #cd7f32;
  color: #fff;
}

/* 分数徽章样式 */
.score-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: bold;
  color: white;
  min-width: 40px;
  text-align: center;
}

.score-badge.score-excellent {
  background: #67c23a;
}

.score-badge.score-good {
  background: #409eff;
}

.score-badge.score-average {
  background: #ff9f43;
}

.score-badge.score-poor {
  background: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-section {
    flex-direction: column;
  }

  .chart-content {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-container {
    height: 300px;
    min-width: auto;
  }

  .chart-legend-right {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .stats-row {
    flex-direction: column;
  }

  .stats-card {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .bridge-safety-analysis-container {
    padding: 15px;
  }

  .stats-card {
    padding: 20px;
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .card-icon {
    width: 56px;
    height: 56px;
  }

  .icon-svg {
    width: 28px;
    height: 28px;
  }

  .main-number {
    font-size: 32px;
  }
}

@media (max-height: 800px) {
  .bridge-safety-analysis-container {
    padding: 15px;
  }

  .table-card {
    max-height: 400px;
  }

  .chart-container {
    height: 280px;
  }
}

/* 表格优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>