<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患详情"
    width="1400px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 隐患信息部分 -->
      <div class="section-title">
        <el-icon><InfoFilled /></el-icon>
        隐患详情
      </div>
      
      <el-form :model="formData" label-width="120px" class="detail-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患来源:">
              <span>{{ formData.dangerSourceName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隐患描述:">
              <span>{{ formData.dangerDesc }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患类型:">
              <span>{{ formData.dangerTypeName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隐患等级:">
              <span>{{ formData.dangerLevelName }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患对象:">
              <span>{{ formData.dangerTargetName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改期限:">
              <span>{{ formData.rectificationDeadline }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患位置:">
              <span>{{ formData.address }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上报时间:">
              <span>{{ formData.reportTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权属单位:">
              <span>{{ formData.ownershipUnitName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人:">
              <span>{{ formData.responsibleUserName }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.longitude && formData.latitude">
          <el-col :span="12">
            <el-form-item label="经度:">
              <span>{{ formData.longitude }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度:">
              <span>{{ formData.latitude }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.picUrls">
          <el-col :span="24">
            <el-form-item label="隐患图片:">
              <div class="image-list">
                <el-image
                  v-for="(url, index) in imageUrls"
                  :key="index"
                  :src="url"
                  :preview-src-list="imageUrls"
                  fit="cover"
                  class="image-item"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 整改情况部分 -->
      <div class="section-title">
        <el-icon><Clock /></el-icon>
        整改情况
      </div>

      <div class="timeline-container">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in statusList"
            :key="index"
            :timestamp="item.dealTime"
            placement="top"
            :color="getTimelineColor(item.handleStatus)"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="status-tag" :class="getStatusClass(item.handleStatus)">
                  {{ item.handleStatusName }}
                </span>
                <span class="operator">{{ item.handleUserName }}</span>
              </div>
              <div class="timeline-description">
                {{ item.description }}
              </div>
              <div v-if="item.picUrls" class="timeline-images">
                <el-image
                  v-for="(url, imgIndex) in item.picUrls.split(',')"
                  :key="imgIndex"
                  :src="url"
                  fit="cover"
                  class="timeline-image"
                  :preview-src-list="item.picUrls.split(',')"
                />
              </div>
              <div v-if="item.remark" class="timeline-remark">
                备注：{{ item.remark }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { InfoFilled, Clock } from '@element-plus/icons-vue';
import { getHiddenDangerStatusList } from '@/api/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表单数据
const formData = ref({});
// 状态列表
const statusList = ref([]);

// 图片URL数组
const imageUrls = computed(() => {
  if (formData.value.picUrls) {
    return formData.value.picUrls.split(',').filter(url => url.trim());
  }
  return [];
});

// 监听props变化
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    formData.value = { ...newVal };
    fetchStatusList();
  }
}, { immediate: true, deep: true });

// 获取整改状态时间线
const fetchStatusList = async () => {
  if (!formData.value.id) return;
  
  try {
    const res = await getHiddenDangerStatusList({ dangerId: formData.value.id });
    if (res && res.code === 200) {
      statusList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取整改状态列表失败:', error);
  }
};

// 获取时间线颜色
const getTimelineColor = (status) => {
  switch (status) {
    case 7003501: // 整改中
      return '#E6A23C';
    case 7003502: // 整改完成
      return '#67C23A';
    default:
      return '#909399';
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 7003501: // 整改中
      return 'status-processing';
    case 7003502: // 整改完成
      return 'status-success';
    default:
      return 'status-default';
  }
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.detail-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-content {
  padding: 0;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #0277FD;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #0277FD;
}

.detail-form {
  margin-bottom: 30px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #666;
}

:deep(.el-form-item__content) {
  color: #333;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.timeline-container {
  background: #fafafa;
  padding: 20px;
  border-radius: 4px;
}

.timeline-content {
  background: white;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.status-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-processing {
  background: #FDF6EC;
  color: #E6A23C;
  border: 1px solid #E6A23C;
}

.status-success {
  background: #F0F9FF;
  color: #67C23A;
  border: 1px solid #67C23A;
}

.status-default {
  background: #F4F4F5;
  color: #909399;
  border: 1px solid #909399;
}

.operator {
  color: #666;
  font-size: 14px;
}

.timeline-description {
  color: #333;
  line-height: 1.6;
  margin-bottom: 12px;
}

.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.timeline-remark {
  color: #666;
  font-size: 12px;
  font-style: italic;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 12px;
}
</style> 