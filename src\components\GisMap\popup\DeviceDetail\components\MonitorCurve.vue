<!-- 设备信息 -->
<template>
  <div class="device-detail-curve">
    <div class="curve-tab">
      <div class="cur-tab1">
        <div
          class="btn"
          :class="{ 'btn-active': state.type === 1 }"
          @click="handleSubtabChange(1)"
        >
          <span>24小时</span>
        </div>
        <div
          class="btn"
          :class="{ 'btn-active': state.type === 2 }"
          @click="handleSubtabChange(2)"
        >
          <span>7天</span>
        </div>
          <div
                  class="btn"
                  :class="{ 'btn-active': state.type === 3 }"
                  @click="handleSubtabChange(3)"
          >
              <span>30天</span>
          </div>
      </div>
      <el-date-picker
        v-model="monthRange"
        type="daterange"
        :clearable="false"
        range-separator="-"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        format="YYYY-MM-DD"
        popper-class="screen-popup-date-picker"
        value-format="YYYY-MM-DD HH:mm:ss"
        :disabled-date="disabledDate"
        @calendar-change="chooseDay = $event[0]"
        @focus="chooseDay = null"
        @change="handleSubtabChange(4)"
      />
    </div>
    <div class="curve-indicators">
       <div>{{state.title}}</div>
       <div>
           <el-select
                   v-model="state.selectIndicator"
                   placeholder="请选择"
                   style="min-width: 120px"
                   @change="handleJczbChange"
           >
               <el-option
                       v-for="item in state.indicators"
                       :key="item.id"
                       :label="item.monitorIndexName"
                       :value="item?.monitorField"
               />
           </el-select>
       </div>
    </div>
    <div class="curve-echart" v-if="tabIndex === 2">
      <SmoothLineChart
              id="smoothLineCharta"
              :value="state.chartData"
              :width="'100%'"
              :height="'270px'"
              :redThreshold="state.redThreshold"
              :orangeThreshold="state.orangeThreshold"
              :yellowThreshold="state.yellowThreshold"
              :latestAlarm="state.latestAlarm"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, computed } from "vue";
import moment from "moment";
import SmoothLineChart from "./SmoothLineChart.vue";
import {
    popupDeviceCurveApiInfo,
    popupMonitorIndicatorsApiInfo
} from "@/components/GisMap/popup/popupApi.js";

const props = defineProps({
    baseInfo: {
        type: Array,
        default: () => [],
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    tabIndex: {
        type: Number,
        default: 1,
    },
});

const state = reactive({
    type: 1,
    resData: [],
    chartData: [],
    title: "",
    indicators: [],
    selectIndicator: "",
    redThreshold: null,
    orangeThreshold: null,
    yellowThreshold: null,
    latestAlarm: null,
});

const toCamelCase = (str)=> {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
};

//指标切换
const handleJczbChange = (value) => {
    const ind = state.indicators.find((item) => item?.monitorField === value);
    const jczb = ind?.monitorIndexName ?? "";
    state.title = jczb && ind?.type === 0 ? `${jczb}` : jczb?`${jczb}（${ind?.measureUnit}）`: "";
    state.chartData = [];
    state.resData.map((item) => {
        if (item[value] !== -999) {
            state.chartData.push({
                // ...item,
                name: item?.monitorTime,
                value: item[value],
                unit: ind?.measureUnit || "",
                jczb: jczb || "",
                type: ind?.type,
            });
        }
    });

    // todo 获取最新报警
   /* if (state.chartData.length > 0) {
        const lastPoint = state.chartData[state.chartData.length - 5];
        state.latestAlarm = {
            value: lastPoint.value,
            unit: lastPoint.unit,
            time: lastPoint.name
        };
    } else {
        state.latestAlarm = null;
    }*/
    // console.log("state.chartData----->>>", state.chartData);
}

const monthRange = ref(["", ""]);
const chooseDay = ref(null);

/**
 * 日期选择器范围控制
 * @param {*} date
 */
const disabledDate = (date) => {
    if (!chooseDay.value) {
        return false;
    }
    const after30Days = moment(date).isAfter(
        moment(chooseDay.value).add(31, "day")
    );
    const before30Days = moment(date).isBefore(
        moment(chooseDay.value).subtract(30, "day")
    );
    return after30Days || before30Days;
};

// 子tab-echart 24小时/7天/30天/自定义
const handleSubtabChange = (index) => {
    state.type = index;
    state.resData = [];
    if (index !== 4) {
        monthRange.value = ["", ""];
    }
    const params = {
        deviceId: props.data.id,
        startTime:
            index === 4
                ? monthRange.value[0]
                : moment()
                    .subtract(index === 1 ? 1 : index === 2 ? 7 : 30, "day")
                    .format("YYYY-MM-DD HH:mm:ss"),
        endTime:
            index === 4
                ? monthRange.value[1]
                : moment().format("YYYY-MM-DD HH:mm:ss"),
    };
    if (popupDeviceCurveApiInfo[props.data?.layerId]){
        Promise.all([
            popupMonitorIndicatorsApiInfo[props.data?.layerId](props.data?.id),
            popupDeviceCurveApiInfo[props.data?.layerId](params)
        ]).then(([indicatorsResponse,curveResponse]) => {
            state.indicators = indicatorsResponse.data;
            state.resData = curveResponse.data;
            if (state.selectIndicator) {
                handleJczbChange(state.selectIndicator);
            } else {
                handleJczbChange(state.selectIndicator = indicatorsResponse.data?.[0]?.monitorField ?? "")
            }
        }).catch((error) => {
            console.error("Error fetching data:", error);
        });
    }
};
handleSubtabChange(2);
</script>

<style lang="scss" scoped>
.device-detail-curve {
  height: 360px;
  overflow-y: auto;
  .curve-tab {
    /*div:nth-child(1) {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        width: 2px;
        height: 13px;
        background: #999;
        right: -10px;
        opacity: 0.8;
        top: 55%;
        transform: translateY(-50%);
      }
    }*/
    display: flex;
    justify-content: space-between;
    align-items: center; //垂直居中

    .cur-tab1 {
      width: 40%;
      display: flex;
      justify-content: left;
      align-items: center; //垂直居中

      .btn {
        width: 96px;
        height: 36px;
        line-height: 36px;
        background: #152d55;
        border-radius: 2px;
        text-align: center;
        cursor: pointer;

        > span {
          font-size: 15px;
          font-weight: 400;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      .btn-active {
        background: #0d93f7;
        > span {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
  :deep(.el-date-editor) {
    width: 360px;
    margin-left: 20px;
    flex-grow: 0 !important;
    background: #152d55;
    border: none;
    box-shadow: none;
    height: 36px;

    .el-range-input,
    .el-range-separator {
      color: #fff;
    }
  }
  .curve-indicators{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 14px;
  }
  .curve-echart {
    margin-top: 10px;
    //height: calc(100% - 46px);
  }
}
</style>
