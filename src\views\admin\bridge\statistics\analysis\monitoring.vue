<template>
  <div class="bridge-monitoring-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 筛选条件 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" inline>
          <el-form-item label="所属桥梁:">
            <el-select 
              v-model="filterForm.bridgeId" 
              placeholder="请选择" 
              clearable 
              style="width: 200px"
              @change="handleBridgeChange"
            >
              <el-option label="全部桥梁" value="" />
              <el-option 
                v-for="bridge in bridgeList" 
                :key="bridge.id" 
                :label="bridge.bridgeName" 
                :value="bridge.id" 
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <div class="stats-row">
        <div class="stats-card total-card">
          <div class="card-icon">
            <svg class="icon-svg" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                    fill="#409EFF" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="card-content">
            <div class="card-header">
              <h3>设备总数</h3>
            </div>
            <div class="total-display">
              <div class="main-number">{{ deviceTotalData.totalCount || 0 }}</div>
              <div class="total-label">台</div>
            </div>
            <div class="sub-stats">
              <div class="sub-stat online">
                <span class="label">使用中:</span>
                <span class="value">{{ deviceTotalData.onlineCount || 0 }}台</span>
              </div>
              <div class="sub-stat offline">
                <span class="label">未使用:</span>
                <span class="value">{{ deviceTotalData.offlineCount || 0 }}台</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用状态统计圆环图 -->
        <div class="stats-card chart-card">
          <div class="card-header">
            <h3>使用状态</h3>
          </div>
          <div class="chart-content-use-status">
            <div ref="useStatusChartRef" class="use-status-chart" style="width: 12rem; height: 12rem;"></div>
            <div class="chart-legend">
              <div v-for="item in useStatusData" :key="item.code" class="legend-item">
                <span class="legend-color" :style="{ backgroundColor: getUseStatusColor(item.code) }"></span>
                <span class="legend-text">{{ item.name }}:</span>
                <span class="legend-count">{{ item.count }}台</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="content-section">
      <!-- 左侧：超载次数分布 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>设备类型统计</h3>
        </div>
        <div class="chart-content">
          <div ref="deviceTypeChartRef" class="chart-container" style="width: 100%; height: 350px;"></div>
        </div>
      </div>

      <!-- 右侧：监测状态统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>监测状态统计</h3>
        </div>
        <div class="chart-content">
          <div ref="onlineStatusChartRef" class="chart-container" style="width: 100%; height: 350px;"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getMonitorDeviceTotalCount,
  getMonitorDeviceUseStatusStatistics,
  getMonitorDeviceOnlineStatusStatistics,
  getMonitorDeviceTypeStatistics,
  getBridgeBasicInfoList
} from '@/api/bridge'

// 响应式数据
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  bridgeId: ''
})

// 基础数据
const bridgeList = ref([])
const deviceTotalData = ref({
  totalCount: 0,
  onlineCount: 0,
  offlineCount: 0,
  statisticsList: []
})
const useStatusData = ref([])
const onlineStatusData = ref([])
const deviceTypeData = ref([])

// 图表引用
const useStatusChartRef = ref(null)
const onlineStatusChartRef = ref(null)
const deviceTypeChartRef = ref(null)

let useStatusChart = null
let onlineStatusChart = null
let deviceTypeChart = null

// 计算属性
const deviceTypeCount = computed(() => {
  return deviceTypeData.value.reduce((total, item) => total + (item.count || 0), 0)
})

const onlineStatusCount = computed(() => {
  return onlineStatusData.value.reduce((total, item) => total + (item.count || 0), 0)
})

// 获取使用状态颜色
const getUseStatusColor = (code) => {
  const colorMap = {
    '1': '#67c23a', // 使用中 - 绿色
    '2': '#f56c6c', // 未使用 - 红色
    '3': '#909399'  // 报废 - 灰色
  }
  return colorMap[code] || '#909399'
}

// 获取监测状态颜色
const getOnlineStatusColor = (code) => {
  const colorMap = {
    '0': '#f56c6c',   // 离线 - 红色
    '1': '#67c23a',   // 在线 - 绿色
    '2': '#ff9f43'    // 故障 - 橙色
  }
  return colorMap[code] || '#909399'
}

// 获取设备类型颜色
const getDeviceTypeColor = (index) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#6f7ad3']
  return colors[index % colors.length]
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 加载设备总数统计
const loadDeviceTotalCount = async () => {
  try {
    const params = { bridgeId: filterForm.bridgeId }
    const response = await getMonitorDeviceTotalCount(params)
    if (response.code === 200 && response.data) {
      deviceTotalData.value = response.data
    }
  } catch (error) {
    console.error('加载设备总数统计失败:', error)
    ElMessage.error('加载设备总数统计失败')
  }
}

// 加载使用状态统计
const loadUseStatusStatistics = async () => {
  try {
    const params = { bridgeId: filterForm.bridgeId }
    const response = await getMonitorDeviceUseStatusStatistics(params)
    if (response.code === 200 && response.data) {
      useStatusData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderUseStatusChart()
    }
  } catch (error) {
    console.error('加载使用状态统计失败:', error)
    ElMessage.error('加载使用状态统计失败')
  }
}

// 加载监测状态统计
const loadOnlineStatusStatistics = async () => {
  try {
    const params = { bridgeId: filterForm.bridgeId }
    const response = await getMonitorDeviceOnlineStatusStatistics(params)
    if (response.code === 200 && response.data) {
      onlineStatusData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderOnlineStatusChart()
    }
  } catch (error) {
    console.error('加载监测状态统计失败:', error)
    ElMessage.error('加载监测状态统计失败')
  }
}

// 加载设备类型统计
const loadDeviceTypeStatistics = async () => {
  try {
    const params = { bridgeId: filterForm.bridgeId }
    const response = await getMonitorDeviceTypeStatistics(params)
    if (response.code === 200 && response.data) {
      deviceTypeData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderDeviceTypeChart()
    }
  } catch (error) {
    console.error('加载设备类型统计失败:', error)
    ElMessage.error('加载设备类型统计失败')
  }
}

// 渲染使用状态圆环图
const renderUseStatusChart = () => {
  if (!useStatusChart || useStatusData.value.length === 0) {
    return
  }

  const data = useStatusData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getUseStatusColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}台 ({d}%)'
    },
    series: [
      {
        name: '使用状态',
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '55%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  try {
    useStatusChart.setOption(option, true)
    useStatusChart.resize()
  } catch (error) {
    console.error('使用状态图表渲染失败:', error)
  }
}

// 渲染监测状态横向柱状图
const renderOnlineStatusChart = () => {
  if (!onlineStatusChart || onlineStatusData.value.length === 0) {
    return
  }

  const data = onlineStatusData.value.map((item, index) => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getOnlineStatusColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        return `${params[0].name}: ${params[0].value}台`
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '10%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      }
    },
    series: [
      {
        type: 'bar',
        data: data,
        barWidth: '50%',
        itemStyle: {
          borderRadius: [0, 4, 4, 0]
        }
      }
    ]
  }

  try {
    onlineStatusChart.setOption(option, true)
    onlineStatusChart.resize()
  } catch (error) {
    console.error('监测状态图表渲染失败:', error)
  }
}

// 渲染设备类型柱状图
const renderDeviceTypeChart = () => {
  if (!deviceTypeChart || deviceTypeData.value.length === 0) {
    return
  }

  const data = deviceTypeData.value.map((item, index) => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getDeviceTypeColor(index)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        return `${params[0].name}: ${params[0].value}台`
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        interval: 0,
        rotate: data.length > 5 ? 30 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        type: 'bar',
        data: data,
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        }
      }
    ]
  }

  try {
    deviceTypeChart.setOption(option, true)
    deviceTypeChart.resize()
  } catch (error) {
    console.error('设备类型图表渲染失败:', error)
  }
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  return new Promise((resolve) => {
    setTimeout(() => {
      // 初始化使用状态图表
      if (useStatusChartRef.value) {
        useStatusChart = echarts.init(useStatusChartRef.value)
      }
      
      // 初始化监测状态图表
      if (onlineStatusChartRef.value) {
        onlineStatusChart = echarts.init(onlineStatusChartRef.value)
      }
      
      // 初始化设备类型图表
      if (deviceTypeChartRef.value) {
        deviceTypeChart = echarts.init(deviceTypeChartRef.value)
      }
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        useStatusChart?.resize()
        onlineStatusChart?.resize()
        deviceTypeChart?.resize()
      })
      
      resolve()
    }, 1000)
  })
}

// 桥梁选择变化
const handleBridgeChange = () => {
  loadAllData()
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadDeviceTotalCount(),
      loadUseStatusStatistics(),
      loadOnlineStatusStatistics(),
      loadDeviceTypeStatistics()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 先加载桥梁列表
  await loadBridgeList()
  
  // 等待图表容器渲染完成后再初始化
  await initCharts()
  
  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (useStatusChart) {
    useStatusChart.dispose()
    useStatusChart = null
  }
  if (onlineStatusChart) {
    onlineStatusChart.dispose()
    onlineStatusChart = null
  }
  if (deviceTypeChart) {
    deviceTypeChart.dispose()
    deviceTypeChart = null
  }
  window.removeEventListener('resize', () => {
    useStatusChart?.resize()
    onlineStatusChart?.resize()
    deviceTypeChart?.resize()
  })
})
</script>

<style scoped>
.bridge-monitoring-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 200px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 筛选条件区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.total-card {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20px;
}

.chart-card {
  flex: 1;
}

.card-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
}

.icon-svg {
  width: 32px;
  height: 32px;
}

.card-content {
  flex: 1;
}

.card-header h3 {
  font-size: 16px;
  color: #606266;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.total-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 16px;
}

.main-number {
  font-size: 36px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.total-label {
  font-size: 16px;
  color: #909399;
  font-weight: 500;
}

.sub-stats {
  display: flex;
  gap: 20px;
}

.sub-stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.sub-stat.online .label {
  color: #67c23a;
}

.sub-stat.offline .label {
  color: #f56c6c;
}

.sub-stat .value {
  color: #303133;
  font-weight: 500;
}

/* 图表内容 */
.chart-content {
  display: flex;
  align-items: center;
  gap: 20px;
}
.chart-content-use-status {
  display: flex;
  align-items: center;
}
.use-status-chart {
  height: 180px;
  flex: 1;
  min-width: 180px;
}

.chart-legend {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-text {
  color: #606266;
  min-width: 50px;
}

.legend-count {
  color: #303133;
  font-weight: 500;
}

/* 内容区域 */
.content-section {
  display: flex;
  gap: 20px;
}

.content-section .chart-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-count {
  font-size: 14px;
  color: #909399;
}

.chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.chart-container {
  flex: 1;
  min-height: 350px;
  width: 100%;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-section {
    flex-direction: column;
  }
}

@media (max-width: 1200px) {
  .stats-row {
    flex-direction: column;
  }

  .chart-content {
    flex-direction: column;
    align-items: stretch;
  }

  .use-status-chart {
    height: 250px;
    min-width: auto;
  }

  .chart-legend {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .bridge-monitoring-analysis-container {
    padding: 15px;
  }

  .total-card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .card-icon {
    width: 56px;
    height: 56px;
  }

  .icon-svg {
    width: 28px;
    height: 28px;
  }

  .main-number {
    font-size: 32px;
  }

  .sub-stats {
    justify-content: center;
  }
}

@media (max-height: 800px) {
  .bridge-monitoring-analysis-container {
    padding: 15px;
  }

  .chart-container {
    height: 280px;
  }

  .use-status-chart {
    height: 160px;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ElementUI 组件优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-select) {
  width: 100%;
}
</style>