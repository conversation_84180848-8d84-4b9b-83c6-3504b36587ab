<template>
  <div class="flood-scheme-container">
    <!-- 搜索区域 -->
    <div class="flood-scheme-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">来源单位:</span>
          <el-select v-model="formData.sourceUnit" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in enterpriseOptions" :key="item.enterpriseName" :label="item.enterpriseName" :value="item.enterpriseName" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">匹配报警类型:</span>
          <el-select v-model="formData.alarmType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in alarmTypeOptions" :key="item.id" :label="item.alarmTypeName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">匹配事件类型:</span>
          <el-select v-model="formData.eventType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.schemeName" class="form-input" placeholder="输入方案名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button> -->
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="schemeName" label="方案名称" min-width="200" />
        <el-table-column prop="sourceUnitName" label="来源单位" min-width="150" />
        <el-table-column prop="alarmTypeName" label="匹配报警类型" min-width="160" />
        <el-table-column prop="eventTypeName" label="匹配事件类型" min-width="140" />
        <el-table-column label="附件" min-width="100">
          <template #default="{ row }">
            <el-button 
              v-if="row.fileUrl" 
              type="primary" 
              link 
              @click.stop="handleDownload(row)"
            >
              查看附件
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="editDate" label="编制日期" min-width="120" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <FloodEmergencySchemeDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getFloodEmergencySchemeePage, 
  deleteFloodEmergencyScheme, 
  getFloodEmergencySchemeDetail,
  getEnterpriseList,
  getAlarmType
} from '@/api/drainage';
import { EVENT_TYPE_OPTIONS } from '@/constants/drainage';
import FloodEmergencySchemeDialog from './FloodEmergencySchemeDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const enterpriseOptions = ref([]);
const alarmTypeOptions = ref([]);
const eventTypeOptions = ref(EVENT_TYPE_OPTIONS);

// 表单数据
const formData = ref({
  sourceUnit: '',
  alarmType: '',
  eventType: '',
  schemeName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchSchemeData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    sourceUnit: '',
    alarmType: '',
    eventType: '',
    schemeName: ''
  };
  currentPage.value = 1;
  fetchSchemeData();
};

// 获取方案分页数据
const fetchSchemeData = async () => {
  try {
    const params = {
      sourceUnit: formData.value.sourceUnit,
      alarmType: formData.value.alarmType,
      eventType: formData.value.eventType,
      schemeName: formData.value.schemeName
    };
    
    const res = await getFloodEmergencySchemeePage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取方案数据失败:', error);
    ElMessage.error('获取方案数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取企业列表失败', error);
  }
};

// 获取报警类型列表
const fetchAlarmTypes = async () => {
  try {
    const res = await getAlarmType();
    if (res && res.data) {
      alarmTypeOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取报警类型列表失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchSchemeData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchSchemeData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getFloodEmergencySchemeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取方案详情失败');
    }
  } catch (error) {
    console.error('获取方案详情失败:', error);
    ElMessage.error('获取方案详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getFloodEmergencySchemeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取方案详情失败');
    }
  } catch (error) {
    console.error('获取方案详情失败:', error);
    ElMessage.error('获取方案详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该方案信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteFloodEmergencyScheme(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchSchemeData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除方案失败:', error);
      ElMessage.error('删除方案失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理下载附件
const handleDownload = (row) => {
  if (row.fileUrl) {
    window.open(row.fileUrl, '_blank');
  } else {
    ElMessage.warning('没有附件可下载');
  }
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchSchemeData();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchEnterprises(),
      fetchAlarmTypes(),
      fetchSchemeData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.flood-scheme-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.flood-scheme-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 