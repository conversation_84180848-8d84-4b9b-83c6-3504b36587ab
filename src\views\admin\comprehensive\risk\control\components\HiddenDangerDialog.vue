<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="hidden-danger-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患来源" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in sourceTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in relatedBusinessOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="隐患描述" prop="dangerDesc">
            <el-input v-model="formData.dangerDesc" placeholder="请输入隐患描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患类型" prop="dangerType">
            <el-select v-model="formData.dangerType" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in dangerTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="隐患等级" prop="dangerLevel">
            <el-select v-model="formData.dangerLevel" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in dangerLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患对象" prop="dangerTarget">
            <el-select v-model="formData.dangerTarget" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in dangerObjectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="整改期限" prop="rectificationDeadline">
            <el-date-picker
              v-model="formData.rectificationDeadline"
              type="datetime"
              placeholder="请选择整改期限"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-tree-select
              v-model="formData.ownershipUnit"
              :data="ownershipUnitOptions"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
              placeholder="请选择权属单位"
              check-strictly
              class="w-full"
              @change="handleOwnershipUnitChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" prop="responsibleUserName">
            <el-input v-model="formData.responsibleUserName" placeholder="请输入责任人姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上报人" prop="reportUser">
            <el-input v-model="formData.reportUser" placeholder="请输入上报人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报时间" prop="reportTime">
            <el-date-picker
              v-model="formData.reportTime"
              type="datetime"
              placeholder="请选择上报时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="隐患位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="隐患图片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              list-type="picture-card"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传 .jpg .jpeg .png .gif .bmp .JPG .JPEG .PNG .GIF .BMP .svg .SVG
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 详情模式下显示整改情况时间线 -->
      <el-row v-if="mode === 'view'" :gutter="20">
        <el-col :span="24">
          <el-form-item label="整改情况">
            <div class="timeline-container">
              <el-timeline>
                <el-timeline-item
                  v-for="(item, index) in timelineData"
                  :key="index"
                  :timestamp="item.dealTime"
                  placement="top"
                >
                  <el-card>
                    <div class="timeline-item">
                      <h4>{{ item.handleStatusName }}</h4>
                      <p><strong>处理人：</strong>{{ item.handleUserName }}</p>
                      <p><strong>处理描述：</strong>{{ item.description }}</p>
                      <p v-if="item.remark"><strong>备注：</strong>{{ item.remark }}</p>
                      <div v-if="item.picUrls" class="image-list">
                        <el-image
                          v-for="(url, idx) in item.picUrls.split(',')"
                          :key="idx"
                          :src="url"
                          :preview-src-list="item.picUrls.split(',')"
                          fit="cover"
                          class="timeline-image"
                        />
                      </div>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Location } from '@element-plus/icons-vue'
import {
  saveHiddenDanger,
  updateHiddenDanger,
  getHiddenDangerStatusList,
} from '@/api/comprehensive'
import { HIDDEN_DANGER_LEVEL_OPTIONS, HIDDEN_DANGER_TYPE_OPTIONS, HIDDEN_DANGER_OBJECT_OPTIONS, HIDDEN_DANGER_SOURCE_OPTIONS, RELATED_BUSINESS_OPTIONS } from '@/constants/comprehensive'
import { getDeptTree } from '@/api/system'
import { uploadFile } from '@/api/upload'
import { AREA_OPTIONS } from '@/constants/bridge'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const fileList = ref([])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增隐患信息',
    edit: '编辑隐患信息',
    view: '隐患信息详情'
  }
  return titles[props.mode] || '隐患信息'
})

// 下拉选项数据
const sourceTypeOptions = ref(HIDDEN_DANGER_SOURCE_OPTIONS)
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const dangerTypeOptions = ref(HIDDEN_DANGER_TYPE_OPTIONS)
const dangerLevelOptions = ref(HIDDEN_DANGER_LEVEL_OPTIONS)
const dangerObjectOptions = ref(HIDDEN_DANGER_OBJECT_OPTIONS)
const ownershipUnitOptions = ref([])
const areaOptions = ref(AREA_OPTIONS)
const timelineData = ref([])

// 表单数据
const formData = reactive({
  id: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  dangerCode: '',
  dangerDesc: '',
  dangerLevel: '',
  dangerLevelName: '',
  dangerObjectId: '',
  dangerObjectName: '',
  dangerStatus: '',
  dangerStatusName: '',
  dangerTarget: '',
  dangerTargetName: '',
  dangerType: '',
  dangerTypeName: '',
  keyWord: '',
  latitude: '',
  longitude: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  picUrls: '',
  rectificationDeadline: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  reportTime: '',
  reportUser: '',
  responsibleUserId: '',
  responsibleUserName: '',
  sourceType: '',
  sourceTypeName: '',
  town: '',
  townName: ''
})

// 表单验证规则
const formRules = {
  sourceType: [{ required: true, message: '请选择隐患来源', trigger: 'change' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  dangerDesc: [{ required: true, message: '请输入隐患描述', trigger: 'blur' }],
  dangerType: [{ required: true, message: '请选择隐患类型', trigger: 'change' }],
  dangerLevel: [{ required: true, message: '请选择隐患等级', trigger: 'change' }],
  dangerTarget: [{ required: true, message: '请选择隐患对象', trigger: 'change' }],
  rectificationDeadline: [{ required: true, message: '请选择整改期限', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  responsibleUserName: [{ required: true, message: '请输入责任人姓名', trigger: 'blur' }],
  reportUser: [{ required: true, message: '请输入上报人', trigger: 'blur' }],
  reportTime: [{ required: true, message: '请选择上报时间', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
  timelineData.value = []
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理图片显示
    if (newVal.picUrls) {
      fileList.value = newVal.picUrls.split(',').map((url, index) => ({
        name: `image_${index}`,
        url: url.trim(),
        uid: Date.now() + index
      })).filter(item => item.url)
    }

    // 如果是详情模式，获取时间线数据
    if (props.mode === 'view' && newVal.id) {
      fetchTimelineData(newVal.id)
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 监听下拉选择变化，更新对应的名称字段
watch(() => formData.sourceType, (val) => {
  if (val) {
    const selected = sourceTypeOptions.value.find(item => item.value === val)
    if (selected) formData.sourceTypeName = selected.label
  }
})

watch(() => formData.relatedBusiness, (val) => {
  if (val) {
    const selected = relatedBusinessOptions.value.find(item => item.value === val)
    if (selected) formData.relatedBusinessName = selected.label
  }
})

watch(() => formData.dangerType, (val) => {
  if (val) {
    const selected = dangerTypeOptions.value.find(item => item.value === val)
    if (selected) formData.dangerTypeName = selected.label
  }
})

watch(() => formData.dangerLevel, (val) => {
  if (val) {
    const selected = dangerLevelOptions.value.find(item => item.value === val)
    if (selected) formData.dangerLevelName = selected.label
  }
})

watch(() => formData.dangerTarget, (val) => {
  if (val) {
    const selected = dangerObjectOptions.value.find(item => item.value === val)
    if (selected) formData.dangerTargetName = selected.label
  }
})

const findName = (tree, value) => {
  if (!tree || !Array.isArray(tree)) return false
  
  for (const item of tree) {
    if (item.id === value) {
      formData.ownershipUnitName = item.name
      return true
    }
    
    if (item.children && item.children.length > 0) {
      if (findName(item.children, value)) {
        return true
      }
    }
  }
  return false
}

// 处理权属单位变化
const handleOwnershipUnitChange = (value) => {
  if (value) {
    findName(ownershipUnitOptions.value, value)
  } else {
    formData.ownershipUnitName = ''
  }
}

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1]
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town)
    if (selectedArea) {
      formData.townName = selectedArea.name
    }
  }
}

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code)
      if (found) return found
    }
  }
  return null
}

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.picUrls ? formData.picUrls.split(',') : []
      urls.push(response.data.url)
      formData.picUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true
  bus.off("getCollectLocation", handleCollectLocation)
  bus.on("getCollectLocation", handleCollectLocation)
}

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 获取权属单位列表
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.data) {
      ownershipUnitOptions.value = res.data
    }
  } catch (error) {
    console.error('获取权属单位列表失败', error)
  }
}

// 获取时间线数据
const fetchTimelineData = async (dangerId) => {
  try {
    const res = await getHiddenDangerStatusList(dangerId)
    if (res && res.code === 200) {
      timelineData.value = res.data || []
    }
  } catch (error) {
    console.error('获取时间线数据失败', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveHiddenDanger(submitData)
    } else if (props.mode === 'edit') {
      res = await updateHiddenDanger(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
})
</script>

<style scoped>
.hidden-danger-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}

.timeline-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  background-color: #fafafa;
}

.timeline-item h4 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.timeline-item p {
  margin: 4px 0;
  color: #666;
}

.image-list {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}
</style> 