<template>
  <div class="heating-hidden-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <!-- 状态统计 -->
      <div class="status-statistics">
        <div class="stat-card total">
          <div class="stat-number">{{ statusStatistics.totalCount }}</div>
          <div class="stat-label">全部隐患</div>
        </div>
        <div class="stat-card pending">
          <div class="stat-number">{{ statusStatistics.unRectifyCount }}</div>
          <div class="stat-label">待整改</div>
        </div>
        <div class="stat-card processing">
          <div class="stat-number">{{ statusStatistics.rectifyingCount }}</div>
          <div class="stat-label">整改中</div>
        </div>
        <div class="stat-card reviewing">
          <div class="stat-number">{{ statusStatistics.reviewCount }}</div>
          <div class="stat-label">待复查</div>
        </div>
        <div class="stat-card completed">
          <div class="stat-number">{{ statusStatistics.rectifiedCount }}</div>
          <div class="stat-label">已整改</div>
        </div>
      </div>

      <!-- 等级统计 -->
      <div class="level-statistics">
        <div class="level-card major">
          <div class="level-number">{{ levelStatistics.bigRiskCount }}</div>
          <div class="level-label">重大隐患</div>
        </div>
        <div class="level-card larger">
          <div class="level-number">{{ levelStatistics.largerRiskCount }}</div>
          <div class="level-label">较大隐患</div>
        </div>
        <div class="level-card general">
          <div class="level-number">{{ levelStatistics.generalRiskCount }}</div>
          <div class="level-label">一般隐患</div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">隐患编码:</span>
          <el-input v-model="searchForm.dangerCode" class="form-input" placeholder="输入隐患编码" />
        </div>
        <div class="form-item">
          <span class="label">隐患状态:</span>
          <el-select v-model="searchForm.dangerStatus" class="form-input" placeholder="请选择">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">隐患等级:</span>
          <el-select v-model="searchForm.dangerLevel" class="form-input" placeholder="请选择">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">上报时间:</span>
          <el-date-picker
            v-model="searchForm.reportTime"
            type="date"
            placeholder="请选择"
            class="form-input"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>
        <div class="form-item">
          <span class="label">责任人:</span>
          <el-input v-model="searchForm.responsibleUserName" class="form-input" placeholder="输入责任人" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 上报隐患</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="dangerCode" label="隐患编码" min-width="120" />
        <el-table-column prop="dangerDesc" label="隐患描述" min-width="150" />
        <el-table-column prop="dangerTypeName" label="隐患类型" min-width="120" />
        <el-table-column prop="dangerLevelName" label="隐患等级" min-width="100" />
        <el-table-column prop="dangerTargetName" label="隐患对象" min-width="100" />
        <el-table-column prop="dangerStatusName" label="隐患状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.dangerStatus)">
              {{ row.dangerStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rectificationDeadline" label="整改期限" min-width="120" />
        <el-table-column prop="reportTime" label="上报时间" min-width="120" />
        <el-table-column prop="ownershipUnitName" label="所属单位" min-width="120" />
        <el-table-column prop="responsibleUserName" label="责任人" min-width="100" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleRectify(row)">隐患整改</el-button>
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <HiddenDangerDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 隐患整改弹窗 -->
    <HiddenDangerRectifyDialog
      v-model:visible="rectifyDialogVisible"
      :danger-id="selectedDangerId"
    />

    <!-- 隐患详情弹窗 -->
    <HiddenDangerDetailDialog
      v-model:visible="detailDialogVisible"
      :data="detailData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  getHeatingHiddenDangerPage,
  getHeatingHiddenDangerStatusStatistics,
  getHeatingHiddenDangerLevelStatistics,
  getHeatingHiddenDangerDetail
} from '@/api/heating';
import { misPosition } from '@/hooks/gishooks';
import {
  HEATING_HIDDEN_DANGER_STATUS_OPTIONS,
  HEATING_HIDDEN_DANGER_LEVEL_OPTIONS
} from '@/constants/heating';
import HiddenDangerDialog from './components/HiddenDangerDialog.vue';
import HiddenDangerRectifyDialog from './components/HiddenDangerRectifyDialog.vue';
import HiddenDangerDetailDialog from './components/HiddenDangerDetailDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 统计数据
const statusStatistics = ref({
  totalCount: 0,
  unRectifyCount: 0,
  rectifyingCount: 0,
  reviewCount: 0,
  rectifiedCount: 0
});

const levelStatistics = ref({
  bigRiskCount: 0,
  largerRiskCount: 0,
  generalRiskCount: 0
});

// 下拉选项数据
const statusOptions = ref(HEATING_HIDDEN_DANGER_STATUS_OPTIONS);
const levelOptions = ref(HEATING_HIDDEN_DANGER_LEVEL_OPTIONS);

// 搜索表单数据
const searchForm = reactive({
  dangerCode: '',
  dangerStatus: '',
  dangerLevel: '',
  reportTime: '',
  responsibleUserName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

const rectifyDialogVisible = ref(false);
const selectedDangerId = ref('');

const detailDialogVisible = ref(false);
const detailData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 2002701: // 待整改
      return 'danger';
    case 2002702: // 整改中
      return 'warning';
    case 2002703: // 待复查
      return 'info';
    case 2002704: // 已整改
      return 'success';
    default:
      return '';
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchHiddenDangerData();
};

// 处理重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = '';
  });
  currentPage.value = 1;
  fetchHiddenDangerData();
};

// 获取隐患分页数据
const fetchHiddenDangerData = async () => {
  try {
    const params = {
      dangerCode: searchForm.dangerCode,
      dangerStatus: searchForm.dangerStatus,
      dangerLevel: searchForm.dangerLevel,
      reportTime: searchForm.reportTime,
      responsibleUserName: searchForm.responsibleUserName
    };
    
    const res = await getHeatingHiddenDangerPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取隐患数据失败:', error);
    ElMessage.error('获取隐患数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取状态统计数据
const fetchStatusStatistics = async () => {
  try {
    const res = await getHeatingHiddenDangerStatusStatistics();
    if (res && res.code === 200) {
      statusStatistics.value = res.data || {};
    }
  } catch (error) {
    console.error('获取状态统计数据失败:', error);
  }
};

// 获取等级统计数据
const fetchLevelStatistics = async () => {
  try {
    const res = await getHeatingHiddenDangerLevelStatistics();
    if (res && res.code === 200) {
      levelStatistics.value = res.data || {};
    }
  } catch (error) {
    console.error('获取等级统计数据失败:', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchHiddenDangerData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchHiddenDangerData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理隐患整改
const handleRectify = (row) => {
  selectedDangerId.value = row.id;
  rectifyDialogVisible.value = true;
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getHeatingHiddenDangerDetail(row.id);
    if (res && res.code === 200) {
      detailData.value = res.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error('获取隐患详情失败');
    }
  } catch (error) {
    console.error('获取隐患详情失败:', error);
    ElMessage.error('获取隐患详情失败');
  }
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude !== '' &&
    row.longitude &&
    row.longitude !== ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  } else {
    ElMessage.warning('没有经纬度，无法定位！');
  }
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchHiddenDangerData();
  fetchStatusStatistics();
  fetchLevelStatistics();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatusStatistics(),
      fetchLevelStatistics(),
      fetchHiddenDangerData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.heating-hidden-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  margin-bottom: 20px;
}

.status-statistics {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
}

.stat-card.total {
  background: linear-gradient(135deg, #909399, #C0C4CC);
}

.stat-card.pending {
  background: linear-gradient(135deg, #F56C6C, #FF8080);
}

.stat-card.processing {
  background: linear-gradient(135deg, #E6A23C, #F0C040);
}

.stat-card.reviewing {
  background: linear-gradient(135deg, #909399, #B0B4B8);
}

.stat-card.completed {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.level-statistics {
  display: flex;
  gap: 16px;
}

.level-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
}

.level-card.major {
  background: linear-gradient(135deg, #DC143C, #FF4040);
}

.level-card.larger {
  background: linear-gradient(135deg, #FF8C00, #FFA500);
}

.level-card.general {
  background: linear-gradient(135deg, #1E90FF, #4169E1);
}

.level-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.level-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 