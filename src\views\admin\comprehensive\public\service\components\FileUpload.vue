<template>
  <div class="file-upload-container">
    <!-- 文件上传按钮 -->
    <div class="upload-header">
      <el-upload
        ref="uploadRef"
        :auto-upload="false"
        :on-change="handleFileChange"
        :file-list="[]"
        :disabled="fileList.length >= limit"
        :show-file-list="false"
        multiple
        :accept="accept"
      >
        <el-button 
          type="primary" 
          size="small"
          :disabled="fileList.length >= limit"
        >
          选择文件
        </el-button>
      </el-upload>
      <span class="upload-tip">{{ getTipText() }}</span>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div 
        class="file-item" 
        v-for="(file, index) in fileList" 
        :key="file.uid || index"
      >
        <div class="file-info">
          <el-icon class="file-icon">
            <Document v-if="isDocumentFile(file.name)" />
            <Picture v-else-if="isImageFile(file.name)" />
            <Files v-else />
          </el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button 
            type="primary" 
            link 
            size="small"
            @click="handleFileRemove(file, index)"
          >
            删除
          </el-button>
          <el-button 
            type="primary" 
            link 
            size="small"
            @click="handleFilePreview(file)"
            v-if="file.url && file.status === 'success'"
          >
            查看
          </el-button>
          <el-button 
            type="primary" 
            link 
            size="small"
            @click="handleFileDownload(file)"
            v-if="file.url && file.status === 'success'"
          >
            下载
          </el-button>
          <div class="upload-status" v-if="file.status === 'uploading'">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span>上传中</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading } from '@element-plus/icons-vue'
import { uploadFile } from '@/api/upload'

const props = defineProps({
  fileList: {
    type: Array,
    default: () => []
  },
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt'
  },
  limit: {
    type: Number,
    default: 3
  },
  maxSize: {
    type: Number,
    default: 50 // MB
  }
})

const emit = defineEmits(['update:fileList', 'update:urls'])

// 表单引用
const uploadRef = ref(null)

// 内部文件列表
const internalFileList = ref([])

// 计算属性：文件列表
const fileList = computed({
  get: () => props.fileList,
  set: (val) => emit('update:fileList', val)
})

// 获取提示文本
const getTipText = () => {
  const fileTypeText = props.accept.includes('image') ? '图片' : '文件'
  return `最多${props.limit}个${fileTypeText}，大小${props.maxSize}M以内`
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return ''
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= props.limit) {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    return false
  }

  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`上传文件大小不能超过 ${props.maxSize}MB!`)
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  const newFileList = [...fileList.value, fileItem]
  emit('update:fileList', newFileList)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = newFileList.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        newFileList[index].url = response.data.url
        newFileList[index].status = 'success'
        emit('update:fileList', [...newFileList])
      }
      
      // 发出URL更新事件
      updateUrls(newFileList)
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = newFileList.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        newFileList.splice(index, 1)
        emit('update:fileList', [...newFileList])
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = newFileList.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      newFileList.splice(index, 1)
      emit('update:fileList', [...newFileList])
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  const newFileList = [...fileList.value]
  newFileList.splice(index, 1)
  emit('update:fileList', newFileList)
  updateUrls(newFileList)
  ElMessage.success('文件已移除')
}

// 处理文件预览
const handleFilePreview = (file) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 更新URLs
const updateUrls = (fileList) => {
  nextTick(() => {
    const urls = fileList
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    emit('update:urls', urls)
  })
}

// 监听fileList变化，更新URLs
watch(() => props.fileList, (newFileList) => {
  updateUrls(newFileList)
}, { immediate: true, deep: true })
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

.file-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #f5f7fa;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
  margin-right: 8px;
}

.file-size {
  color: #909399;
  font-size: 12px;
  flex-shrink: 0;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 