<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患整改"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="rectify-dialog"
  >
    <div class="rectify-content">
      <!-- 新增按钮 -->
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </div>

      <!-- 整改记录列表 -->
      <el-table :data="rectifyList" style="width: 100%" :header-cell-style="headerCellStyle">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="handleStatusName" label="整改状态" min-width="100" />
        <el-table-column prop="handleUserName" label="整改责任人" min-width="120" />
        <el-table-column prop="dealTime" label="整改时间" min-width="150" />
        <el-table-column prop="description" label="整改描述" min-width="200" />
        <el-table-column label="操作" fixed="right" min-width="150">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click="handleView(row)">查看</el-button>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>

    <!-- 整改方案新增/编辑弹窗 -->
    <RectifyFormDialog
      v-model:visible="formDialogVisible"
      :mode="formMode"
      :data="formData"
      :danger-id="dangerId"
      @success="handleFormSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getHiddenDangerHandleList, deleteHiddenDangerHandle } from '@/api/drainage';
import RectifyFormDialog from './RectifyFormDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表格数据
const rectifyList = ref([]);

// 表单弹窗相关
const formDialogVisible = ref(false);
const formMode = ref('add'); // 'add', 'edit', 'view'
const formData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 监听弹窗显示，获取数据
watch(() => props.visible, (newVal) => {
  if (newVal && props.dangerId) {
    fetchRectifyList();
  }
});

// 获取整改记录列表
const fetchRectifyList = async () => {
  try {
    const res = await getHiddenDangerHandleList(props.dangerId);
    if (res && res.code === 200) {
      rectifyList.value = res.data || [];
    }
  } catch (error) {
    console.error('获取整改记录失败:', error);
    ElMessage.error('获取整改记录失败');
  }
};

// 处理新增
const handleAdd = () => {
  formMode.value = 'add';
  formData.value = {};
  formDialogVisible.value = true;
};

// 处理查看
const handleView = (row) => {
  formMode.value = 'view';
  formData.value = { ...row };
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  formMode.value = 'edit';
  formData.value = { ...row };
  formDialogVisible.value = true;
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该整改记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteHiddenDangerHandle(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchRectifyList();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除整改记录失败:', error);
      ElMessage.error('删除整改记录失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理表单成功提交
const handleFormSuccess = () => {
  fetchRectifyList();
};

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
.rectify-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

.rectify-content {
  min-height: 400px;
}

.header-actions {
  margin-bottom: 16px;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}
</style> 