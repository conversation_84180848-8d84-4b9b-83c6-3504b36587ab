# 设备分组管理功能实现

## 功能概述

实现了完整的设备分组管理功能，包括增删改查、关联设备管理等操作。

## 实现的文件

### 1. API接口层 (`src/api/bridge.js`)

新增了以下API接口：

- `getDeviceGroupPage()` - 获取设备分组分页列表
- `getDeviceGroupDetail()` - 获取设备分组详情
- `saveDeviceGroup()` - 新增设备分组
- `updateDeviceGroup()` - 更新设备分组
- `deleteDeviceGroup()` - 删除设备分组
- `getDeviceGroupDeviceList()` - 获取分组设备列表
- `saveDeviceToGroup()` - 新增设备到分组
- `saveBatchDeviceToGroup()` - 批量新增设备到分组
- `deleteDeviceFromGroup()` - 删除分组中的设备
- `GROUP_TYPE_OPTIONS` - 分组类型常量

### 2. 弹窗组件 (`src/views/admin/bridge/device/management/components/DeviceGroupDialog.vue`)

实现了多模式的弹窗组件：

- **新增模式**：创建新的设备分组并关联设备
- **编辑模式**：修改分组信息，支持添加/删除关联设备
- **查看模式**：只读查看分组详情和关联设备

#### 主要功能

- 表单验证（分组名称、分组类型、所属桥梁为必填项）
- 设备类型下拉选择
- 设备名称多选（支持筛选）
- 关联设备列表展示
- 设备添加/删除操作

### 3. 主页面 (`src/views/admin/bridge/device/management/group.vue`)

实现了完整的列表管理功能：

- 条件筛选（所属桥梁、分组类型、分组名称）
- 分页查询
- 新增、编辑、详情查看、删除操作
- 关联设备数量展示

## 数据结构

### 设备分组实体

```javascript
{
  "id": "",
  "groupName": "",           // 分组名称
  "groupType": "",          // 分组类型
  "groupTypeName": "",      // 分组类型名称
  "bridgeId": "",           // 所属桥梁ID
  "groupDesc": "",          // 分组描述
  "createTime": "",         // 创建时间
  "updateTime": ""          // 更新时间
}
```

### 分组设备关联

```javascript
{
  "id": "",
  "deviceId": "",           // 设备ID
  "deviceName": "",         // 设备名称
  "deviceType": 0,          // 设备类型
  "deviceTypeName": "",     // 设备类型名称
  "groupId": ""             // 分组ID
}
```

## 常量配置

### 分组类型

```javascript
GROUP_TYPE_OPTIONS = [
  { label: '同截面设备分组', value: '4020101' },
  { label: '静挠设备分组', value: '4020102' }
]
```

## 使用说明

1. **查询功能**：通过所属桥梁、分组类型、分组名称进行筛选查询
2. **新增分组**：填写基本信息，选择设备类型和设备进行关联
3. **编辑分组**：修改分组信息，支持添加/删除关联设备
4. **删除分组**：删除分组及其所有关联关系
5. **查看详情**：只读模式查看分组信息和关联设备

## 技术特点

- 使用 Vue 3 Composition API
- Element Plus UI 组件库
- 响应式布局设计
- 完整的错误处理机制
- 统一的代码风格
- 支持分页和搜索
- 优化的用户交互体验

## 注意事项

1. 时间字段使用 moment.js 格式化为 `YYYY-MM-DD HH:mm:ss` 格式
2. 编辑模式下删除设备会立即调用删除接口
3. 新增设备时会先保存分组，再批量关联设备
4. 支持设备名称的模糊搜索过滤
5. 已关联的设备在选择时会被禁用

## 扩展功能

可以考虑后续添加：

- 导入/导出功能
- 批量操作
- 设备详情弹窗
- 分组复制功能
- 操作日志记录 