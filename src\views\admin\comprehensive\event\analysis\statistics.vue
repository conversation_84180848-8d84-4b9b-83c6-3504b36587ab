<template>
  <div class="comprehensive-page-container">
    <div class="bg-white p-6 rounded-lg shadow" style="height: calc(100vh - 160px); overflow-y: auto;">
      <!-- 顶部筛选区域 -->
      <div class="filter-section mb-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800">应急事件统计分析</h3>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">日期:</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
              class="w-64"
            />
            <div class="flex space-x-2">
              <el-button
                :type="activeTimeRange === 'recent7' ? 'primary' : 'default'"
                size="small"
                @click="setTimeRange('recent7')"
              >
                近7日
              </el-button>
              <el-button
                :type="activeTimeRange === 'recent30' ? 'primary' : 'default'"
                size="small"
                @click="setTimeRange('recent30')"
              >
                最近30天
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片区域 -->
      <div class="statistics-cards mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 事件总数 -->
          <div class="stat-card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium mb-2">事件总数</h4>
                <p class="text-3xl font-bold">{{ statusStats.totalCount || 0 }}</p>
                <div class="flex items-center mt-2">
                  <span class="text-xs">同比</span>
                  <el-icon class="ml-1" :class="getTrendIcon(statusStats.yoyAnalysisTrend)">
                    <component :is="getTrendIcon(statusStats.yoyAnalysisTrend)" />
                  </el-icon>
                  <span class="text-xs ml-1">{{ statusStats.yoyAnalysis || '0%' }}</span>
                </div>
              </div>
              <div class="text-4xl opacity-80">
                <el-icon><Warning /></el-icon>
              </div>
            </div>
          </div>

          <!-- 已处理 -->
          <div class="stat-card bg-gradient-to-r from-green-500 to-green-600 text-white">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium mb-2">已处理</h4>
                <p class="text-3xl font-bold">{{ statusStats.handled || 0 }}</p>
                <div class="flex items-center mt-2">
                  <span class="text-xs">同比</span>
                  <el-icon class="ml-1">
                    <ArrowDown />
                  </el-icon>
                  <span class="text-xs ml-1">10%</span>
                </div>
              </div>
              <div class="text-4xl opacity-80">
                <el-icon><Select /></el-icon>
              </div>
            </div>
          </div>

          <!-- 未处理 -->
          <div class="stat-card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium mb-2">未处理</h4>
                <p class="text-3xl font-bold">{{ statusStats.pendingHandle || 0 }}</p>
                <div class="flex items-center mt-2">
                  <span class="text-xs">同比</span>
                  <el-icon class="ml-1">
                    <ArrowUp />
                  </el-icon>
                  <span class="text-xs ml-1">10%</span>
                </div>
              </div>
              <div class="text-4xl opacity-80">
                <el-icon><Clock /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件等级统计 -->
      <div class="level-stats mb-6">
        <h4 class="text-md font-semibold text-gray-800 mb-4">事件等级统计</h4>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="level-card">
            <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg border border-red-200">
              <div>
                <h5 class="text-sm text-red-600 mb-1">特别重大</h5>
                <p class="text-2xl font-bold text-red-700">{{ levelStats.catastrophicCount || 0 }}</p>
                <div class="flex items-center mt-1">
                  <span class="text-xs text-red-500">同比</span>
                  <el-icon class="ml-1 text-red-500" :class="getTrendIcon(levelStats.catastrophicYoyAnalysisTrend)">
                    <component :is="getTrendIcon(levelStats.catastrophicYoyAnalysisTrend)" />
                  </el-icon>
                  <span class="text-xs text-red-500 ml-1">{{ levelStats.catastrophicYoyAnalysis || '0%' }}</span>
                </div>
              </div>
              <div class="text-2xl text-red-400">
                <el-icon><CircleCloseFilled /></el-icon>
              </div>
            </div>
          </div>

          <div class="level-card">
            <div class="flex items-center justify-between p-4 bg-orange-50 rounded-lg border border-orange-200">
              <div>
                <h5 class="text-sm text-orange-600 mb-1">重大</h5>
                <p class="text-2xl font-bold text-orange-700">{{ levelStats.criticalCount || 0 }}</p>
                <div class="flex items-center mt-1">
                  <span class="text-xs text-orange-500">同比</span>
                  <el-icon class="ml-1 text-orange-500" :class="getTrendIcon(levelStats.criticalCountYoyAnalysisTrend)">
                    <component :is="getTrendIcon(levelStats.criticalCountYoyAnalysisTrend)" />
                  </el-icon>
                  <span class="text-xs text-orange-500 ml-1">{{ levelStats.criticalCountYoyAnalysis || '0%' }}</span>
                </div>
              </div>
              <div class="text-2xl text-orange-400">
                <el-icon><WarningFilled /></el-icon>
              </div>
            </div>
          </div>

          <div class="level-card">
            <div class="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div>
                <h5 class="text-sm text-yellow-600 mb-1">较大</h5>
                <p class="text-2xl font-bold text-yellow-700">{{ levelStats.seriousCount || 0 }}</p>
                <div class="flex items-center mt-1">
                  <span class="text-xs text-yellow-500">同比</span>
                  <el-icon class="ml-1 text-yellow-500" :class="getTrendIcon(levelStats.seriousYoyAnalysisTrend)">
                    <component :is="getTrendIcon(levelStats.seriousYoyAnalysisTrend)" />
                  </el-icon>
                  <span class="text-xs text-yellow-500 ml-1">{{ levelStats.seriousYoyAnalysis || '0%' }}</span>
                </div>
              </div>
              <div class="text-2xl text-yellow-400">
                <el-icon><Warning /></el-icon>
              </div>
            </div>
          </div>

          <div class="level-card">
            <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div>
                <h5 class="text-sm text-blue-600 mb-1">一般</h5>
                <p class="text-2xl font-bold text-blue-700">{{ levelStats.minorCount || 0 }}</p>
                <div class="flex items-center mt-1">
                  <span class="text-xs text-blue-500">同比</span>
                  <el-icon class="ml-1 text-blue-500" :class="getTrendIcon(levelStats.minorYoyAnalysisTrend)">
                    <component :is="getTrendIcon(levelStats.minorYoyAnalysisTrend)" />
                  </el-icon>
                  <span class="text-xs text-blue-500 ml-1">{{ levelStats.minorYoyAnalysis || '0%' }}</span>
                </div>
              </div>
              <div class="text-2xl text-blue-400">
                <el-icon><InfoFilled /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 所属行业统计 -->
          <div class="chart-container">
            <h4 class="text-md font-semibold text-gray-800 mb-4">所属行业统计</h4>
            <div class="bg-white p-4 rounded-lg border" style="height: 350px;">
              <div ref="businessChart" style="width: 100%; height: 100%;"></div>
            </div>
          </div>

          <!-- 事件趋势分析 -->
          <div class="chart-container">
            <h4 class="text-md font-semibold text-gray-800 mb-4">事件趋势分析</h4>
            <div class="bg-white p-4 rounded-lg border" style="height: 350px;">
              <div ref="trendChart" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import moment from 'moment'
import * as echarts from 'echarts'
import { 
  Warning, 
  Select, 
  Clock, 
  ArrowUp, 
  ArrowDown, 
  CircleCloseFilled, 
  WarningFilled, 
  InfoFilled 
} from '@element-plus/icons-vue'
import { 
  getEmergencyEventStatusStatistics,
  getEmergencyEventLevelStatistics,
  getEmergencyEventRelatedBusinessStatistics,
  getEmergencyEventTrendStatistics
} from '@/api/comprehensive'
import { ElMessage } from 'element-plus'

// 响应式数据
const dateRange = ref([])
const activeTimeRange = ref('recent7')
const loading = ref(false)

// 统计数据
const statusStats = ref({})
const levelStats = ref({})
const businessStats = ref([])
const trendStats = ref([])

// 图表实例
const businessChart = ref(null)
const trendChart = ref(null)
let businessChartInstance = null
let trendChartInstance = null

// 初始化日期范围
const initDateRange = () => {
  const end = moment()
  const start = moment().subtract(6, 'days')
  dateRange.value = [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
}

// 设置时间范围
const setTimeRange = (type) => {
  activeTimeRange.value = type
  const end = moment()
  let start
  
  if (type === 'recent7') {
    start = moment().subtract(6, 'days')
  } else if (type === 'recent30') {
    start = moment().subtract(29, 'days')
  }
  
  dateRange.value = [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
  loadAllStatistics()
}

// 日期范围变化处理
const handleDateRangeChange = (value) => {
  if (value && value.length === 2) {
    activeTimeRange.value = ''
    loadAllStatistics()
  }
}

// 获取趋势图标
const getTrendIcon = (trend) => {
  return trend === 'up' ? 'ArrowUp' : 'ArrowDown'
}

// 获取查询参数
const getQueryParams = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return {}
  }
  
  return {
    startTime: dateRange.value[0] + ' 00:00:00',
    endTime: dateRange.value[1] + ' 23:59:59'
  }
}

// 加载事件状态统计
const loadStatusStatistics = async () => {
  try {
    const params = getQueryParams()
    const { data } = await getEmergencyEventStatusStatistics(params)
    statusStats.value = data || {}
  } catch (error) {
    console.error('获取事件状态统计失败:', error)
    ElMessage.error('获取事件状态统计失败')
  }
}

// 加载事件等级统计
const loadLevelStatistics = async () => {
  try {
    const params = getQueryParams()
    const { data } = await getEmergencyEventLevelStatistics(params)
    levelStats.value = data || {}
  } catch (error) {
    console.error('获取事件等级统计失败:', error)
    ElMessage.error('获取事件等级统计失败')
  }
}

// 加载所属行业统计
const loadBusinessStatistics = async () => {
  try {
    const params = getQueryParams()
    const { data } = await getEmergencyEventRelatedBusinessStatistics(params)
    businessStats.value = data || []
    await nextTick()
    initBusinessChart()
  } catch (error) {
    console.error('获取所属行业统计失败:', error)
    ElMessage.error('获取所属行业统计失败')
  }
}

// 加载事件趋势统计
const loadTrendStatistics = async () => {
  try {
    const params = getQueryParams()
    const { data } = await getEmergencyEventTrendStatistics(params)
    trendStats.value = data || []
    await nextTick()
    initTrendChart()
  } catch (error) {
    console.error('获取事件趋势统计失败:', error)
    ElMessage.error('获取事件趋势统计失败')
  }
}

// 加载所有统计数据
const loadAllStatistics = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStatusStatistics(),
      loadLevelStatistics(), 
      loadBusinessStatistics(),
      loadTrendStatistics()
    ])
  } finally {
    loading.value = false
  }
}

// 初始化所属行业图表
const initBusinessChart = () => {
  if (!businessChart.value || !businessStats.value.length) return
  
  if (businessChartInstance) {
    businessChartInstance.dispose()
  }
  
  businessChartInstance = echarts.init(businessChart.value)
  
  const categories = businessStats.value.map(item => item.relatedBusinessName)
  const totalData = businessStats.value.map(item => item.totalCount)
  const handledData = businessStats.value.map(item => item.handled)
  const rateData = businessStats.value.map(item => (item.handledRate * 100).toFixed(1))
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['总数', '已处理', '处理率'],
      top: 10
    },
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '事件数量',
        min: 0,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '处理率',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总数',
        type: 'bar',
        data: totalData,
        itemStyle: {
          color: '#5470C6'
        }
      },
      {
        name: '已处理',
        type: 'bar',
        data: handledData,
        itemStyle: {
          color: '#91CC75'
        }
      },
      {
        name: '处理率',
        type: 'line',
        yAxisIndex: 1,
        data: rateData,
        itemStyle: {
          color: '#EE6666'
        },
        label: {
          show: true,
          formatter: '{c}%'
        }
      }
    ]
  }
  
  businessChartInstance.setOption(option)
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChart.value || !trendStats.value.length) return
  
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  
  trendChartInstance = echarts.init(trendChart.value)
  
  const dates = trendStats.value.map(item => moment(item.date).format('MM-DD'))
  const counts = trendStats.value.map(item => item.count)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}件`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '事件数量',
      minInterval: 1
    },
    series: [
      {
        name: '事件数量',
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: {
          opacity: 0.3
        },
        itemStyle: {
          color: '#5470C6'
        },
        data: counts
      }
    ]
  }
  
  trendChartInstance.setOption(option)
}

// 窗口大小变化处理
const handleResize = () => {
  if (businessChartInstance) {
    businessChartInstance.resize()
  }
  if (trendChartInstance) {
    trendChartInstance.resize()
  }
}

// 组件挂载
onMounted(() => {
  initDateRange()
  loadAllStatistics()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onBeforeUnmount(() => {
  if (businessChartInstance) {
    businessChartInstance.dispose()
  }
  if (trendChartInstance) {
    trendChartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.comprehensive-page-container {
  padding: 20px;
}

.filter-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 16px;
}

.stat-card {
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.level-card {
  transition: all 0.3s ease;
}

.level-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .comprehensive-page-container {
    padding: 10px;
  }
  
  .filter-section .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .statistics-cards .grid {
    grid-template-columns: 1fr;
  }
  
  .level-stats .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-section .grid {
    grid-template-columns: 1fr;
  }
}

@media (max-height: 910px) {
  .bg-white.p-6.rounded-lg.shadow {
    height: calc(100vh - 140px) !important;
  }
}

@media (max-height: 1080px) {
  .bg-white.p-6.rounded-lg.shadow {
    height: calc(100vh - 160px) !important;
  }
}
</style>