<template>
  <div class="emergency-shelter-container">
    <!-- 搜索区域 -->
    <div class="emergency-shelter-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">场所名称:</span>
          <el-input v-model="formData.shelterName" class="form-input" placeholder="请输入场所名称" />
        </div>
        <div class="form-item">
          <span class="label">当前状态:</span>
          <el-select v-model="formData.shelterStatus" class="form-input" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option v-for="item in shelterStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" height="100%" empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="shelterName" label="应急避难场所名称" min-width="160" />
        <el-table-column prop="longitude" label="经度" min-width="100" />
        <el-table-column prop="latitude" label="纬度" min-width="100" />
        <el-table-column prop="shelterCapacity" label="所能容纳人数" min-width="120" />
        <el-table-column prop="shelterStatusName" label="当前状态" min-width="100" />
        <el-table-column prop="contactUser" label="联系人" min-width="100" />
        <el-table-column prop="contactInfo" label="联系电话" min-width="120" />
        <el-table-column prop="dutyPhone" label="值班电话" min-width="120" />
        <el-table-column prop="address" label="详细地址" min-width="200" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <EmergencyShelterDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getEmergencyShelterPage, 
  deleteEmergencyShelter, 
  getEmergencyShelterDetail
} from '@/api/comprehensive';
import { EMERGENCY_SHELTER_STATUS_OPTIONS } from '@/constants/comprehensive';
import { misPosition } from '@/hooks/gishooks';
import EmergencyShelterDialog from './EmergencyShelterDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const shelterStatusOptions = ref(EMERGENCY_SHELTER_STATUS_OPTIONS);

// 表单数据
const formData = ref({
  shelterName: '',
  shelterStatus: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add');
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchShelterData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    shelterName: '',
    shelterStatus: ''
  };
  currentPage.value = 1;
  fetchShelterData();
};

// 获取避难场所分页数据
const fetchShelterData = async () => {
  try {
    const params = {
      shelterName: formData.value.shelterName,
      shelterStatus: formData.value.shelterStatus
    };
    
    const res = await getEmergencyShelterPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取避难场所数据失败:', error);
    ElMessage.error('获取避难场所数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 其他处理函数（新增、编辑、删除、定位等）实现类似救援队伍组件
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchShelterData();
};

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchShelterData();
};

const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

const handleEdit = async (row) => {
  try {
    const res = await getEmergencyShelterDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取避难场所详情失败');
  }
};

const handleDetail = async (row) => {
  try {
    const res = await getEmergencyShelterDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    }
  } catch (error) {
    ElMessage.error('获取避难场所详情失败');
  }
};

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该避难场所信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencyShelter(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchShelterData();
      }
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

const handleLocation = (row) => {
  if (row.latitude && row.longitude) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

const handleDialogSuccess = () => {
  fetchShelterData();
};

onMounted(() => {
  fetchShelterData();
});
</script>

<style scoped>
/* 样式复用救援队伍组件的样式 */
.emergency-shelter-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

.emergency-shelter-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

.search-btn, .reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

.table-container {
  flex: 1;
  overflow: hidden;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 