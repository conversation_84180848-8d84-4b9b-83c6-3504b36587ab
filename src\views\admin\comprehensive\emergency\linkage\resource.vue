<template>
  <div class="emergency-resource-container">
    <el-tabs v-model="activeTab" class="emergency-resource-tabs">
      <el-tab-pane label="救援队伍" name="team">
        <EmergencyTeam />
      </el-tab-pane>
      <el-tab-pane label="应急物资" name="supplies">
        <EmergencySupplies />
      </el-tab-pane>
      <el-tab-pane label="避难场所" name="shelter">
        <EmergencyShelter />
      </el-tab-pane>
      <el-tab-pane label="救援人员" name="responders">
        <EmergencyResponders />
      </el-tab-pane>
      <el-tab-pane label="医疗机构" name="hospital">
        <EmergencyHospital />
      </el-tab-pane>
      <el-tab-pane label="应急仓库" name="store">
        <EmergencyStore />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import EmergencyTeam from './components/EmergencyTeam.vue'
import EmergencySupplies from './components/EmergencySupplies.vue'
import EmergencyShelter from './components/EmergencyShelter.vue'
import EmergencyResponders from './components/EmergencyResponders.vue'
import EmergencyHospital from './components/EmergencyHospital.vue'
import EmergencyStore from './components/EmergencyStore.vue'

// 当前激活的选项卡
const activeTab = ref('team')

onMounted(() => {
  console.log('应急资源管理组件已挂载')
})
</script>

<style scoped>
.emergency-resource-container {
  padding: 0;
}

.emergency-resource-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}
</style>