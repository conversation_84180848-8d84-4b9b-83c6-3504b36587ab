<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="duty-schedule-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="值班日期" prop="scheduleTime">
            <el-date-picker
              v-model="formData.scheduleTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择值班日期"
              class="w-full"
              @change="handleDateChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班次" prop="shiftId">
            <el-select v-model="formData.shiftId" placeholder="请选择班次" class="w-full" @change="handleShiftChange">
              <el-option v-for="item in shiftOptions" :key="item.id" :label="item.shiftName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="值班人员" prop="userId">
            <el-select v-model="formData.userId" placeholder="请选择值班人员" class="w-full" @change="handleUserChange">
              <el-option v-for="item in userOptions" :key="item.id" :label="item.userName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveDutySchedule,
  updateDutySchedule,
  getDutyShiftList,
  getDutyUserPage
} from '@/api/comprehensive';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增排班',
    edit: '编辑排班',
    view: '排班详情'
  };
  return titles[props.mode] || '排班信息';
});

// 下拉选项数据
const shiftOptions = ref([]);
const userOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  scheduleTime: '',
  shiftId: '',
  shiftName: '',
  userId: '',
  userName: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  scheduleTime: [{ required: true, message: '请选择值班日期', trigger: 'change' }],
  shiftId: [{ required: true, message: '请选择班次', trigger: 'change' }],
  userId: [{ required: true, message: '请选择值班人员', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 处理日期变化
const handleDateChange = (value) => {
  if (value) {
    // 重新获取班次列表
    fetchShiftList(value);
  } else {
    // 清空班次选择
    formData.shiftId = '';
    formData.shiftName = '';
    fetchShiftList();
  }
};

// 处理班次变化
const handleShiftChange = (value) => {
  const selected = shiftOptions.value.find(item => item.id === value);
  if (selected) {
    formData.shiftName = selected.shiftName;
  }
};

// 处理用户变化
const handleUserChange = (value) => {
  const selected = userOptions.value.find(item => item.id === value);
  if (selected) {
    formData.userName = selected.userName;
  }
};

// 获取班次列表
const fetchShiftList = async (scheduleTime = '') => {
  try {
    let params = {};
    if (scheduleTime) {
      // 如果选择了日期，查询该日期的班次
      params = {
        startTime: scheduleTime + ' 00:00:00',
        endTime: scheduleTime + ' 23:59:59'
      };
    }
    
    const res = await getDutyShiftList(params);
    if (res && res.data) {
      shiftOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取班次列表失败', error);
    shiftOptions.value = [];
  }
};

// 获取值班人员列表
const fetchUserList = async () => {
  try {
    const res = await getDutyUserPage(1, 999, {});
    if (res && res.data && res.data.records) {
      userOptions.value = res.data.records;
    }
  } catch (error) {
    console.error('获取值班人员列表失败', error);
    userOptions.value = [];
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 如果有日期，重新获取班次列表
    if (formData.scheduleTime) {
      fetchShiftList(formData.scheduleTime);
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveDutySchedule(submitData);
    } else if (props.mode === 'edit') {
      res = await updateDutySchedule(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchShiftList();
  fetchUserList();
});
</script>

<style scoped>
.duty-schedule-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style>
