<template>
  <div class="duty-container">
    <el-tabs v-model="activeTab" class="duty-tabs">
      <el-tab-pane label="排班管理" name="schedule">
        <ScheduleManagement />
      </el-tab-pane>
      <el-tab-pane label="交接班记录" name="handover">
        <HandoverRecords />
      </el-tab-pane>
      <el-tab-pane label="值班统计" name="statistics">
        <DutyStatistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ScheduleManagement from './components/ScheduleManagement.vue'
import HandoverRecords from './components/HandoverRecords.vue'
import DutyStatistics from './components/DutyStatistics.vue'

// 当前激活的选项卡
const activeTab = ref('schedule')

onMounted(() => {
  console.log('值班管理组件已挂载')
})
</script>

<style scoped>
.duty-container {
  padding: 0;
}

.duty-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}
</style>