<template>
  <el-dialog
    v-model="dialogVisible"
    title="下发"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="issue-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="下发单位" prop="issuedUnit">
            <el-tree-select
              v-model="formData.issuedUnit"
              :data="deptOptions"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
              placeholder="请选择"
              check-strictly
              :render-after-expand="false"
              style="width: 100%"
              @change="handleDeptChange"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { issueSupervise } from '@/api/comprehensive'
import { getDeptTree } from '@/api/system'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 部门选项
const deptOptions = ref([])

// 表单数据
const formData = reactive({
  superviseId: '',
  issuedUnit: '',
  issuedUnitName: ''
})

// 表单验证规则
const formRules = {
  issuedUnit: [{ required: true, message: '请选择下发单位', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
}

// 监听props.data变化
watch(() => props.data, (newVal) => {
  if (newVal && newVal.id) {
    formData.superviseId = newVal.id
  } else {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理部门选择变化
const handleDeptChange = (value) => {
  const findDept = (depts, targetId) => {
    for (const dept of depts) {
      if (dept.id === targetId) {
        return dept
      }
      if (dept.children) {
        const found = findDept(dept.children, targetId)
        if (found) return found
      }
    }
    return null
  }

  const selected = findDept(deptOptions.value, value)
  if (selected) {
    formData.issuedUnitName = selected.deptName
  }
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      deptOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = {
      superviseId: formData.superviseId,
      issuedUnit: formData.issuedUnit,
      issuedUnitName: formData.issuedUnitName
    }

    const res = await issueSupervise(submitData)

    if (res && res.code === 200) {
      ElMessage.success('下发成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || '下发失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
})
</script>

<style scoped>
.issue-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}
</style> 