<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="emergency-supplies-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物资名称" prop="suppliesName">
            <el-input v-model="formData.suppliesName" placeholder="请输入物资名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格型号" prop="suppliesModel">
            <el-input v-model="formData.suppliesModel" placeholder="请输入规格型号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物资类型" prop="suppliesType">
            <el-select v-model="formData.suppliesType" placeholder="请选择物资类型" class="w-full" @change="handleSuppliesTypeChange">
              <el-option v-for="item in suppliesTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属仓库" prop="storeId">
            <el-select v-model="formData.storeId" placeholder="请选择所属仓库" class="w-full" @change="handleStoreChange">
              <el-option v-for="item in storeOptions" :key="item.id" :label="item.storeName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数量" prop="suppliesNumber">
            <el-input-number v-model="formData.suppliesNumber" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计量单位" prop="suppliesUnit">
            <el-input v-model="formData.suppliesUnit" placeholder="请输入单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="国标物资名称" prop="gbSuppliesName">
            <el-input v-model="formData.gbSuppliesName" placeholder="请输入国标物资名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国标物资代码" prop="gbSuppliesCode">
            <el-input v-model="formData.gbSuppliesCode" placeholder="请输入国标物资代码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="国标二级分类" prop="gbSubLevelTypeName">
            <el-input v-model="formData.gbSubLevelTypeName" placeholder="请输入国标二级分类" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二级分类代码" prop="gbSubLevelType">
            <el-input v-model="formData.gbSubLevelType" placeholder="请输入二级分类代码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="灾害类型" prop="diseaseType">
            <el-input v-model="formData.diseaseType" placeholder="请输入灾害类型" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属区域">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencySupplies,
  updateEmergencySupplies,
  getEmergencyStoreList
} from '@/api/comprehensive';
import { EMERGENCY_SUPPLIES_TYPE_OPTIONS } from '@/constants/comprehensive';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const suppliesTypeOptions = EMERGENCY_SUPPLIES_TYPE_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增应急物资装备',
    edit: '编辑应急物资装备',
    view: '应急物资装备详情'
  };
  return titles[props.mode] || '应急物资装备信息';
});

// 行政区划选项和仓库选项
const areaOptions = ref(AREA_OPTIONS);
const storeOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  suppliesName: '',
  suppliesModel: '',
  suppliesType: '',
  suppliesTypeName: '',
  suppliesNumber: 0,
  suppliesUnit: '',
  storeId: '',
  storeName: '',
  gbSuppliesName: '',
  gbSuppliesCode: '',
  gbSubLevelType: '',
  gbSubLevelTypeName: '',
  diseaseType: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  keyWord: ''
});

// 表单验证规则
const formRules = {
  suppliesName: [{ required: true, message: '请输入物资名称', trigger: 'blur' }],
  suppliesType: [{ required: true, message: '请选择物资类型', trigger: 'change' }],
  storeId: [{ required: true, message: '请选择所属仓库', trigger: 'change' }],
  suppliesNumber: [{ required: true, message: '请输入数量', trigger: 'blur' }],
  suppliesUnit: [{ required: true, message: '请输入单位', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'suppliesNumber') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 获取仓库列表
const fetchStoreList = async () => {
  try {
    const res = await getEmergencyStoreList();
    if (res && res.data) {
      storeOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取仓库列表失败:', error);
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理物资类型变化
const handleSuppliesTypeChange = (value) => {
  const selected = suppliesTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.suppliesTypeName = selected.label;
  }
};

// 处理仓库变化
const handleStoreChange = (value) => {
  const selected = storeOptions.value.find(item => item.id === value);
  if (selected) {
    formData.storeName = selected.storeName;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencySupplies(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencySupplies(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreList();
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.emergency-supplies-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 