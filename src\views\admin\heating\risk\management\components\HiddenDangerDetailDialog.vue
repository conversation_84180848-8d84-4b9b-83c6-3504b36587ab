<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患详情"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="detail-dialog"
  >
    <div class="detail-content">
      <!-- 隐患详情 -->
      <div class="info-section">
        <div class="section-title">
          <el-icon class="title-icon"><Document /></el-icon>
          隐患详情
        </div>
        
        <div class="detail-grid">
          <div class="detail-item">
            <span class="label">隐患来源：</span>
            <span class="value">{{ data.dangerSourceName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">隐患描述：</span>
            <span class="value">{{ data.dangerDesc || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">隐患类型：</span>
            <span class="value">{{ data.dangerTypeName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">隐患等级：</span>
            <span class="value">{{ data.dangerLevelName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">隐患对象：</span>
            <span class="value">{{ data.dangerTargetName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">整改期限：</span>
            <span class="value">{{ data.rectificationDeadline || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">上报时间：</span>
            <span class="value">{{ data.reportTime || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">上报人：</span>
            <span class="value">{{ data.reportUser || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">权属单位：</span>
            <span class="value">{{ data.ownershipUnitName || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="label">责任人：</span>
            <span class="value">{{ data.responsibleUserName || '-' }}</span>
          </div>
          <div class="detail-item full-width">
            <span class="label">位置坐标：</span>
            <span class="value">{{ data.longitude && data.latitude ? `${data.longitude}, ${data.latitude}` : '-' }}</span>
          </div>
          <div class="detail-item full-width">
            <span class="label">详细地址：</span>
            <span class="value">{{ data.address || '-' }}</span>
          </div>
        </div>

        <!-- 隐患图片 -->
        <div v-if="data.picUrls" class="image-section">
          <div class="section-subtitle">隐患图片</div>
          <div class="image-grid">
            <el-image
              v-for="(url, index) in imageUrls"
              :key="index"
              :src="url"
              fit="cover"
              class="detail-image"
              :preview-src-list="imageUrls"
              :initial-index="index"
            />
          </div>
        </div>
      </div>

      <!-- 整改情况 -->
      <div class="timeline-section">
        <div class="section-title">
          <el-icon class="title-icon"><Clock /></el-icon>
          整改情况
        </div>
        
        <el-timeline v-if="timelineData.length > 0">
          <el-timeline-item
            v-for="(item, index) in timelineData"
            :key="index"
            :timestamp="item.dealTime"
            placement="top"
            :type="getTimelineType(item.processStatus)"
          >
            <el-card class="timeline-card">
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="status-badge" :class="getStatusClass(item.processStatus)">
                    {{ item.processStatusName }}
                  </span>
                  <span class="handler">{{ item.handleUserName }}</span>
                </div>
                <div class="timeline-description">
                  {{ item.description }}
                </div>
                <div v-if="item.remark" class="timeline-remark">
                  备注：{{ item.remark }}
                </div>
                <!-- 图片显示 -->
                <div v-if="item.picUrls" class="timeline-images">
                  <el-image
                    v-for="(url, imgIndex) in item.picUrls.split(',')"
                    :key="imgIndex"
                    :src="url"
                    fit="cover"
                    class="timeline-image"
                    :preview-src-list="item.picUrls.split(',')"
                    :initial-index="imgIndex"
                  />
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <div v-else class="no-timeline">
          <el-empty description="暂无整改记录" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Document, Clock } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getHeatingHiddenDangerStatusList } from '@/api/heating';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 时间线数据
const timelineData = ref([]);

// 图片URL数组
const imageUrls = computed(() => {
  if (props.data.picUrls) {
    return props.data.picUrls.split(',');
  }
  return [];
});

// 监听数据变化
watch(() => props.data, (newVal) => {
  if (newVal && newVal.id) {
    fetchTimelineData(newVal.id);
  }
}, { immediate: true });

// 获取整改状态时间线数据
const fetchTimelineData = async (dangerId) => {
  try {
    const res = await getHeatingHiddenDangerStatusList(dangerId);
    if (res && res.code === 200) {
      timelineData.value = res.data || [];
    }
  } catch (error) {
    console.error('获取整改时间线失败:', error);
    ElMessage.error('获取整改时间线失败');
  }
};

// 获取时间线类型
const getTimelineType = (status) => {
  switch (status) {
    case 7003401: // 上报
      return 'primary';
    case 7003402: // 整改
      return 'warning';
    case 7003403: // 复核
      return 'success';
    default:
      return 'info';
  }
};

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 7003401: // 上报
      return 'status-report';
    case 7003402: // 整改
      return 'status-rectify';
    case 7003403: // 复核
      return 'status-review';
    default:
      return 'status-default';
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  timelineData.value = [];
};
</script>

<style scoped>
.detail-dialog {
  font-family: PingFangSC, PingFang SC;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-section,
.timeline-section {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #0277FD;
}

.title-icon {
  margin-right: 8px;
  color: #0277FD;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 16px 0 12px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.label {
  min-width: 100px;
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  word-break: break-word;
}

.image-section {
  margin-top: 20px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.detail-image {
  width: 120px;
  height: 120px;
  border-radius: 4px;
  cursor: pointer;
}

.timeline-card {
  margin-bottom: 8px;
}

.timeline-content {
  padding: 4px 0;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-report {
  background: #E3F2FD;
  color: #1976D2;
}

.status-rectify {
  background: #FFF3E0;
  color: #F57C00;
}

.status-review {
  background: #E8F5E8;
  color: #388E3C;
}

.status-default {
  background: #F5F5F5;
  color: #666;
}

.handler {
  color: #666;
  font-size: 14px;
}

.timeline-description {
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.timeline-remark {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.timeline-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.no-timeline {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 12px;
}

:deep(.el-card__body) {
  padding: 12px;
}
</style> 