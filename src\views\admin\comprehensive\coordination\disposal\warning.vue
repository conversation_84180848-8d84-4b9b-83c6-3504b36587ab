<template>
  <div class="warning-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="statistics-cards">
        <!-- 预警状态统计 -->
        <div class="stat-card total-card">
          <div class="stat-number">{{ statusStatistics.totalCount || 0 }}</div>
          <div class="stat-label">全部预警</div>
        </div>
        <div class="stat-card pending-card">
          <div class="stat-number">{{ statusStatistics.pendingHandle || 0 }}</div>
          <div class="stat-label">待处置</div>
          <div class="stat-rate">占比: {{ statusStatistics.pendingHandleRate || '0%' }}</div>
        </div>
        <div class="stat-card handling-card">
          <div class="stat-number">{{ statusStatistics.handling || 0 }}</div>
          <div class="stat-label">处置中</div>
          <div class="stat-rate">占比: {{ statusStatistics.handlingRate || '0%' }}</div>
        </div>
        <div class="stat-card handled-card">
          <div class="stat-number">{{ statusStatistics.handled || 0 }}</div>
          <div class="stat-label">已处置</div>
          <div class="stat-rate">占比: {{ statusStatistics.handledRate || '0%' }}</div>
        </div>
        <div class="stat-card released-card">
          <div class="stat-number">{{ statusStatistics.released || 0 }}</div>
          <div class="stat-label">已解除</div>
          <div class="stat-rate">占比: {{ statusStatistics.releasedRate || '0%' }}</div>
        </div>

        <!-- 预警等级统计 -->
        <div class="stat-card level1-card">
          <div class="stat-number">{{ levelStatistics.level1Count || 0 }}</div>
          <div class="stat-label">一级预警</div>
          <div class="stat-rate">占比: {{ levelStatistics.level1Rate || '0%' }}</div>
        </div>
        <div class="stat-card level2-card">
          <div class="stat-number">{{ levelStatistics.level2Count || 0 }}</div>
          <div class="stat-label">二级预警</div>
          <div class="stat-rate">占比: {{ levelStatistics.level2Rate || '0%' }}</div>
        </div>
        <div class="stat-card level3-card">
          <div class="stat-number">{{ levelStatistics.level3Count || 0 }}</div>
          <div class="stat-label">三级预警</div>
          <div class="stat-rate">占比: {{ levelStatistics.level3Rate || '0%' }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属行业:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预警类型:</span>
          <el-select v-model="formData.warningType" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预警时间:</span>
          <el-date-picker
            v-model="formData.warningTime"
            type="daterange"
            class="form-input"
            placeholder="请选择时间范围"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </div>
        <div class="form-item">
          <span class="label">预警状态:</span>
          <el-select v-model="formData.warningStatus" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in warningStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.warningTitle" class="form-input" placeholder="输入预警标题" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增预警</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="relatedBusinessName" label="所属行业" min-width="80" />
        <el-table-column prop="warningCode" label="预警编号" min-width="120" />
        <el-table-column prop="warningTitle" label="预警标题" min-width="150" />
        <el-table-column prop="warningTypeName" label="预警类型" min-width="120" />
        <el-table-column prop="warningDesc" label="预警描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="warningTime" label="预警时间" min-width="120" />
        <el-table-column prop="publishUnitName" label="发布单位" min-width="120" />
        <el-table-column label="预警级别" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="getWarningLevelType(row.warningLevel)"
              size="small"
            >
              {{ row.warningLevelName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预警状态" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="getWarningStatusType(row.warningStatus)"
              size="small"
            >
              {{ row.warningStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="180">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <WarningDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 详情对话框 -->
    <WarningDetailDialog
      v-model:visible="detailDialogVisible"
      :data="detailDialogData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag } from 'element-plus'
import {
  getWarningInfoPage,
  deleteWarningInfo,
  getWarningInfoDetail,
  getWarningStatusStatistics,
  getWarningLevelStatistics,
  WARNING_LEVEL_OPTIONS,
  WARNING_STATUS_OPTIONS,
  WARNING_TYPE_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/api/comprehensive'
import { misPosition } from '@/hooks/gishooks'
import WarningDialog from './components/WarningDialog.vue'
import WarningDetailDialog from './components/WarningDetailDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 统计数据
const statusStatistics = ref({})
const levelStatistics = ref({})

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const warningTypeOptions = ref(WARNING_TYPE_OPTIONS)
const warningStatusOptions = ref(WARNING_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  relatedBusiness: '',
  warningType: '',
  warningTime: [],
  warningStatus: '',
  warningTitle: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 详情对话框相关
const detailDialogVisible = ref(false)
const detailDialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取预警级别标签类型
const getWarningLevelType = (level) => {
  const typeMap = {
    7002201: 'danger',  // 一级预警 - 红色
    7002202: 'warning', // 二级预警 - 橙色
    7002203: 'success'  // 三级预警 - 绿色
  }
  return typeMap[level] || ''
}

// 获取预警状态标签类型
const getWarningStatusType = (status) => {
  const typeMap = {
    7002301: 'danger',  // 待处置
    7002302: 'warning', // 处置中
    7002303: 'success', // 已处置
    7002304: 'info'     // 已解除
  }
  return typeMap[status] || ''
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchWarningData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    warningType: '',
    warningTime: [],
    warningStatus: '',
    warningTitle: ''
  }
  currentPage.value = 1
  fetchWarningData()
}

// 获取预警分页数据
const fetchWarningData = async () => {
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      warningType: formData.value.warningType,
      warningStatus: formData.value.warningStatus,
      warningTitle: formData.value.warningTitle
    }
    
    // 处理时间范围
    if (formData.value.warningTime && formData.value.warningTime.length === 2) {
      params.startTime = formData.value.warningTime[0] + ' 00:00:00'
      params.endTime = formData.value.warningTime[1] + ' 23:59:59'
    }
    
    const res = await getWarningInfoPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
    ElMessage.error('获取预警数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 获取预警状态统计
const fetchStatusStatistics = async () => {
  try {
    const res = await getWarningStatusStatistics()
    if (res && res.code === 200) {
      statusStatistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取预警状态统计失败:', error)
  }
}

// 获取预警等级统计
const fetchLevelStatistics = async () => {
  try {
    const res = await getWarningLevelStatistics()
    if (res && res.code === 200) {
      levelStatistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取预警等级统计失败:', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchWarningData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchWarningData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getWarningInfoDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取预警详情失败')
    }
  } catch (error) {
    console.error('获取预警详情失败:', error)
    ElMessage.error('获取预警详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getWarningInfoDetail(row.id)
    if (res && res.code === 200) {
      detailDialogData.value = res.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error('获取预警详情失败')
    }
  } catch (error) {
    console.error('获取预警详情失败:', error)
    ElMessage.error('获取预警详情失败')
  }
}

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchWarningData()
  fetchStatusStatistics()
  fetchLevelStatistics()
}

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatusStatistics(),
      fetchLevelStatistics(),
      fetchWarningData()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})
</script>

<style scoped>
.warning-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  width: 100%;
  margin-bottom: 16px;
}

.statistics-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-card {
  flex: 1;
  min-width: 120px;
  height: 80px;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  margin-bottom: 2px;
}

.stat-rate {
  font-size: 12px;
  opacity: 0.9;
}

.total-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.handling-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.handled-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.released-card {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.level1-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.level2-card {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.level3-card {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .statistics-cards {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item {
    margin-right: 0;
    justify-content: space-between;
  }
  
  .form-input {
    width: 200px;
  }
}
</style>