<template>
  <el-dialog
    v-model="dialogVisible"
    title="事项反馈"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="feedback-dialog"
  >
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
      </div>
    </div>

    <!-- 处置列表 -->
    <div class="table-container">
      <el-table :data="handleList" style="width: 100%" :header-cell-style="headerCellStyle">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="handleStatusName" label="处理进度" min-width="120" />
        <el-table-column prop="description" label="处理描述" min-width="200" />
        <el-table-column prop="dealTime" label="处理时间" min-width="120" />
        <el-table-column label="操作" fixed="right" min-width="120">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click="handleView(row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>

    <!-- 处置新增/编辑弹窗 -->
    <HandleDialog
      v-model:visible="handleDialogVisible"
      :mode="handleDialogMode"
      :data="handleDialogData"
      :supervise-id="superviseId"
      @success="handleDialogSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getSuperviseHandleList } from '@/api/comprehensive'
import HandleDialog from './HandleDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 督查督办ID
const superviseId = ref('')

// 处置列表数据
const handleList = ref([])

// 处置弹窗相关
const handleDialogVisible = ref(false)
const handleDialogMode = ref('add')
const handleDialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 监听props.data变化
watch(() => props.data, (newVal) => {
  if (newVal && newVal.id) {
    superviseId.value = newVal.id
    fetchHandleList()
  }
}, { immediate: true, deep: true })

// 获取处置列表
const fetchHandleList = async () => {
  if (!superviseId.value) return

  try {
    const res = await getSuperviseHandleList(superviseId.value)
    if (res && res.code === 200) {
      handleList.value = res.data || []
    }
  } catch (error) {
    console.error('获取处置列表失败:', error)
    ElMessage.error('获取处置列表失败')
  }
}

// 处理新增
const handleAdd = () => {
  handleDialogMode.value = 'add'
  handleDialogData.value = {}
  handleDialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  handleDialogMode.value = 'edit'
  handleDialogData.value = { ...row }
  handleDialogVisible.value = true
}

// 处理查看
const handleView = (row) => {
  handleDialogMode.value = 'view'
  handleDialogData.value = { ...row }
  handleDialogVisible.value = true
}

// 处理弹窗成功提交
const handleDialogSuccess = () => {
  fetchHandleList()
  emit('success')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  superviseId.value = ''
  handleList.value = []
}
</script>

<style scoped>
.feedback-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  margin-bottom: 16px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}
</style> 