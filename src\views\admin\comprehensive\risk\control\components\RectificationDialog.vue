<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="rectification-dialog"
  >
    <div class="rectification-content">
      <!-- 整改列表 -->
      <div class="rectification-list">
        <div class="list-header">
          <span class="list-title">整改记录</span>
          <el-button type="primary" @click="handleAddRectification">新增整改</el-button>
        </div>
        
        <div class="list-content">
          <el-table :data="rectificationList" style="width: 100%">
            <el-table-column prop="handleStatusName" label="整改状态" width="100" />
            <el-table-column prop="handleUserName" label="整改责任人" width="120" />
            <el-table-column prop="description" label="整改描述" min-width="200" show-overflow-tooltip />
            <el-table-column prop="dealTime" label="整改时间" width="160" />
            <el-table-column label="是否按期" width="80">
              <template #default="{ row }">
                <el-tag :type="row.onSchedule ? 'success' : 'danger'">
                  {{ row.onSchedule ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEditRectification(row)">编辑</el-button>
                <el-button type="danger" link @click="handleDeleteRectification(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 整改表单弹窗 -->
    <RectificationFormDialog
      v-model:visible="formDialogVisible"
      :mode="formMode"
      :data="formData"
      :danger-id="dangerId"
      @success="handleFormSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getHiddenDangerHandleList, deleteHiddenDangerHandle } from '@/api/comprehensive'
import RectificationFormDialog from './RectificationFormDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => '隐患整改管理')

// 整改列表数据
const rectificationList = ref([])

// 表单弹窗相关
const formDialogVisible = ref(false)
const formMode = ref('add') // 'add', 'edit'
const formData = ref({})

// 获取整改列表
const fetchRectificationList = async () => {
  if (!props.dangerId) return
  
  try {
    const res = await getHiddenDangerHandleList(props.dangerId)
    if (res && res.code === 200) {
      rectificationList.value = res.data || []
    }
  } catch (error) {
    console.error('获取整改列表失败:', error)
    ElMessage.error('获取整改列表失败')
  }
}

// 新增整改
const handleAddRectification = () => {
  formMode.value = 'add'
  formData.value = {}
  formDialogVisible.value = true
}

// 编辑整改
const handleEditRectification = (row) => {
  formMode.value = 'edit'
  formData.value = { ...row }
  formDialogVisible.value = true
}

// 删除整改
const handleDeleteRectification = (row) => {
  ElMessageBox.confirm('确定要删除该整改记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteHiddenDangerHandle(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchRectificationList()
        emit('success')
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除整改记录失败:', error)
      ElMessage.error('删除整改记录失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 表单操作成功回调
const handleFormSuccess = () => {
  fetchRectificationList()
  emit('success')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 监听dangerId变化，重新获取数据
watch(() => props.dangerId, (newVal) => {
  if (newVal && props.visible) {
    fetchRectificationList()
  }
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.dangerId) {
    fetchRectificationList()
  }
})
</script>

<style scoped>
.rectification-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.rectification-content {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.rectification-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.list-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.list-content {
  flex: 1;
  overflow-y: auto;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}
</style> 