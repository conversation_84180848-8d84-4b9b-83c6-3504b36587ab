<template>
  <div class="pipeline-node-container">
    <!-- 搜索区域 -->
    <div class="pipeline-node-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">管点类型:</span>
          <el-select v-model="formData.pointType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in pointTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.managementUnit" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.pointCode" class="form-input" placeholder="请输入管点编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleImport">导入</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="pointCode" label="管点编码" min-width="120" />
        <el-table-column prop="pointTypeName" label="管点类型" min-width="100" />
        <el-table-column label="附属物" min-width="120">
          <template #default="{ row }">
            {{ row.attachedFacilities || 'xxxx' }}
          </template>
        </el-table-column>
        <el-table-column label="埋深 (m)" min-width="100">
          <template #default="{ row }">
            {{ row.depth || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="高程 (m)" min-width="100">
          <template #default="{ row }">
            {{ row.elevation || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="connectedPipeline" label="关联管线" min-width="120" />
        <el-table-column prop="managementUnitName" label="所在管井" min-width="120" />
        <el-table-column prop="managementUnitName" label="所在道路" min-width="120" />
        <el-table-column prop="managementUnitName" label="权属单位" min-width="120" />
        <el-table-column prop="installTime" label="安装时间" min-width="120">
          <template #default="{ row }">
            {{ row.installTime ? row.installTime.substring(0, 10) : '2024年5月6日' }}
          </template>
        </el-table-column>
        <el-table-column label="位置" min-width="100">
          <template #default="{ row }">
            {{ row.address || 'xxxx位置' }}
          </template>
        </el-table-column>
        <el-table-column prop="usageStatusName" label="使用状态" min-width="100" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <PipelineNodeDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getPipelineNodePage, 
  deletePipelineNode, 
  getPipelineNodeDetail,
  getAllEnterpriseList
} from '@/api/heating';
import { POINT_TYPE_OPTIONS } from '@/constants/heating';
import { misPosition } from '@/hooks/gishooks';
import PipelineNodeDialog from './PipelineNodeDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const pointTypeOptions = POINT_TYPE_OPTIONS;
const enterpriseOptions = ref([]);

// 表单数据
const formData = ref({
  pointType: '',
  managementUnit: '',
  pointCode: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchPipelineNodeData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pointType: '',
    managementUnit: '',
    pointCode: ''
  };
  currentPage.value = 1;
  fetchPipelineNodeData();
};

// 获取管点分页数据
const fetchPipelineNodeData = async () => {
  try {
    const params = {
      pointType: formData.value.pointType,
      managementUnit: formData.value.managementUnit,
      pointCode: formData.value.pointCode
    };
    
    const res = await getPipelineNodePage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取管点数据失败:', error);
    ElMessage.error('获取管点数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取供热企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取供热企业列表失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchPipelineNodeData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPipelineNodeData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getPipelineNodeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管点详情失败');
    }
  } catch (error) {
    console.error('获取管点详情失败:', error);
    ElMessage.error('获取管点详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getPipelineNodeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取管点详情失败');
    }
  } catch (error) {
    console.error('获取管点详情失败:', error);
    ElMessage.error('获取管点详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该管点信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePipelineNode(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPipelineNodeData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除管点失败:', error);
      ElMessage.error('删除管点失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导入
const handleImport = () => {
  console.log('导入');
  ElMessage.info('导入功能待实现');
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPipelineNodeData();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchEnterprises(),
      fetchPipelineNodeData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.pipeline-node-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.pipeline-node-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 