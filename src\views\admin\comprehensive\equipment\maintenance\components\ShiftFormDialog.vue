<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="shift-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="班次名称" prop="shiftName">
        <el-input v-model="formData.shiftName" placeholder="请输入班次名称" />
      </el-form-item>

      <el-form-item label="开始时间" prop="startTime">
        <el-time-picker
          v-model="formData.startTime"
          placeholder="请选择开始时间"
          format="HH:mm"
          value-format="HH:mm:ss"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="结束时间" prop="endTime">
        <el-time-picker
          v-model="formData.endTime"
          placeholder="请选择结束时间"
          format="HH:mm"
          value-format="HH:mm:ss"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="是否跨日" prop="overNight">
        <el-radio-group v-model="formData.overNight">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <div class="help-text">跨日指班次从当天延续到第二天（如夜班）</div>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveDutyShift,
  updateDutyShift
} from '@/api/comprehensive';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增班次' : '编辑班次';
});

// 表单数据
const formData = reactive({
  id: '',
  shiftName: '',
  startTime: '',
  endTime: '',
  overNight: false,
  remark: ''
});

// 表单验证规则
const formRules = {
  shiftName: [
    { required: true, message: '请输入班次名称', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  overNight: [
    { required: true, message: '请选择是否跨日', trigger: 'change' }
  ]
};

// 重置表单
const resetForm = () => {
  formData.id = '';
  formData.shiftName = '';
  formData.startTime = '';
  formData.endTime = '';
  formData.overNight = false;
  formData.remark = '';
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理时间格式转换
    if (formData.startTime) {
      const startTime = new Date(formData.startTime);
      formData.startTime = startTime.toTimeString().substring(0, 8);
    }
    if (formData.endTime) {
      const endTime = new Date(formData.endTime);
      formData.endTime = endTime.toTimeString().substring(0, 8);
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    
    // 处理时间格式转换
    if (submitData.startTime) {
      submitData.startTime = `2023-01-01 ${submitData.startTime}`;
    }
    if (submitData.endTime) {
      submitData.endTime = `2023-01-01 ${submitData.endTime}`;
    }

    let res;
    if (props.mode === 'add') {
      res = await saveDutyShift(submitData);
    } else if (props.mode === 'edit') {
      res = await updateDutyShift(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.shift-form-dialog {
  font-family: PingFangSC, PingFang SC;
}

.help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 