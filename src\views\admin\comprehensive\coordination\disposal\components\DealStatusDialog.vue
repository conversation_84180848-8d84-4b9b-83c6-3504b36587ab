<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="deal-status-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置状态" prop="dealStatus">
            <el-select v-model="formData.dealStatus" placeholder="请选择" class="w-full" @change="handleDealStatusChange">
              <el-option v-for="item in dealStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处理状态" prop="handleStatus">
            <el-select v-model="formData.handleStatus" placeholder="请选择" class="w-full" @change="handleHandleStatusChange">
              <el-option v-for="item in handleStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置单位" prop="handleUnitName">
            <el-input v-model="formData.handleUnitName" placeholder="请输入处置单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处置人员" prop="handleUserName">
            <el-input v-model="formData.handleUserName" placeholder="请输入处置人员" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="处置时间" prop="dealTime">
            <el-date-picker
              v-model="formData.dealTime"
              type="datetime"
              placeholder="请选择处置时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="处置描述" prop="description">
            <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入处置描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置前照片">
            <div class="upload-section">
              <el-button type="primary" size="small" @click="handleBeforeUpload">+ 上传</el-button>
              <span class="upload-hint">支持格式：jpg,png,bmp,mp4,jpeg,doc,pdf等</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处置后照片">
            <div class="upload-section">
              <el-button type="primary" size="small" @click="handleAfterUpload">+ 上传</el-button>
              <span class="upload-hint">支持格式：jpg,png,bmp,mp4,jpeg,doc,pdf等</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  saveWarningDealStatus,
  updateWarningDealStatus,
  DEAL_STATUS_OPTIONS,
  HANDLE_STATUS_OPTIONS
} from '@/api/comprehensive'

// 使用从常量文件导入的选项
const dealStatusOptions = DEAL_STATUS_OPTIONS
const handleStatusOptions = HANDLE_STATUS_OPTIONS

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  warningId: {
    type: String,
    default: ''
  },
  dealId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增处置',
    edit: '编辑处置',
    view: '处置详情'
  }
  return titles[props.mode] || '处置信息'
})

// 表单数据
const formData = reactive({
  id: '',
  warningId: '',
  dealId: '',
  dealStatus: '',
  dealStatusName: '',
  handleStatus: '',
  handleStatusName: '',
  handleUnitName: '',
  handleUserName: '',
  dealTime: '',
  description: '',
  beforePicUrls: '',
  afterPicUrls: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  dealStatus: [{ required: true, message: '请选择处置状态', trigger: 'change' }],
  handleStatus: [{ required: true, message: '请选择处理状态', trigger: 'change' }],
  handleUnitName: [{ required: true, message: '请输入处置单位', trigger: 'blur' }],
  handleUserName: [{ required: true, message: '请输入处置人员', trigger: 'blur' }],
  dealTime: [{ required: true, message: '请选择处置时间', trigger: 'change' }],
  description: [{ required: true, message: '请输入处置描述', trigger: 'blur' }]
}

// 重置表单
const resetForm = () => {
  formData.id = ''
  formData.warningId = ''
  formData.dealId = ''
  formData.dealStatus = ''
  formData.dealStatusName = ''
  formData.handleStatus = ''
  formData.handleStatusName = ''
  formData.handleUnitName = ''
  formData.handleUserName = ''
  formData.dealTime = ''
  formData.description = ''
  formData.beforePicUrls = ''
  formData.afterPicUrls = ''
  formData.remark = ''
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 直接赋值所有字段
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
    // 新增时设置预警ID和处置ID
    formData.warningId = props.warningId
    formData.dealId = props.dealId
  }
}, { immediate: true, deep: true })

// 处理处置状态变化
const handleDealStatusChange = (value) => {
  const selected = dealStatusOptions.find(item => item.value === value)
  if (selected) {
    formData.dealStatusName = selected.label
  }
}

// 处理处理状态变化
const handleHandleStatusChange = (value) => {
  const selected = handleStatusOptions.find(item => item.value === value)
  if (selected) {
    formData.handleStatusName = selected.label
  }
}

// 处理处置前照片上传
const handleBeforeUpload = () => {
  ElMessage.info('文件上传功能待实现')
}

// 处理处置后照片上传
const handleAfterUpload = () => {
  ElMessage.info('文件上传功能待实现')
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构造提交数据
    const submitData = {
      id: formData.id,
      warningId: formData.warningId,
      dealId: formData.dealId,
      dealStatus: formData.dealStatus,
      dealStatusName: formData.dealStatusName,
      handleStatus: formData.handleStatus,
      handleStatusName: formData.handleStatusName,
      handleUnit: formData.handleUnitName,
      handleUnitName: formData.handleUnitName,
      handleUserId: formData.handleUserName,
      handleUserName: formData.handleUserName,
      dealTime: formData.dealTime,
      description: formData.description,
      beforePicUrls: formData.beforePicUrls,
      afterPicUrls: formData.afterPicUrls,
      remark: formData.remark
    }
    
    let res
    if (props.mode === 'add') {
      res = await saveWarningDealStatus(submitData)
    } else if (props.mode === 'edit') {
      res = await updateWarningDealStatus(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.message || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style scoped>
.deal-status-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}

.deal-unit-list {
  width: 100%;
}

.deal-unit-item {
  margin-bottom: 12px;
}

.deal-unit-item:last-child {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}
</style> 