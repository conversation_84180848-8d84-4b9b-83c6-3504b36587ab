<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="repair-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修编号" prop="repairNo">
            <el-input v-model="formData.repairNo" placeholder="请输入维修编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修时间" prop="repairTime">
            <el-date-picker
              v-model="formData.repairTime"
              type="datetime"
              placeholder="选择维修时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联设备" prop="deviceId">
            <el-select
              v-model="formData.deviceId"
              placeholder="请选择设备"
              filterable
              remote
              reserve-keyword
              :remote-method="remoteSearchDevice"
              :loading="deviceLoading"
              style="width: 100%"
              @change="handleDeviceChange"
              @visible-change="handleDeviceDropdownVisible"
            >
              <el-option
                v-for="item in deviceOptions"
                :key="item.id"
                :label="item.deviceName"
                :value="item.id"
              />
              <div
                v-if="devicePage.hasMore && deviceOptions.length > 0"
                class="device-load-more"
                @click="loadMoreDevices"
                style="padding: 8px 12px; text-align: center; color: #409eff; cursor: pointer; border-top: 1px solid #ebeef5; font-size: 12px;"
              >
                <span v-if="!deviceLoading">点击加载更多设备 ({{ deviceOptions.length }}/{{ devicePage.total }})</span>
                <span v-else>加载中...</span>
              </div>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnit">
            <el-tree-select
              v-model="formData.ownershipUnit"
              :data="deptTreeData"
              :render-after-expand="false"
              placeholder="请选择权属单位"
              style="width: 100%"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children'
              }"
              @change="handleDeptChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修位置" prop="address">
            <el-input v-model="formData.address" placeholder="维修位置" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="经度" prop="longitude">
            <el-input v-model="formData.longitude" placeholder="经度" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纬度" prop="latitude">
            <el-input v-model="formData.latitude" placeholder="纬度" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="维修结果" prop="repairResult">
            <el-select v-model="formData.repairResult" placeholder="请选择" style="width: 100%" @change="handleRepairResultChange">
              <el-option
                v-for="item in repairResultOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="维修人员" prop="repairUserName">
            <el-input v-model="formData.repairUserName" placeholder="请输入维修人员" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修内容" prop="repairContent">
            <el-input
              v-model="formData.repairContent"
              type="textarea"
              :rows="3"
              placeholder="请输入维修内容"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修方案" prop="repairScheme">
            <el-input
              v-model="formData.repairScheme"
              type="textarea"
              :rows="3"
              placeholder="请输入维修方案"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修前照片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleBeforePhotoChange"
              :on-remove="handleBeforePhotoRemove"
              :file-list="beforePhotoList"
              list-type="picture-card"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，支持jpg、png格式
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="维修后照片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleAfterPhotoChange"
              :on-remove="handleAfterPhotoRemove"
              :file-list="afterPhotoList"
              list-type="picture-card"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，支持jpg、png格式
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  大小50M以内，支持常见文件格式
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {
  saveDeviceRepair,
  updateDeviceRepair,
  getMonitorDevicePage
} from '@/api/comprehensive'
import { getDeptTree } from '@/api/system'
import { uploadFile } from '@/api/upload'
import { REPAIR_RESULT_OPTIONS } from '@/constants/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增设备维修',
    edit: '编辑设备维修',
    view: '设备维修详情'
  }
  return titles[props.mode] || '设备维修'
})

// 下拉选项数据
const repairResultOptions = ref(REPAIR_RESULT_OPTIONS)
const deviceOptions = ref([])
const deptTreeData = ref([])
const deviceLoading = ref(false)

// 设备分页管理
const devicePage = ref({
  current: 1,
  size: 10,
  total: 0,
  hasMore: true
})
const deviceSearchKeyword = ref('')

// 文件列表
const beforePhotoList = ref([])
const afterPhotoList = ref([])
const fileList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  repairNo: '',
  deviceId: '',
  deviceName: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  address: '',
  longitude: '',
  latitude: '',
  repairTime: '',
  repairContent: '',
  repairScheme: '',
  repairResult: '',
  repairResultName: '',
  repairUserName: '',
  contactInfo: '',
  beforePicUrls: '',
  afterPicUrls: '',
  fileUrls: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  repairNo: [{ required: true, message: '请输入维修编号', trigger: 'blur' }],
  deviceId: [{ required: true, message: '请选择关联设备', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择权属单位', trigger: 'change' }],
  repairTime: [{ required: true, message: '请选择维修时间', trigger: 'change' }],
  repairContent: [{ required: true, message: '请输入维修内容', trigger: 'blur' }],
  repairResult: [{ required: true, message: '请选择维修结果', trigger: 'change' }],
  repairUserName: [{ required: true, message: '请输入维修人员', trigger: 'blur' }],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  beforePhotoList.value = []
  afterPhotoList.value = []
  fileList.value = []
  
  // 重置设备选择器状态
  deviceOptions.value = []
  devicePage.value = {
    current: 1,
    size: 10,
    total: 0,
    hasMore: true
  }
  deviceSearchKeyword.value = ''
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理图片显示
    if (newVal.beforePicUrls) {
      beforePhotoList.value = newVal.beforePicUrls.split(',').map((url, index) => ({
        name: `before_${index}`,
        url: url,
        uid: Date.now() + index
      }))
    }
    
    if (newVal.afterPicUrls) {
      afterPhotoList.value = newVal.afterPicUrls.split(',').map((url, index) => ({
        name: `after_${index}`,
        url: url,
        uid: Date.now() + index + 1000
      }))
    }
    
    if (newVal.fileUrls) {
      fileList.value = newVal.fileUrls.split(',').map((url, index) => ({
        name: `file_${index}`,
        url: url,
        uid: Date.now() + index + 2000
      }))
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理设备选择变化
const handleDeviceChange = (value) => {
  const selectedDevice = deviceOptions.value.find(item => item.id === value)
  if (selectedDevice) {
    formData.deviceName = selectedDevice.deviceName
    formData.address = selectedDevice.address
    formData.longitude = selectedDevice.longitude
    formData.latitude = selectedDevice.latitude
  }
}

// 处理维修结果选择变化
const handleRepairResultChange = (value) => {
  const selected = repairResultOptions.value.find(item => item.value === value)
  if (selected) {
    formData.repairResultName = selected.label
  }
}

// 处理部门选择变化
const handleDeptChange = (value) => {
  const selectedDept = findDeptById(deptTreeData.value, value)
  if (selectedDept) {
    formData.ownershipUnitName = selectedDept.name
  }
}

// 根据ID查找部门信息
const findDeptById = (depts, id) => {
  for (const dept of depts) {
    if (dept.id === id) {
      return dept
    }
    if (dept.children) {
      const found = findDeptById(dept.children, id)
      if (found) return found
    }
  }
  return null
}

// 搜索防抖定时器
let searchTimer = null

// 远程搜索设备
const remoteSearchDevice = async (query) => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  // 使用防抖机制，避免频繁搜索
  searchTimer = setTimeout(async () => {
    deviceSearchKeyword.value = query || ''
    // 重置分页
    devicePage.value.current = 1
    devicePage.value.hasMore = true
    
    await loadDeviceList(true)
  }, 300) // 300ms 防抖延迟
}

// 加载设备列表
const loadDeviceList = async (isSearch = false) => {
  if (deviceLoading.value) return
  
  deviceLoading.value = true
  try {
    const params = {}
    if (deviceSearchKeyword.value) {
      params.deviceName = deviceSearchKeyword.value
    }
    
    const res = await getMonitorDevicePage(devicePage.value.current, devicePage.value.size, params)
    
    if (res && res.code === 200) {
      const newDevices = res.data.usmMonitorDevicePage.records || []
      
      if (isSearch || devicePage.value.current === 1) {
        // 搜索或首次加载时替换数据
        deviceOptions.value = newDevices
      } else {
        // 加载更多时追加数据
        deviceOptions.value = [...deviceOptions.value, ...newDevices]
      }
      
      // 更新分页状态
      devicePage.value.total = res.data.usmMonitorDevicePage.total || 0
      const totalPages = Math.ceil(devicePage.value.total / devicePage.value.size)
      devicePage.value.hasMore = devicePage.value.current < totalPages
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
  } finally {
    deviceLoading.value = false
  }
}

// 加载更多设备
const loadMoreDevices = async () => {
  if (!devicePage.value.hasMore || deviceLoading.value) return
  
  devicePage.value.current += 1
  await loadDeviceList(false)
}

// 设备下拉框显示状态变化
const handleDeviceDropdownVisible = (visible) => {
  if (visible) {
    if (deviceOptions.value.length === 0) {
      // 首次打开下拉框时加载初始数据
      devicePage.value.current = 1
      deviceSearchKeyword.value = ''
      loadDeviceList(true)
    }
    
    // 添加滚动监听
    nextTick(() => {
      const dropdown = document.querySelector('.el-select-dropdown__wrap')
      if (dropdown) {
        const handleScroll = () => {
          const { scrollTop, scrollHeight, clientHeight } = dropdown
          // 当滚动到接近底部时（距离底部50px以内）
          if (scrollTop + clientHeight >= scrollHeight - 50) {
            if (devicePage.value.hasMore && !deviceLoading.value) {
              loadMoreDevices()
            }
          }
        }
        
        // 移除之前的监听器（防止重复绑定）
        dropdown.removeEventListener('scroll', handleScroll)
        // 添加新的监听器
        dropdown.addEventListener('scroll', handleScroll)
      }
    })
  }
}

// 维修前照片变化处理
const handleBeforePhotoChange = async (file, fileList) => {
  // 检查文件类型和大小
  const isImage = file.raw.type.startsWith('image/')
  const isLt20M = file.size / 1024 / 1024 < 20
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }
  
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.beforePicUrls ? formData.beforePicUrls.split(',') : []
      urls.push(response.data.url)
      formData.beforePicUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 维修后照片变化处理
const handleAfterPhotoChange = async (file, fileList) => {
  // 检查文件类型和大小
  const isImage = file.raw.type.startsWith('image/')
  const isLt20M = file.size / 1024 / 1024 < 20
  
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }
  
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.afterPicUrls ? formData.afterPicUrls.split(',') : []
      urls.push(response.data.url)
      formData.afterPicUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 附件变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过 50MB!')
    return
  }

  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.fileUrls ? formData.fileUrls.split(',') : []
      urls.push(response.data.url)
      formData.fileUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 维修前照片删除处理
const handleBeforePhotoRemove = (file, fileList) => {
  if (file.url) {
    const urls = formData.beforePicUrls ? formData.beforePicUrls.split(',') : []
    const filteredUrls = urls.filter(url => url !== file.url)
    formData.beforePicUrls = filteredUrls.join(',')
    beforePhotoList.value = fileList
  }
}

// 维修后照片删除处理
const handleAfterPhotoRemove = (file, fileList) => {
  if (file.url) {
    const urls = formData.afterPicUrls ? formData.afterPicUrls.split(',') : []
    const filteredUrls = urls.filter(url => url !== file.url)
    formData.afterPicUrls = filteredUrls.join(',')
    afterPhotoList.value = fileList
  }
}

// 附件删除处理
const handleFileRemove = (file, fileList) => {
  if (file.url) {
    const urls = formData.fileUrls ? formData.fileUrls.split(',') : []
    const filteredUrls = urls.filter(url => url !== file.url)
    formData.fileUrls = filteredUrls.join(',')
    fileList.value = fileList
  }
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      deptTreeData.value = res.data || []
    }
  } catch (error) {
    console.error('获取部门树失败', error)
  }
}

// 初始化设备列表
const initDeviceList = async () => {
  // 重置分页状态
  devicePage.value.current = 1
  devicePage.value.hasMore = true
  deviceSearchKeyword.value = ''
  
  await loadDeviceList(true)
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveDeviceRepair(submitData)
    } else if (props.mode === 'edit') {
      res = await updateDeviceRepair(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree()
  initDeviceList()
})
</script>

<style scoped>
.repair-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}

.device-load-more:hover {
  background-color: #f5f7fa;
}

:deep(.el-select-dropdown__item.is-disabled) {
  color: #409eff !important;
  cursor: pointer !important;
}

:deep(.el-scrollbar__view) {
  padding-bottom: 0 !important;
}
</style> 