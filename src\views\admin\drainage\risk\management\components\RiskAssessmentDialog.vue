<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="risk-assessment-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险编码" prop="riskCode">
            <el-input v-model="formData.riskCode" placeholder="请输入风险编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管线编码" prop="pipelineCode">
            <el-input v-model="formData.pipelineCode" placeholder="请输入管线编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线类型" prop="pipelineType">
            <el-select v-model="formData.pipelineType" placeholder="请选择" class="w-full">
              <el-option v-for="item in pipelineTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管径" prop="pipeDiameter">
            <div class="flex items-center">
              <el-input-number v-model="formData.pipeDiameter" :min="0" :precision="0" class="w-full-unit" />
              <span class="unit-label">mm</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建设时间" prop="constructionTime">
            <el-date-picker
              v-model="formData.constructionTime"
              type="datetime"
              placeholder="选择建设时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管材" prop="material">
            <el-select v-model="formData.material" placeholder="请选择" class="w-full">
              <el-option v-for="item in materialOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在道路" prop="roadName">
            <el-input v-model="formData.roadName" placeholder="请输入所在道路" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="评估时间" prop="assessmentDate">
            <el-date-picker
              v-model="formData.assessmentDate"
              type="datetime"
              placeholder="选择评估时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险等级" prop="riskLevel">
            <el-select v-model="formData.riskLevel" placeholder="请选择" class="w-full" @change="handleRiskLevelChange">
              <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="管控状态" prop="pipelineStatus">
            <el-select v-model="formData.pipelineStatus" placeholder="请选择" class="w-full" @change="handlePipelineStatusChange">
              <el-option v-for="item in pipelineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="评估人" prop="assessor">
            <el-input v-model="formData.assessor" placeholder="请输入评估人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="评估类型" prop="assessmentType">
            <el-select v-model="formData.assessmentType" placeholder="请选择" class="w-full">
              <el-option v-for="item in assessmentTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="风险描述" prop="riskDescription">
            <el-input v-model="formData.riskDescription" type="textarea" :rows="3" placeholder="请输入风险描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="风险管控措施" prop="riskControlMeasures">
            <el-input v-model="formData.riskControlMeasures" type="textarea" :rows="3" placeholder="请输入风险管控措施" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  updateRiskAssessment
} from '@/api/drainage';
import { 
  PIPELINE_TYPE_OPTIONS, 
  MATERIAL_OPTIONS, 
  RISK_LEVEL_OPTIONS, 
  PIPELINE_STATUS_OPTIONS, 
  ASSESSMENT_TYPE_OPTIONS,
  RISK_LEVEL_MAP,
  PIPELINE_STATUS_MAP
} from '@/constants/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  return '修改风险评估';
});

// 下拉选项数据
const pipelineTypeOptions = PIPELINE_TYPE_OPTIONS;
const materialOptions = MATERIAL_OPTIONS;
const riskLevelOptions = RISK_LEVEL_OPTIONS.filter(item => item.value !== '');
const pipelineStatusOptions = PIPELINE_STATUS_OPTIONS.filter(item => item.value !== '');
const assessmentTypeOptions = ASSESSMENT_TYPE_OPTIONS;

// 表单数据
const formData = reactive({
  id: '',
  riskCode: '',
  pipelineCode: '',
  pipelineId: '',
  pipelineType: '',
  pipeDiameter: '',
  constructionTime: '',
  material: '',
  roadName: '',
  assessmentDate: '',
  riskLevel: '',
  riskLevelName: '',
  pipelineStatus: '',
  pipelineStatusName: '',
  assessor: '',
  assessmentType: '',
  riskDescription: '',
  riskControlMeasures: '',
  remarks: '',
  assessRiskValue: ''
});

// 表单验证规则
const formRules = {
  riskCode: [{ required: true, message: '请输入风险编码', trigger: 'blur' }],
  pipelineCode: [{ required: true, message: '请输入管线编码', trigger: 'blur' }],
  pipelineType: [{ required: true, message: '请选择管线类型', trigger: 'change' }],
  assessmentDate: [{ required: true, message: '请选择评估时间', trigger: 'change' }],
  riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  pipelineStatus: [{ required: true, message: '请选择管控状态', trigger: 'change' }],
  assessor: [{ required: true, message: '请输入评估人', trigger: 'blur' }],
  assessmentType: [{ required: true, message: '请选择评估类型', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = '';
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 处理风险等级变化
const handleRiskLevelChange = (value) => {
  if (value) {
    formData.riskLevelName = RISK_LEVEL_MAP[value] || '';
  }
};

// 处理管控状态变化
const handlePipelineStatusChange = (value) => {
  if (value) {
    formData.pipelineStatusName = PIPELINE_STATUS_MAP[value] || '';
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    const res = await updateRiskAssessment(submitData);

    if (res && res.code === 200) {
      ElMessage.success('更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || '更新失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.risk-assessment-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style> 