<template>
  <div class="drainage-report-container">
    <el-tabs v-model="activeTab" class="report-tabs">
      <el-tab-pane label="安全评估报告管理" name="management">
        <ReportManagement />
      </el-tab-pane>
      <el-tab-pane label="安全评估报告审核发布" name="audit">
        <ReportAudit />
      </el-tab-pane>
      <el-tab-pane label="安全评估报告模板管理" name="template">
        <ReportTemplate />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ReportManagement from './components/ReportManagement.vue'
import ReportAudit from './components/ReportAudit.vue'
import ReportTemplate from './components/ReportTemplate.vue'

// 排水安全风险评估报告组件

// 当前激活的选项卡
const activeTab = ref('management')

onMounted(() => {
  console.log('排水安全风险评估报告组件已挂载')
})
</script>

<style scoped>
.drainage-report-container {
  padding: 0;
}

.report-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}
</style> 