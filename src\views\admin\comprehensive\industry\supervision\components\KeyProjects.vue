<template>
  <div class="key-projects-container">
    <!-- 搜索区域 -->
    <div class="key-projects-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属区域:</span>
          <el-select v-model="formData.town" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in areaOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">计划年度:</span>
          <el-date-picker
            v-model="formData.projectYear"
            type="year"
            class="form-input"
            placeholder="请选择年度"
            format="YYYY"
            value-format="YYYY"
          />
        </div>
        <div class="form-item">
          <el-input v-model="formData.projectName" class="form-input" placeholder="请输入项目名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <el-button type="primary" class="operation-btn" @click="handleUrging" :disabled="!selectedProject">催办</el-button>
        <el-button type="primary" class="operation-btn" @click="handleUrgingRecord">催办记录</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column width="50">
          <template #default="{ row }">
            <el-radio v-model="selectedProject" :label="row.id" @change="handleSelectProject(row)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="120" />
        <el-table-column prop="projectName" label="项目名称" min-width="150" />
        <el-table-column prop="managementUnitName" label="主管单位" min-width="120" />
        <el-table-column prop="projectTypeName" label="项目类型" min-width="100" />
        <el-table-column prop="projectYear" label="计划年度" min-width="100" />
        <el-table-column prop="projectContent" label="项目内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="townName" label="项目位置" min-width="100" />
        <el-table-column label="项目建设周期（月）" min-width="120">
          <template #default="{ row }">
            {{ row.projectPeriod || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="项目总投资（万元）" min-width="120">
          <template #default="{ row }">
            {{ row.projectInvest || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="projectStatusName" label="工程进展状态" min-width="120" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <ProjectDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 催办对话框 -->
    <UrgingDialog
      v-model:visible="urgingDialogVisible"
      :project-id="selectedProject"
      @success="handleUrgingSuccess"
    />

    <!-- 催办记录对话框 -->
    <UrgingRecordDialog
      v-model:visible="urgingRecordVisible"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus'
import { 
  getProjectPage, 
  deleteProject, 
  getProjectDetail
} from '@/api/comprehensive'
import { PROJECT_TYPE_OPTIONS } from '@/constants/comprehensive'
import { AREA_OPTIONS } from '@/constants/gas'
import { misPosition } from '@/hooks/gishooks'
import ProjectDialog from './ProjectDialog.vue'
import UrgingDialog from './UrgingDialog.vue'
import UrgingRecordDialog from './UrgingRecordDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 下拉选项数据
const projectTypeOptions = PROJECT_TYPE_OPTIONS
const areaOptions = ref([])

// 选中的项目
const selectedProject = ref('')

// 表单数据 - 注意：年度重点建设项目默认isMain为true
const formData = ref({
  town: '', // 默认全部
  projectYear: '',
  projectName: '',
  isMain: true // 年度重点建设项目默认为true
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

// 催办对话框
const urgingDialogVisible = ref(false)

// 催办记录对话框
const urgingRecordVisible = ref(false)

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 初始化区域选项
const initAreaOptions = () => {
  // 从AREA_OPTIONS中提取东明县的children
  const dongmingCounty = AREA_OPTIONS.find(area => area.code === '371728')
  if (dongmingCounty && dongmingCounty.children) {
    areaOptions.value = dongmingCounty.children.map(child => ({
      label: child.name,
      value: child.code
    }))
  }
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchProjectData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    town: '', // 默认沙窝镇
    projectYear: '',
    projectName: '',
    isMain: true // 重置时保持isMain为true
  }
  currentPage.value = 1
  fetchProjectData()
}

// 获取项目列表数据
const fetchProjectData = async () => {
  try {
    const params = {
      ...formData.value
    }
    
    const res = await getProjectPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data?.records || res.data?.list || []
      total.value = res.data?.total || 0
    }
  } catch (error) {
    console.error('获取项目数据失败:', error)
    ElMessage.error('获取项目数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchProjectData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchProjectData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理项目选择
const handleSelectProject = (row) => {
  console.log('选择项目', row)
}

// 处理新增 - 新增时设置为重点项目
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = { isMain: true }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getProjectDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取项目详情失败')
    }
  } catch (error) {
    console.error('获取项目详情失败:', error)
    ElMessage.error('获取项目详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getProjectDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'view'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取项目详情失败')
    }
  } catch (error) {
    console.error('获取项目详情失败:', error)
    ElMessage.error('获取项目详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该项目信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteProject(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchProjectData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      ElMessage.error('删除项目失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 处理导出
const handleExport = () => {
  console.log('导出')
  ElMessage.info('导出功能待实现')
}

// 处理催办
const handleUrging = () => {
  if (!selectedProject.value) {
    ElMessage.warning('请先选择项目')
    return
  }
  urgingDialogVisible.value = true
}

// 处理催办记录
const handleUrgingRecord = () => {
  urgingRecordVisible.value = true
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchProjectData()
}

// 处理催办成功
const handleUrgingSuccess = () => {
  selectedProject.value = ''
  fetchProjectData()
}

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    initAreaOptions()
    await fetchProjectData()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})
</script>

<style scoped>
.key-projects-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.key-projects-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  /* background: #0277FD; */
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 