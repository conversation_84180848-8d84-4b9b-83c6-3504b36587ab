<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="report-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告编码" prop="reportCode">
            <el-input v-model="formData.reportCode" placeholder="请输入报告编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="报告名称" prop="reportName">
            <el-input v-model="formData.reportName" placeholder="请输入报告名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告类型" prop="reportType">
            <el-select v-model="formData.reportType" placeholder="请选择" class="w-full" @change="handleTypeChange">
              <el-option v-for="item in reportTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人" prop="createBy">
            <el-input v-model="formData.createBy" placeholder="请输入创建人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="更新人" prop="updateBy">
            <el-input v-model="formData.updateBy" placeholder="请输入更新人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="文件上传">
            <el-upload
              class="upload-demo"
              action="/upload"
              :on-success="handleFileSuccess"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              :before-upload="beforeFileUpload"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">只能上传pdf文件，且不超过10MB</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveAssessReport,
  updateAssessReport
} from '@/api/drainage';
import { REPORT_TYPE_OPTIONS, REPORT_TYPE_MAP } from '@/constants/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增安全评估报告',
    edit: '编辑安全评估报告',
    view: '安全评估报告详情'
  };
  return titles[props.mode] || '安全评估报告';
});

// 下拉选项数据
const reportTypeOptions = REPORT_TYPE_OPTIONS.filter(item => item.value !== '');

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  reportCode: '',
  reportName: '',
  reportType: '',
  reportTypeName: '',
  createBy: '',
  updateBy: '',
  fileUrls: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  reportCode: [{ required: true, message: '请输入报告编码', trigger: 'blur' }],
  reportName: [{ required: true, message: '请输入报告名称', trigger: 'blur' }],
  reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
  createBy: [{ required: true, message: '请输入创建人', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理文件列表
    if (newVal.fileUrls) {
      fileList.value = [{
        name: `${newVal.reportName || '报告'}.pdf`,
        url: newVal.fileUrls
      }];
    } else {
      fileList.value = [];
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理报告类型变化
const handleTypeChange = (value) => {
  const selected = reportTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.reportTypeName = selected.label;
  }
};

// 文件上传成功回调
const handleFileSuccess = (response, file) => {
  if (response && response.code === 200) {
    formData.fileUrls = response.data.url;
    ElMessage.success('文件上传成功');
  } else {
    ElMessage.error('文件上传失败');
  }
};

// 文件移除回调
const handleFileRemove = () => {
  formData.fileUrls = '';
};

// 上传前检查
const beforeFileUpload = (file) => {
  const isPDF = file.type === 'application/pdf';
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isPDF) {
    ElMessage.error('只能上传PDF格式的文件!');
    return false;
  }
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 更新报告类型名称
    const selectedType = reportTypeOptions.find(item => item.value === formData.reportType);
    if (selectedType) {
      formData.reportTypeName = selectedType.label;
    }

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveAssessReport(submitData);
    } else if (props.mode === 'edit') {
      res = await updateAssessReport(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.report-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo {
  width: 100%;
}

:deep(.el-upload__tip) {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
</style> 