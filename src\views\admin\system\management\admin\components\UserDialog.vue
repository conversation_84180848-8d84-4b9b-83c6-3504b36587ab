<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="user-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属单位" prop="deptId">
            <el-select v-model="formData.deptId" placeholder="请选择" class="w-full" @change="handleDeptChange">
              <el-option v-for="item in deptOptions" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="mobile">
            <el-input v-model="formData.mobile" placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职务" prop="major">
            <el-input v-model="formData.major" placeholder="请输入职务" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" prop="nickName">
            <el-input v-model="formData.nickName" placeholder="请输入用户名" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属角色" prop="roleId">
            <el-select v-model="formData.roleId" placeholder="请选择" class="w-full" @change="handleRoleChange">
              <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="formData.email" placeholder="请输入邮箱" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属单位" prop="belongProject">
            <el-select v-model="formData.belongProject" placeholder="请选择" class="w-full">
              <el-option label="东明县综合行政执法局/燃气办" value="东明县综合行政执法局/燃气办" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'add'">
        <el-col :span="12">
          <el-form-item label="密码" prop="password">
            <el-input v-model="formData.password" type="password" placeholder="6-10位字，英文数字组合" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">提 交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveUser,
  getAllRoles,
  getDeptTree
} from '@/api/system';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  selectedDeptId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增用户',
    edit: '编辑用户',
    view: '用户详情'
  };
  return titles[props.mode] || '用户信息';
});

// 下拉选项数据
const deptOptions = ref([]);
const roleOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  deptId: '',
  deptName: '',
  name: '',
  mobile: '',
  major: '',
  nickName: '',
  roleId: '',
  roleName: '',
  email: '',
  belongProject: '东明县综合行政执法局/燃气办',
  password: '',
  state: '0'
});

// 表单验证规则
const formRules = computed(() => {
  const rules = {
    deptId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    mobile: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    nickName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    roleId: [{ required: true, message: '请选择所属角色', trigger: 'change' }],
    email: [
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
  };

  // 只在新增模式下添加密码验证
  if (props.mode === 'add') {
    rules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 10, message: '密码长度为6-10位', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9]+$/, message: '密码只能包含字母和数字', trigger: 'blur' }
    ];
  }

  return rules;
});

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'belongProject') {
      formData[key] = '东明县综合行政执法局/燃气办';
    } else if (key === 'state') {
      formData[key] = '0';
    } else {
      formData[key] = '';
    }
  });
};

// 构建部门树形结构的平铺选项
const buildDeptOptions = (deptTree) => {
  const options = [];
  
  const traverse = (nodes, level = 0) => {
    nodes.forEach(node => {
      const prefix = '　'.repeat(level);
      options.push({
        label: prefix + node.name,
        value: node.id
      });
      
      if (node.children && node.children.length > 0) {
        traverse(node.children, level + 1);
      }
    });
  };
  
  traverse(deptTree);
  return options;
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 如果有角色数组，取第一个角色的ID
    if (newVal.role && newVal.role.length > 0) {
      formData.roleId = newVal.role[0].id;
      formData.roleName = newVal.role[0].name;
    }
  } else if (props.mode === 'add') {
    resetForm();
    // 新增时默认设置所属单位
    if (props.selectedDeptId) {
      formData.deptId = props.selectedDeptId;
    }
  }
}, { immediate: true, deep: true });

// 监听选中的部门ID变化
watch(() => props.selectedDeptId, (newVal) => {
  if (props.mode === 'add' && newVal) {
    formData.deptId = newVal;
  }
});

// 处理部门变化
const handleDeptChange = (value) => {
  const selected = deptOptions.value.find(item => item.value === value);
  if (selected) {
    formData.deptName = selected.label.replace(/　/g, ''); // 去掉缩进字符
  }
};

// 处理角色变化
const handleRoleChange = (value) => {
  const selected = roleOptions.value.find(item => item.value === value);
  if (selected) {
    formData.roleName = selected.label;
  }
};

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree();
    if (res && res.data) {
      deptOptions.value = buildDeptOptions(res.data);
    }
  } catch (error) {
    console.error('获取部门树失败', error);
  }
};

// 获取角色列表
const fetchRoles = async () => {
  try {
    const res = await getAllRoles();
    if (res && res.data) {
      roleOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取角色列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };

    const res = await saveUser(submitData);

    if (res && res.status === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree();
  fetchRoles();
});
</script>

<style scoped>
.user-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 