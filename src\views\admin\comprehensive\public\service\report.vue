<template>
  <div class="public-report-container">
    <!-- 统计卡片区域 -->
    <div class="statistics-cards">
      <div class="card-item total">
        <div class="card-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">全部报警</div>
          <div class="card-number">{{ statistics.totalCount || 0 }}</div>
        </div>
      </div>
      
      <div class="card-item pending">
        <div class="card-icon">
          <el-icon><Clock /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">待处置</div>
          <div class="card-number">{{ statistics.pendingHandle || 0 }}</div>
          <div class="card-rate">占比: {{ statistics.pendingHandleRate || '0%' }}</div>
        </div>
      </div>
      
      <div class="card-item handling">
        <div class="card-icon">
          <el-icon><Tools /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">处置中</div>
          <div class="card-number">{{ statistics.handling || 0 }}</div>
          <div class="card-rate">占比: {{ statistics.handlingRate || '0%' }}</div>
        </div>
      </div>
      
      <div class="card-item handled">
        <div class="card-icon">
          <el-icon><Check /></el-icon>
        </div>
        <div class="card-content">
          <div class="card-title">已处置</div>
          <div class="card-number">{{ statistics.handled || 0 }}</div>
          <div class="card-rate">占比: {{ statistics.handledRate || '0%' }}</div>
        </div>
      </div>
    </div>
    
    <!-- 搜索区域 -->
    <div class="public-report-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">报警来源:</span>
          <el-select v-model="formData.alarmSource" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in alarmSourceOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">紧急程度:</span>
          <el-select v-model="formData.urgentLevel" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in urgentLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">报警时间:</span>
          <el-date-picker
            v-model="formData.alarmTimeRange"
            class="form-input"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </div>
        <div class="form-item">
          <span class="label">报警状态:</span>
          <el-select v-model="formData.alarmStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in alarmStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.codeOrTitle" class="form-input" placeholder="输入报警编号或标题" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="alarmCode" label="报警编号" min-width="120" />
        <el-table-column prop="alarmTitle" label="报警标题" min-width="180" />
        <el-table-column prop="alarmTime" label="报警时间" min-width="150">
          <template #default="{ row }">
            {{ formatDateTime(row.alarmTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="alarmSourceName" label="报警来源" min-width="120" />
        <el-table-column prop="relatedBusinessName" label="所属行业" min-width="100" />
        <el-table-column prop="urgentLevelName" label="紧急程度" min-width="120" />
        <el-table-column prop="reportPerson" label="上报人员" min-width="100" />
        <el-table-column prop="reportContact" label="联系方式" min-width="120" />
        <el-table-column label="处置状态" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusTagType(row.alarmStatus)" 
              size="small"
            >
              {{ row.alarmStatusName || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="300">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleHandle(row)">处置</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 报警信息对话框 -->
    <PublicAlarmDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />

    <!-- 报警详情对话框 -->
    <AlarmDetailDialog
      v-model:visible="detailDialogVisible"
      :data="dialogData"
    />

    <!-- 报警处置对话框 -->
    <AlarmHandleDialog
      v-model:visible="handleDialogVisible"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElTag } from 'element-plus'
import { Warning, Clock, Tools, Check } from '@element-plus/icons-vue'
import {
  getAlarmStatusStatistics,
  getPublicAlarmPage,
  deletePublicAlarm,
  getPublicAlarmDetail
} from '@/api/comprehensive'
import { ALARM_SOURCE_OPTIONS, ALARM_STATUS_OPTIONS, URGENT_LEVEL_OPTIONS } from '@/constants/comprehensive'
import { misPosition } from '@/hooks/gishooks'
import PublicAlarmDialog from './components/PublicAlarmDialog.vue'
import AlarmDetailDialog from './components/AlarmDetailDialog.vue'
import AlarmHandleDialog from './components/AlarmHandleDialog.vue'
import moment from 'moment'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 统计数据
const statistics = ref({
  totalCount: 0,
  pendingHandle: 0,
  pendingHandleRate: '0%',
  handling: 0,
  handlingRate: '0%',
  handled: 0,
  handledRate: '0%'
})

// 下拉选项数据
const alarmSourceOptions = ref(ALARM_SOURCE_OPTIONS)
const alarmStatusOptions = ref(ALARM_STATUS_OPTIONS)
const urgentLevelOptions = ref(URGENT_LEVEL_OPTIONS)

// 表单数据
const formData = ref({
  alarmSource: '',
  urgentLevel: '',
  alarmTimeRange: [],
  alarmStatus: '',
  codeOrTitle: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view'
const dialogData = ref({})

const detailDialogVisible = ref(false)
const handleDialogVisible = ref(false)

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 7001501: // 待处置
      return 'warning'
    case 7001502: // 处置中
      return 'info'
    case 7001503: // 已处置
      return 'success'
    default:
      return ''
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchPublicAlarmData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    alarmSource: '',
    urgentLevel: '',
    alarmTimeRange: [],
    alarmStatus: '',
    codeOrTitle: ''
  }
  currentPage.value = 1
  fetchPublicAlarmData()
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getAlarmStatusStatistics()
    if (res && res.code === 200) {
      statistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取报警分页数据
const fetchPublicAlarmData = async () => {
  try {
    const params = {
      alarmSource: formData.value.alarmSource,
      urgentLevel: formData.value.urgentLevel,
      alarmStatus: formData.value.alarmStatus,
      codeOrTitle: formData.value.codeOrTitle
    }
    
    // 处理时间范围
    if (formData.value.alarmTimeRange && formData.value.alarmTimeRange.length === 2) {
      params.startTime = formData.value.alarmTimeRange[0]
      params.endTime = formData.value.alarmTimeRange[1]
    }
    
    const res = await getPublicAlarmPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取报警数据失败:', error)
    ElMessage.error('获取报警数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchPublicAlarmData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchPublicAlarmData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getPublicAlarmDetail(row.id)
    if (res && res.code === 200) {
      dialogMode.value = 'edit'
      dialogData.value = res.data
      dialogVisible.value = true
    } else {
      ElMessage.error('获取报警详情失败')
    }
  } catch (error) {
    console.error('获取报警详情失败:', error)
    ElMessage.error('获取报警详情失败')
  }
}

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getPublicAlarmDetail(row.id)
    if (res && res.code === 200) {
      dialogData.value = res.data
      detailDialogVisible.value = true
    } else {
      ElMessage.error('获取报警详情失败')
    }
  } catch (error) {
    console.error('获取报警详情失败:', error)
    ElMessage.error('获取报警详情失败')
  }
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该报警信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deletePublicAlarm(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        fetchPublicAlarmData()
        fetchStatistics() // 更新统计数据
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除报警失败:', error)
      ElMessage.error('删除报警失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理报警处置
const handleHandle = async (row) => {
  try {
    const res = await getPublicAlarmDetail(row.id)
    if (res && res.code === 200) {
      dialogData.value = res.data
      handleDialogVisible.value = true
    } else {
      ElMessage.error('获取报警详情失败')
    }
  } catch (error) {
    console.error('获取报警详情失败:', error)
    ElMessage.error('获取报警详情失败')
  }
}

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 处理导出
const handleExport = () => {
  console.log('导出')
  ElMessage.info('导出功能待实现')
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPublicAlarmData()
  fetchStatistics() // 更新统计数据
}

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatistics(),
      fetchPublicAlarmData()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})
</script>

<style scoped>
.public-report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计卡片样式 */
.statistics-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.card-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.card-item.total .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-item.pending .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-item.handling .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-item.handled .card-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.card-rate {
  font-size: 12px;
  color: #999;
}

/* 搜索区域样式 */
.public-report-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .statistics-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .statistics-cards {
    grid-template-columns: 1fr;
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item {
    margin-right: 0;
    justify-content: space-between;
  }
  
  .form-input {
    width: auto;
    flex: 1;
    margin-left: 16px;
  }
}
</style>