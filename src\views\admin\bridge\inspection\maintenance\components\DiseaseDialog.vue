<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="disease-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害名称" prop="diseaseName" required>
              <el-input v-model="formData.diseaseName" placeholder="请输入病害名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属桥梁" prop="bridgeId" required>
              <el-select 
                v-model="formData.bridgeId" 
                placeholder="请选择桥梁" 
                class="w-full" 
                @change="handleBridgeChange"
                filterable
              >
                <el-option
                  v-for="item in bridgeOptions"
                  :key="item.id"
                  :label="item.bridgeName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="构件名称" prop="componentId" required>
              <el-select 
                v-model="formData.componentId" 
                placeholder="请选择构件" 
                class="w-full" 
                @change="handleComponentChange"
                :disabled="!formData.bridgeId || !formData.partType"
                filterable
              >
                <el-option
                  v-for="item in componentOptions"
                  :key="item.id"
                  :label="item.componentName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部件名称" prop="partType" required>
              <el-select v-model="formData.partType" placeholder="请选择部件" class="w-full" @change="handlePartTypeChange">
                <el-option
                  v-for="item in partTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="缺损类型" prop="defectType" required>
              <el-select v-model="formData.defectType" placeholder="请选择缺损类型" class="w-full" @change="handleDefectTypeChange">
                <el-option
                  v-for="item in defectTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害等级" prop="diseaseLevel" required>
              <el-select v-model="formData.diseaseLevel" placeholder="请选择病害等级" class="w-full" @change="handleDiseaseLevelChange">
                <el-option
                  v-for="item in diseaseLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害来源" prop="diseaseSource" required>
              <el-select v-model="formData.diseaseSource" placeholder="请选择病害来源" class="w-full" @change="handleDiseaseSourceChange">
                <el-option
                  v-for="item in diseaseSourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害位置" prop="diseaseLocation">
              <el-input v-model="formData.diseaseLocation" placeholder="请输入病害位置" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="病害范围" prop="diseaseScope">
              <el-input v-model="formData.diseaseScope" placeholder="请输入病害范围" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="病害坐标" prop="diseaseGeomText">
              <el-input v-model="formData.diseaseGeomText" placeholder="请输入病害坐标" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="病害照片" prop="diseasePhotoUrl">
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleDiseaseFileChange"
                :on-remove="handleDiseaseFileRemove"
                :file-list="diseaseFileList"
                list-type="picture-card"
                :limit="9"
                :disabled="mode === 'view'"
                multiple
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    大小20M以内，建议尺寸16：9
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="养护建议" prop="maintainAdvice">
              <el-input
                v-model="formData.maintainAdvice"
                type="textarea"
                :rows="3"
                placeholder="请输入养护建议"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 处理信息 -->
      <div class="form-section">
        <h3 class="section-title">病害处理信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否处理" prop="isProcessed">
              <el-select v-model="formData.isProcessed" placeholder="请选择" class="w-full">
                <el-option
                  v-for="item in processStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理类型" prop="processType">
              <el-select v-model="formData.processType" placeholder="请选择处理类型" class="w-full" @change="handleProcessTypeChange">
                <el-option
                  v-for="item in processTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处理单位" prop="processUnit">
              <el-select v-model="formData.processUnit" placeholder="请选择处理单位" class="w-full" filterable>
                <el-option
                  v-for="item in processUnitOptions"
                  :key="item.id"
                  :label="item.enterpriseName"
                  :value="item.enterpriseName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理人" prop="processPerson">
              <el-input v-model="formData.processPerson" placeholder="请输入处理人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处理时间" prop="processTime">
              <el-date-picker
                v-model="formData.processTime"
                type="datetime"
                placeholder="请选择处理时间"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处理结果" prop="processResult">
              <el-input
                v-model="formData.processResult"
                type="textarea"
                :rows="3"
                placeholder="请输入处理结果"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处理照片" prop="processPhotoUrl">
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleProcessFileChange"
                :on-remove="handleProcessFileRemove"
                :file-list="processFileList"
                list-type="picture-card"
                :limit="9"
                :disabled="mode === 'view'"
                multiple
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    大小20M以内，建议尺寸16：9
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="处理报告" prop="processReportUrl">
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleReportFileChange"
                :on-remove="handleReportFileRemove"
                :file-list="reportFileList"
                :limit="5"
                :disabled="mode === 'view'"
                multiple
              >
                <el-button type="primary">
                  <el-icon class="el-icon--left"><Upload /></el-icon>
                  选择文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持pdf、doc、docx等格式文件，大小不超过50M
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { Plus, Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import {
  saveDiseaseRecord,
  updateDiseaseRecord,
  getBridgeBasicInfoList,
  getBridgeComponentList,
  getMaintenanceEnterpriseList,
  DEFECT_TYPE_OPTIONS,
  DISEASE_LEVEL_OPTIONS,
  DISEASE_SOURCE_OPTIONS,
  PROCESS_TYPE_OPTIONS,
  PROCESS_STATUS_OPTIONS
} from '@/api/bridge'
import { SUBJECT_PART_TYPE_OPTIONS } from '@/constants/bridge'
import { uploadFile } from '@/api/upload'
import moment from 'moment'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增病害数据',
    edit: '编辑病害数据',
    view: '病害数据详情'
  }
  return titles[props.mode] || '病害数据'
})

// 下拉选项数据
const bridgeOptions = ref([])
const componentOptions = ref([])
const processUnitOptions = ref([])
const partTypeOptions = ref(SUBJECT_PART_TYPE_OPTIONS)
const defectTypeOptions = ref(DEFECT_TYPE_OPTIONS)
const diseaseLevelOptions = ref(DISEASE_LEVEL_OPTIONS)
const diseaseSourceOptions = ref(DISEASE_SOURCE_OPTIONS)
const processTypeOptions = ref(PROCESS_TYPE_OPTIONS)
const processStatusOptions = ref(PROCESS_STATUS_OPTIONS)

// 文件列表
const diseaseFileList = ref([])
const processFileList = ref([])
const reportFileList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  diseaseName: '',
  bridgeId: '',
  componentId: '',
  componentName: '',
  partType: '',
  partTypeName: '',
  defectType: '',
  defectTypeName: '',
  diseaseLevel: '',
  diseaseLevelName: '',
  diseaseSource: '',
  diseaseSourceName: '',
  diseaseLocation: '',
  diseaseScope: '',
  diseaseGeomText: '',
  diseasePhotoUrl: '',
  maintainAdvice: '',
  isProcessed: false,
  processType: '',
  processTypeName: '',
  processUnit: '',
  processPerson: '',
  processTime: '',
  processResult: '',
  processPhotoUrl: '',
  processReportUrl: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  diseaseName: [{ required: true, message: '请输入病害名称', trigger: 'blur' }],
  bridgeId: [{ required: true, message: '请选择所属桥梁', trigger: 'change' }],
  componentId: [{ required: true, message: '请选择构件名称', trigger: 'change' }],
  partType: [{ required: true, message: '请选择部件名称', trigger: 'change' }],
  defectType: [{ required: true, message: '请选择缺损类型', trigger: 'change' }],
  diseaseLevel: [{ required: true, message: '请选择病害等级', trigger: 'change' }],
  diseaseSource: [{ required: true, message: '请选择病害来源', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'isProcessed') {
      formData[key] = false
    } else {
      formData[key] = ''
    }
  })
  diseaseFileList.value = []
  processFileList.value = []
  reportFileList.value = []
  componentOptions.value = []
}

// 加载构件选项
const loadComponentOptions = async (bridgeId, partType) => {
  try {
    const response = await getBridgeComponentList({ 
      bridgeId: bridgeId,
      partType: partType
    })
    if (response.code === 200) {
      componentOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取构件列表失败:', error)
    ElMessage.error('获取构件列表失败')
    componentOptions.value = []
  }
}

// 处理桥梁选择变化
const handleBridgeChange = async (value) => {
  formData.componentId = ''
  formData.componentName = ''
  componentOptions.value = []
  
  // 如果桥梁和部件都已选择，则加载构件列表
  if (value && formData.partType) {
    await loadComponentOptions(value, formData.partType)
  }
}

// 处理构件选择变化
const handleComponentChange = (value) => {
  const selected = componentOptions.value.find(item => item.id === value)
  if (selected) {
    formData.componentName = selected.componentName
  }
}

// 处理部件类型选择变化
const handlePartTypeChange = async (value) => {
  const selected = partTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.partTypeName = selected.label
  }
  
  // 清空构件选择
  formData.componentId = ''
  formData.componentName = ''
  componentOptions.value = []
  
  // 如果桥梁和部件都已选择，则加载构件列表
  if (formData.bridgeId && value) {
    await loadComponentOptions(formData.bridgeId, value)
  }
}

// 处理缺损类型选择变化
const handleDefectTypeChange = (value) => {
  const selected = defectTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.defectTypeName = selected.label
  }
}

// 处理病害等级选择变化
const handleDiseaseLevelChange = (value) => {
  const selected = diseaseLevelOptions.value.find(item => item.value === value)
  if (selected) {
    formData.diseaseLevelName = selected.label
  }
}

// 处理病害来源选择变化
const handleDiseaseSourceChange = (value) => {
  const selected = diseaseSourceOptions.value.find(item => item.value === value)
  if (selected) {
    formData.diseaseSourceName = selected.label
  }
}

// 处理处理类型选择变化
const handleProcessTypeChange = (value) => {
  const selected = processTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.processTypeName = selected.label
  }
}

// 病害照片文件变化处理
const handleDiseaseFileChange = async (file, fileList) => {
  if (!validateFile(file.raw)) return
  
  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.diseasePhotoUrl ? formData.diseasePhotoUrl.split(',') : []
      urls.push(response.data.url)
      formData.diseasePhotoUrl = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 病害照片文件删除处理
const handleDiseaseFileRemove = (file, fileList) => {
  if (file.url && formData.diseasePhotoUrl) {
    const urls = formData.diseasePhotoUrl.split(',').filter(url => url !== file.url)
    formData.diseasePhotoUrl = urls.join(',')
  }
}

// 处理照片文件变化处理
const handleProcessFileChange = async (file, fileList) => {
  if (!validateFile(file.raw)) return
  
  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.processPhotoUrl ? formData.processPhotoUrl.split(',') : []
      urls.push(response.data.url)
      formData.processPhotoUrl = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 处理照片文件删除处理
const handleProcessFileRemove = (file, fileList) => {
  if (file.url && formData.processPhotoUrl) {
    const urls = formData.processPhotoUrl.split(',').filter(url => url !== file.url)
    formData.processPhotoUrl = urls.join(',')
  }
}

// 处理报告文件变化处理
const handleReportFileChange = async (file, fileList) => {
  if (!validateFile(file.raw, 50)) return
  
  try {
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.processReportUrl ? formData.processReportUrl.split(',') : []
      urls.push(response.data.url)
      formData.processReportUrl = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 处理报告文件删除处理
const handleReportFileRemove = (file, fileList) => {
  if (file.url && formData.processReportUrl) {
    const urls = formData.processReportUrl.split(',').filter(url => url !== file.url)
    formData.processReportUrl = urls.join(',')
  }
}

// 文件验证
const validateFile = (file, maxSize = 20) => {
  const isLtMaxSize = file.size / 1024 / 1024 < maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`上传文件大小不能超过 ${maxSize}MB!`)
    return false
  }
  return true
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理文件显示
    if (newVal.diseasePhotoUrl) {
      diseaseFileList.value = newVal.diseasePhotoUrl.split(',').map((url, index) => ({
        name: `disease_image_${index}`,
        url: url,
        uid: Date.now() + index
      }))
    }
    
    if (newVal.processPhotoUrl) {
      processFileList.value = newVal.processPhotoUrl.split(',').map((url, index) => ({
        name: `process_image_${index}`,
        url: url,
        uid: Date.now() + index + 1000
      }))
    }
    
    if (newVal.processReportUrl) {
      reportFileList.value = newVal.processReportUrl.split(',').map((url, index) => ({
        name: `report_${index}`,
        url: url,
        uid: Date.now() + index + 2000
      }))
    }

    // 如果桥梁ID和部件类型都存在，加载构件列表
    if (newVal.bridgeId && newVal.partType) {
      loadComponentOptions(newVal.bridgeId, newVal.partType)
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200) {
      bridgeOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取桥梁列表失败:', error)
  }
}

// 获取处理单位列表
const fetchProcessUnits = async () => {
  try {
    const response = await getMaintenanceEnterpriseList({ enterpriseType: '5001002' })
    if (response.code === 200) {
      processUnitOptions.value = response.data || []
    }
  } catch (error) {
    console.error('获取处理单位列表失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }
    
    // 处理时间格式
    if (submitData.processTime) {
      submitData.processTime = moment(submitData.processTime).format('YYYY-MM-DD HH:mm:ss')
    }
    
    let res
    if (props.mode === 'add') {
      res = await saveDiseaseRecord(submitData)
    } else if (props.mode === 'edit') {
      res = await updateDiseaseRecord(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBridges()
  fetchProcessUnits()
})
</script>

<style scoped>
.disease-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #0277FD;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item[required] .el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input),
:deep(.el-textarea__inner) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-textarea__inner) {
  height: auto;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 