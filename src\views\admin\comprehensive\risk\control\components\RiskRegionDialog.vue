<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="risk-region-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险区域名称" prop="regionName">
            <el-input v-model="formData.regionName" placeholder="请输入风险区域名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" prop="responsibleUser">
            <el-input v-model="formData.responsibleUser" placeholder="请输入责任人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="区域范围">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" readonly />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" readonly />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker" :disabled="mode === 'view'"></el-button>
            </div>
            <div class="text-gray-500 text-xs mt-1">点击选择绘制</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveRiskRegionDivision,
  updateRiskRegionDivision
} from '@/api/comprehensive';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增风险区域',
    edit: '编辑风险区域',
    view: '风险区域详情'
  };
  return titles[props.mode] || '风险区域';
});

// 表单数据
const formData = reactive({
  id: '',
  regionName: '',
  responsibleUser: '',
  contactInfo: '',
  remark: '',
  geomText: '',
  longitude: '',
  latitude: ''
});

// 表单验证规则
const formRules = {
  regionName: [{ required: true, message: '请输入风险区域名称', trigger: 'blur' }],
  responsibleUser: [{ required: true, message: '请输入责任人', trigger: 'blur' }],
  contactInfo: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 解析geomText获取经纬度
const parseGeomText = (geomText) => {
  if (!geomText) return { longitude: '', latitude: '' };
  
  // 如果是POINT格式: POINT(longitude latitude)
  const pointMatch = geomText.match(/POINT\(([\d.-]+)\s+([\d.-]+)\)/);
  if (pointMatch) {
    return {
      longitude: pointMatch[1],
      latitude: pointMatch[2]
    };
  }
  
  // 如果是POLYGON格式，取第一个点
  const polygonMatch = geomText.match(/POLYGON\(\(([\d.-]+)\s+([\d.-]+)/);
  if (polygonMatch) {
    return {
      longitude: polygonMatch[1],
      latitude: polygonMatch[2]
    };
  }
  
  return { longitude: '', latitude: '' };
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 解析geomText获取经纬度显示
    const coords = parseGeomText(newVal.geomText);
    formData.longitude = coords.longitude;
    formData.latitude = coords.latitude;
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
    // 构造POINT格式的geomText
    formData.geomText = `POINT(${params.longitude} ${params.latitude})`;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 检查是否选择了区域范围
    if (!formData.geomText) {
      ElMessage.warning('请选择区域范围');
      return;
    }

    const submitData = { ...formData };
    // 移除用于显示的经纬度字段
    delete submitData.longitude;
    delete submitData.latitude;

    let res;
    if (props.mode === 'add') {
      res = await saveRiskRegionDivision(submitData);
    } else if (props.mode === 'edit') {
      res = await updateRiskRegionDivision(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.risk-region-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-xs {
  font-size: 0.75rem;
}

.mt-1 {
  margin-top: 0.25rem;
}
</style> 