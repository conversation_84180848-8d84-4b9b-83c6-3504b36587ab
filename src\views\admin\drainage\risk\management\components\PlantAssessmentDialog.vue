<template>
  <el-dialog
    v-model="dialogVisible"
    title="修改"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="plant-assessment-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="风险编码:">
            <el-input v-model="formData.riskCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="污水厂名称:">
            <el-input v-model="formData.factoryName" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="厂站类型:">
            <el-input v-model="factoryTypeName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="污水处理设计规模:">
            <div class="flex items-center">
              <el-input v-model="formData.processingCapacity" disabled />
              <span class="unit-label">m³/天</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="投入运行时间:">
            <el-input v-model="formData.operationTime" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="权属单位:">
            <el-input v-model="formData.assessor" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置:">
            <el-input v-model="formData.address" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="*风险等级:" prop="riskLevel">
            <el-select v-model="formData.riskLevel" placeholder="请选择" class="w-full" @change="handleRiskLevelChange">
              <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="*管控状态:" prop="factoryStatus">
            <el-select v-model="formData.factoryStatus" placeholder="请选择" class="w-full" @change="handleStatusChange">
              <el-option v-for="item in pipelineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="风险描述:">
            <el-input v-model="formData.riskDescription" type="textarea" :rows="3" placeholder="请输入风险描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="管控措施描述:">
            <el-input v-model="formData.riskControlMeasures" type="textarea" :rows="3" placeholder="请输入管控措施描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注:">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { updateSewagePlantRiskAssessment } from '@/api/drainage';
import { 
  RISK_LEVEL_OPTIONS,
  PIPELINE_STATUS_OPTIONS,
  RISK_LEVEL_MAP,
  PIPELINE_STATUS_MAP,
  SEWAGE_PLANT_TYPE_OPTIONS
} from '@/constants/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 下拉选项数据
const riskLevelOptions = RISK_LEVEL_OPTIONS;
const pipelineStatusOptions = PIPELINE_STATUS_OPTIONS;

// 表单数据
const formData = reactive({
  id: '',
  riskCode: '',
  factoryId: '',
  factoryName: '',
  factoryType: 0,
  factoryTypeName: '',
  processingCapacity: '',
  operationTime: '',
  assessor: '',
  address: '',
  riskLevel: 0,
  riskLevelName: '',
  factoryStatus: 0,
  factoryStatusName: '',
  riskDescription: '',
  riskControlMeasures: '',
  remarks: '',
  assessmentDate: '',
  assessmentType: 0,
  assessRiskValue: ''
});

// 计算厂站类型名称
const factoryTypeName = computed(() => {
  const option = SEWAGE_PLANT_TYPE_OPTIONS.find(item => item.value === formData.factoryType);
  return option ? option.label : '';
});

// 表单验证规则
const formRules = {
  riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }],
  factoryStatus: [{ required: true, message: '请选择管控状态', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 处理风险等级变化
const handleRiskLevelChange = (value) => {
  const selected = riskLevelOptions.find(item => item.value === value);
  if (selected) {
    formData.riskLevelName = selected.label;
  }
};

// 处理管控状态变化
const handleStatusChange = (value) => {
  const selected = pipelineStatusOptions.find(item => item.value === value);
  if (selected) {
    formData.factoryStatusName = selected.label;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 更新名称字段
    const selected = riskLevelOptions.find(item => item.value === formData.riskLevel);
    if (selected) {
      formData.riskLevelName = selected.label;
    }

    const selectedStatus = pipelineStatusOptions.find(item => item.value === formData.factoryStatus);
    if (selectedStatus) {
      formData.factoryStatusName = selectedStatus.label;
    }

    const submitData = { ...formData };

    const res = await updateSewagePlantRiskAssessment(submitData);

    if (res && res.code === 200) {
      ElMessage.success('更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || '更新失败');
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};
</script>

<style scoped>
.plant-assessment-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 60px;
  margin-left: 8px;
  font-size: 14px;
  color: #666;
}

:deep(.el-input[disabled] .el-input__wrapper) {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

:deep(.el-textarea[disabled] .el-textarea__inner) {
  background-color: #f5f5f5;
  cursor: not-allowed;
}
</style> 