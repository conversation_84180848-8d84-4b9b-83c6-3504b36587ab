<template>
  <div class="equipment-monitor-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="stat-card">
        <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
        <div class="stat-label">设备总数</div>
      </div>
      <div class="stat-card online">
        <div class="stat-number">{{ statistics.onlineCount || 0 }}</div>
        <div class="stat-label">在线设备</div>
      </div>
      <div class="stat-card offline">
        <div class="stat-number">{{ statistics.offlineCount || 0 }}</div>
        <div class="stat-label">离线设备</div>
      </div>
      <div class="stat-card rate">
        <div class="stat-number">{{ statistics.onlineRate || '0.00%' }}</div>
        <div class="stat-label">设备在线率</div>
      </div>
      <div class="stat-card report">
        <div class="stat-number">{{ statistics.reportCount || 0 }}</div>
        <div class="stat-label">监测数据上报总数</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="equipment-monitor-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属专项:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">设备状态:</span>
          <el-select v-model="formData.onlineStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in onlineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.deviceName" class="form-input" placeholder="输入设备名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="200" />
        <el-table-column prop="deviceTypeName" label="设备类型" min-width="120" />
        <el-table-column prop="relatedBusinessName" label="所属专项" min-width="100" />
        <el-table-column label="设备状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.onlineStatus === 1 ? 'success' : 'danger'" size="small">
              {{ row.onlineStatus === 1 ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ownershipUnitName" label="权属单位" min-width="150" />
        <el-table-column prop="address" label="位置" min-width="200" show-overflow-tooltip />
        <el-table-column prop="time" label="数据采集时间" min-width="160" />
        <el-table-column label="操作" fixed="right" min-width="120">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleViewData(row)">查看数据</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 设备监控弹窗 -->
    <MonitorDeviceDialog
      v-model:visible="dialogVisible"
      :device-data="selectedDevice"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag } from 'element-plus'
import { getMonitorDevicePage } from '@/api/comprehensive'
import { RELATED_BUSINESS_OPTIONS, DEVICE_ONLINE_STATUS_OPTIONS } from '@/constants/comprehensive'
import MonitorDeviceDialog from './components/MonitorDeviceDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 统计数据
const statistics = ref({
  totalCount: 0,
  onlineCount: 0,
  offlineCount: 0,
  onlineRate: '0.00%',
  reportCount: 0
})

// 下拉选项数据
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS
const onlineStatusOptions = DEVICE_ONLINE_STATUS_OPTIONS

// 表单数据
const formData = ref({
  relatedBusiness: '',
  onlineStatus: '',
  deviceName: ''
})

// 弹窗相关
const dialogVisible = ref(false)
const selectedDevice = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchMonitorDeviceData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    onlineStatus: '',
    deviceName: ''
  }
  currentPage.value = 1
  fetchMonitorDeviceData()
}

// 获取监测设备分页数据
const fetchMonitorDeviceData = async () => {
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      onlineStatus: formData.value.onlineStatus,
      deviceName: formData.value.deviceName
    }
    
    const res = await getMonitorDevicePage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      // 更新统计数据
      statistics.value = {
        totalCount: res.data.totalCount || 0,
        onlineCount: res.data.onlineCount || 0,
        offlineCount: res.data.offlineCount || 0,
        onlineRate: res.data.onlineRate || '0.00%',
        reportCount: res.data.reportCount || 0
      }

      // 更新表格数据
      const devicePage = res.data.usmMonitorDevicePage || {}
      tableData.value = devicePage.records || []
      total.value = devicePage.total || 0
    }
  } catch (error) {
    console.error('获取监测设备数据失败:', error)
    ElMessage.error('获取监测设备数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchMonitorDeviceData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchMonitorDeviceData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看数据
const handleViewData = (row) => {
  selectedDevice.value = row
  dialogVisible.value = true
}

// 在组件挂载后获取数据
onMounted(() => {
  fetchMonitorDeviceData()
})
</script>

<style scoped>
.equipment-monitor-container {
  width: 100%;
  height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 180px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card.online {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.offline {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.rate {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
}

.stat-card.report {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.equipment-monitor-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-section {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item {
    width: 100%;
    margin-right: 0;
  }
  
  .form-input {
    width: 100%;
  }
}
</style>