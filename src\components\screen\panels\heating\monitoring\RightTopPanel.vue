<template>
  <PanelBox title="报警等级统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="alarm-level-stats">
        <div class="alarm-level-item level-one">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">一级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level1.count }}</span>
              <span class="percent">{{ alarmStats.level1.percent }}</span>
            </div>
          </div>
        </div>
        
        <div class="alarm-level-item level-two">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">二级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level2.count }}</span>
              <span class="percent">{{ alarmStats.level2.percent }}</span>
            </div>
          </div>
        </div>
        
        <div class="alarm-level-item level-three">
          <div class="corner-marks">
            <span class="corner-mark top-left"></span>
            <span class="corner-mark top-right"></span>
            <span class="corner-mark bottom-left"></span>
            <span class="corner-mark bottom-right"></span>
          </div>
          <div class="alarm-level-content">
            <div class="alarm-level-title">三级报警</div>
            <div class="alarm-level-value">
              <span class="value">{{ alarmStats.level3.count }}</span>
              <span class="percent">{{ alarmStats.level3.percent }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getMonitorAnalysisLevelStatistics } from '@/api/heating'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到 dayIndex
const getDayIndex = (timeRange) => {
  switch (timeRange) {
    case 'week':
      return 7
    case 'month':
      return 30
    case 'year':
      return 365
    default:
      return 7
  }
}

// 预定义报警等级配置（确保始终显示固定结构）
const alarmLevelConfig = [
  {
    level: 'level1',
    alarmLevel: '2001701',
    title: '一级报警'
  },
  {
    level: 'level2',
    alarmLevel: '2001702',
    title: '二级报警'
  },
  {
    level: 'level3',
    alarmLevel: '2001703',
    title: '三级报警'
  }
]

// 报警等级统计数据（初始化为固定结构）
const alarmStats = ref({
  level1: { count: 0, percent: '0%' },
  level2: { count: 0, percent: '0%' },
  level3: { count: 0, percent: '0%' }
})

// 从接口获取报警等级统计数据
const fetchAlarmLevelStatistics = async (timeRange) => {
  try {
    const dayIndex = getDayIndex(timeRange)
    const response = await getMonitorAnalysisLevelStatistics(dayIndex)

    if (response.code === 200 && response.data && response.data.statistics) {
      // 重置数据为初始状态
      const newStats = {
        level1: { count: 0, percent: '0%' },
        level2: { count: 0, percent: '0%' },
        level3: { count: 0, percent: '0%' }
      }

      // 计算总数用于百分比计算
      const totalCount = response.data.statistics.reduce((sum, item) => sum + (item.totalCount || 0), 0)

      // 根据接口返回数据更新对应的报警等级
      response.data.statistics.forEach(item => {
        const configIndex = alarmLevelConfig.findIndex(config => config.alarmLevel === item.alarmLevel)
        if (configIndex !== -1) {
          const levelKey = alarmLevelConfig[configIndex].level
          const count = item.totalCount || 0
          const percent = totalCount > 0 ? ((count / totalCount) * 100).toFixed(2) + '%' : '0%'

          newStats[levelKey] = {
            count: count,
            percent: percent
          }
        }
      })

      alarmStats.value = newStats
    } else {
      // 接口返回异常时，重置为初始状态
      alarmStats.value = {
        level1: { count: 0, percent: '0%' },
        level2: { count: 0, percent: '0%' },
        level3: { count: 0, percent: '0%' }
      }
    }
  } catch (error) {
    console.error('获取报警等级统计数据失败:', error)
    // 发生错误时，重置为初始状态
    alarmStats.value = {
      level1: { count: 0, percent: '0%' },
      level2: { count: 0, percent: '0%' },
      level3: { count: 0, percent: '0%' }
    }
  }
}

// 处理时间范围变化
const handleTimeChange = (value) => {
  console.log('时间范围变更为:', value)
  fetchAlarmLevelStatistics(value)
}

// 初始化数据
onMounted(() => {
  fetchAlarmLevelStatistics(timeRange.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.alarm-level-stats {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  gap: 16px;
  margin-top: 5%;
}

.alarm-level-item {
  position: relative;
  width: 138px;
  height: 60px;
  border: 1px solid;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.level-one {
  border-color: #FF2626;
  background: linear-gradient(90deg, rgba(255, 38, 38, 0.2) 0%, rgba(255, 38, 38, 0.05) 100%);
}

.level-two {
  border-color: #FF8127;
  background: linear-gradient(90deg, rgba(255, 129, 39, 0.2) 0%, rgba(255, 129, 39, 0.05) 100%);
}

.level-three {
  border-color: #FFD700;
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.05) 100%);
}

.corner-marks {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.corner-mark {
  position: absolute;
  width: 5px;
  height: 5px;
  background-color: transparent;
}

.level-one .corner-mark {
  border-color: #FF2626;
}

.level-two .corner-mark {
  border-color: #FF8127;
}

.level-three .corner-mark {
  border-color: #FFD700;
}

.corner-mark.top-left {
  top: -1px;
  left: -1px;
  border-top: 2px solid;
  border-left: 2px solid;
}

.corner-mark.top-right {
  top: -1px;
  right: -1px;
  border-top: 2px solid;
  border-right: 2px solid;
}

.corner-mark.bottom-left {
  bottom: -1px;
  left: -1px;
  border-bottom: 2px solid;
  border-left: 2px solid;
}

.corner-mark.bottom-right {
  bottom: -1px;
  right: -1px;
  border-bottom: 2px solid;
  border-right: 2px solid;
}

.alarm-level-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.alarm-level-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #FFFFFF;
  margin-bottom: 4px;
  text-align: center;
}

.alarm-level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 18px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式布局适配 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (max-width: 1919px) {
  .panel-content {
    padding: 12px;
  }
  
  .alarm-level-item {
    width: 120px;
    height: 55px;
  }
  
  .alarm-level-title {
    font-size: 14px;
  }
  
  .alarm-level-value {
    font-size: 16px;
  }
}

@media screen and (min-width: 2561px) {
  .panel-content {
    padding: 18px;
  }
  
  .alarm-level-item {
    width: 150px;
    height: 70px;
  }
  
  .alarm-level-title {
    font-size: 18px;
  }
  
  .alarm-level-value {
    font-size: 20px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .panel-content {
    padding: 15px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .panel-content {
    padding: 8px;
  }
}
</style> 