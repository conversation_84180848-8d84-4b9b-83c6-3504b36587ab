<template>
  <PanelBox title="监测设备">
    <div class="panel-content">
      <div class="stats-row">
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备总数</span>
          <span class="stat-value-blue">{{ statsData.totalDevices }}</span>
          <span class="stat-unit">台</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">在线设备</span>
          <span class="stat-value-sky">{{ statsData.onlineDevices }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-dot"><span class="stat-dot-inner"></span></span>
          <span class="stat-label">设备在线率</span>
          <span class="stat-value-gradient">{{ statsData.onlineRate }}</span>
        </div>
      </div>

      <div class="monitoring-list">
        <div class="monitor-item" v-for="(item, index) in monitoringData" :key="index">
          <div class="monitor-info">
            <span class="monitor-name">{{ item.name }}</span>
            <div class="chart-area">
              <div class="progress-container">
                <div class="progress-bar" :style="{ width: `${item.percentage}%` }">
                  <span class="indicator-mark"></span>
                </div>
              </div>
              <div class="percentage">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import PanelBox from '@/components/screen/PanelBox.vue'
import { ref, reactive, onMounted } from 'vue'
import { getMonitorDeviceStatistics } from '@/api/heating'

// 统计数据
const statsData = reactive({
  totalDevices: 0,
  onlineDevices: 0,
  onlineRate: '0%'
})

// 监测设备数据
const monitoringData = ref([])

// 获取监测设备数据
const fetchData = async () => {
  try {
    const response = await getMonitorDeviceStatistics()
    if (response.code === 200 && response.data) {
      const data = response.data

      // 更新统计数据
      statsData.totalDevices = data.totalCount || 0
      statsData.onlineDevices = (data.totalCount || 0) - (data.offlineCount || 0)
      statsData.onlineRate = data.onlineRate ? `${Math.round(data.onlineRate)}%` : '0%'

      // 更新监测设备列表数据
      if (data.deviceTypeStatistics && Array.isArray(data.deviceTypeStatistics)) {
        monitoringData.value = data.deviceTypeStatistics.map(item => ({
          name: formatDeviceTypeName(item.deviceTypeName || item.deviceType),
          value: item.onlineCount || 0,
          percentage: Math.round(item.onlineRate || 0)
        }))
      } else {
        monitoringData.value = []
      }
    }
  } catch (error) {
    console.error('获取监测设备数据失败:', error)
    // 发生错误时重置数据
    statsData.totalDevices = 0
    statsData.onlineDevices = 0
    statsData.onlineRate = '0%'
    monitoringData.value = []
  }
}

// 格式化设备类型名称（简化显示）
const formatDeviceTypeName = (deviceTypeName) => {
  if (!deviceTypeName) return '未知设备'

  // 根据设备类型名称进行简化映射
  const nameMap = {
    '固定点式激光甲烷气体监测仪': '固定点式激光甲烷气体监测仪',
    '井盖位移传感器': '井盖位移传感器',
    '万宾固定式激光甲烷气体监测仪': '万宾固定式激光甲烷气体监测仪',
    '万宾井下温湿度传感器': '万宾井下温湿度传感器',
    '万宾井盖位移传感器': '万宾井盖位移传感器'
  }

  return nameMap[deviceTypeName] || deviceTypeName
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stats-row {
  display: flex;
  margin-bottom: 10px;
  gap: 10%;
  align-items: center;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-dot {
  width: 9px;
  height: 9px;
  background: rgba(5, 90, 219, 0.4);
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-dot-inner {
  width: 5px;
  height: 5px;
  background: #055ADB;
  border-radius: 50%;
  position: absolute;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
}

.stat-value-sky {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #3CF3FF;
  line-height: 26px;
  text-align: left;
  font-style: normal;
}

.stat-value-blue {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #055ADB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-value-gradient {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  line-height: 26px;
  text-align: left;
  font-style: normal;
  background: linear-gradient(90deg, #FFFFFF 0%, #36F281 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-unit {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

.monitoring-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.monitor-item {
  display: flex;
  flex-direction: column;
}

.monitor-info {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.monitor-name {
  color: #fff;
  font-size: 12px;
  margin-left: 5px;
  opacity: 0.6;
}

.chart-area {
  display: flex;
  align-items: center;
  gap: 5px;
}

.progress-container {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: visible;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #6DBFEC 0%, #129AFF 100%);
  border-radius: 4px;
  position: relative;
}

.indicator-mark {
  position: absolute;
  width: 2px;
  height: 14px;
  background: #FFFFFF;
  right: 0;
  top: -3px;
}

.percentage {
  min-width: 45px;
  text-align: right;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}

/* 940px左右高度的屏幕特别优化 */
@media (min-height: 910px) and (max-height: 1055px) {
  .panel-content {
    padding: 10px;
    gap: 1px;
  }

  .stats-row {
    margin-bottom: -5px;
  }

  .stat-item {
    gap: 3px;
  }

  .stat-dot {
    width: 7px;
    height: 7px;
  }

  .stat-dot-inner {
    width: 3px;
    height: 3px;
  }

  .stat-label {
    font-size: 12px;
  }

  .stat-value-sky,
  .stat-value-blue,
  .stat-value-gradient {
    font-size: 18px;
  }

  .stat-unit {
    font-size: 10px;
  }

  .monitoring-list {
    gap: 0px;
  }

  .monitor-name {
    font-size: 11px;
    margin-bottom: 1px;
  }

  .chart-area {
    gap: 3px;
  }

  .progress-container {
    height: 6px;
  }

  .indicator-mark {
    height: 10px;
    width: 1px;
    top: -2px;
  }

  .percentage {
    min-width: 35px;
    font-size: 12px;
  }
}
</style>