<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="component-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="构件编码" prop="componentCode">
            <el-input v-model="formData.componentCode" placeholder="请输入构件编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="构件名称" prop="componentName">
            <el-input v-model="formData.componentName" placeholder="请输入构件名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属桥梁" prop="bridgeId">
            <el-select v-model="formData.bridgeId" placeholder="请选择" class="w-full" @change="handleBridgeChange">
              <el-option v-for="item in bridgeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部件" prop="partType">
            <el-select v-model="formData.partType" placeholder="请选择" class="w-full" @change="handlePartTypeChange">
              <el-option v-for="item in partTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="构件位置" prop="componentLocation">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.componentLocation" placeholder="输入详细位置" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveBridgeComponent,
  updateBridgeComponent,
  getBridgeBasicInfoList
} from '@/api/bridge';
import { SUBJECT_PART_TYPE_OPTIONS, AREA_OPTIONS } from '@/constants/bridge';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const partTypeOptions = SUBJECT_PART_TYPE_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增桥梁构件',
    edit: '编辑桥梁构件',
    view: '桥梁构件详情'
  };
  return titles[props.mode] || '桥梁构件';
});

// 下拉选项数据
const bridgeOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  componentCode: '',
  componentName: '',
  bridgeId: '',
  partType: '',
  partTypeName: '',
  componentLocation: '',
  longitude: '',
  latitude: '',
  remark: '',
  nameOrCode: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  componentCode: [{ required: true, message: '请输入构件编码', trigger: 'blur' }],
  componentName: [{ required: true, message: '请输入构件名称', trigger: 'blur' }],
  bridgeId: [{ required: true, message: '请选择所属桥梁', trigger: 'change' }],
  partType: [{ required: true, message: '请选择所属部件', trigger: 'change' }],
  componentLocation: [{ required: true, message: '请输入构件位置', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 部件类型
  const selectedPartType = partTypeOptions.find(item => item.value === formData.partType);
  if (selectedPartType) {
    formData.partTypeName = selectedPartType.label;
  }

  // 设置名称或编码用于搜索
  formData.nameOrCode = formData.componentName || formData.componentCode;

  // 更新geomText字段用于兼容后端
  if (formData.longitude && formData.latitude) {
    formData.geomText = `POINT(${formData.longitude} ${formData.latitude})`;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 如果有geomText，解析为经纬度 POINT(115.075652 35.303896)   
    if (newVal.geomText && newVal.geomText.includes('POINT')) {
      const coords = newVal.geomText.split('POINT(')[1].split(')')[0].split(' ');
      if (coords.length >= 2) {
        formData.longitude = coords[0].trim();
        formData.latitude = coords[1].trim();
      }
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理桥梁变化
const handleBridgeChange = (value) => {
  const selected = bridgeOptions.value.find(item => item.value === value);
  if (selected) {
    // 可以在这里添加额外的处理逻辑
  }
};

// 处理部件类型变化
const handlePartTypeChange = (value) => {
  const selected = partTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.partTypeName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const res = await getBridgeBasicInfoList({});
    if (res && res.data) {
      bridgeOptions.value = res.data.map(item => ({
        label: item.bridgeName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveBridgeComponent(submitData);
    } else if (props.mode === 'edit') {
      res = await updateBridgeComponent(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchBridges();
});
</script>

<style scoped>
.component-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 