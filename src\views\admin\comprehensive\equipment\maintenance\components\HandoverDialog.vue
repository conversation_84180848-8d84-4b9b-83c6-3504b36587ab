<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="handover-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="值班日期" prop="scheduleTime">
            <el-date-picker
              v-model="formData.scheduleTime"
              type="date"
              placeholder="选择值班日期"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="班次" prop="shiftId">
            <el-select v-model="formData.shiftId" placeholder="请选择班次" style="width: 100%" @change="handleShiftChange">
              <el-option
                v-for="item in shiftOptions"
                :key="item.id"
                :label="item.shiftName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="交班人员" prop="handoverUserId">
            <el-select v-model="formData.handoverUserId" placeholder="请选择交班人员" style="width: 100%" @change="handleHandoverUserChange">
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.userName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="交班时间" prop="handoverTime">
            <el-date-picker
              v-model="formData.handoverTime"
              type="datetime"
              placeholder="选择交班时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="接班人员" prop="takeoverUserId">
            <el-select v-model="formData.takeoverUserId" placeholder="请选择接班人员" style="width: 100%" @change="handleTakeoverUserChange">
              <el-option
                v-for="item in userOptions"
                :key="item.id"
                :label="item.userName"
                :value="item.id"
                :disabled="item.id === formData.handoverUserId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="接班时间" prop="takeoverTime">
            <el-date-picker
              v-model="formData.takeoverTime"
              type="datetime"
              placeholder="选择接班时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="交班记录" prop="changeRecord">
            <el-input
              v-model="formData.changeRecord"
              type="textarea"
              :rows="4"
              placeholder="请输入交班记录内容"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  saveDutyShiftChange,
  updateDutyShiftChange,
  getDutyShiftList,
  getDutyUserList
} from '@/api/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增交接班记录',
    edit: '编辑交接班记录',
    view: '交接班记录详情'
  }
  return titles[props.mode] || '交接班记录'
})

// 下拉选项数据
const shiftOptions = ref([])
const userOptions = ref([])

// 表单数据
const formData = reactive({
  id: '',
  scheduleTime: '',
  shiftId: '',
  shiftName: '',
  handoverUserId: '',
  handoverUserName: '',
  handoverTime: '',
  takeoverUserId: '',
  takeoverUserName: '',
  takeoverTime: '',
  changeRecord: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  scheduleTime: [{ required: true, message: '请选择值班日期', trigger: 'change' }],
  shiftId: [{ required: true, message: '请选择班次', trigger: 'change' }],
  handoverUserId: [{ required: true, message: '请选择交班人员', trigger: 'change' }],
  handoverTime: [{ required: true, message: '请选择交班时间', trigger: 'change' }],
  takeoverUserId: [{ required: true, message: '请选择接班人员', trigger: 'change' }],
  takeoverTime: [{ required: true, message: '请选择接班时间', trigger: 'change' }],
  changeRecord: [{ required: true, message: '请输入交班记录', trigger: 'blur' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
}

// 处理班次变化
const handleShiftChange = (value) => {
  const selected = shiftOptions.value.find(item => item.id === value)
  if (selected) {
    formData.shiftName = selected.shiftName
  }
}

// 处理交班人员变化
const handleHandoverUserChange = (value) => {
  const selected = userOptions.value.find(item => item.id === value)
  if (selected) {
    formData.handoverUserName = selected.userName
  }
  // 如果接班人员和交班人员相同，清空接班人员
  if (formData.takeoverUserId === value) {
    formData.takeoverUserId = ''
    formData.takeoverUserName = ''
  }
}

// 处理接班人员变化
const handleTakeoverUserChange = (value) => {
  const selected = userOptions.value.find(item => item.id === value)
  if (selected) {
    formData.takeoverUserName = selected.userName
  }
}

// 获取班次列表
const fetchShiftList = async () => {
  try {
    const res = await getDutyShiftList({ overNight: '' })
    if (res && res.code === 200) {
      shiftOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取班次列表失败:', error)
  }
}

// 获取用户列表
const fetchUserList = async () => {
  try {
    const res = await getDutyUserList({})
    if (res && res.code === 200) {
      userOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证交班人员和接班人员不能相同
    if (formData.handoverUserId === formData.takeoverUserId) {
      ElMessage.error('交班人员和接班人员不能是同一人')
      return
    }

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveDutyShiftChange(submitData)
    } else if (props.mode === 'edit') {
      res = await updateDutyShiftChange(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchShiftList()
  fetchUserList()
})
</script>

<style scoped>
.handover-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}
</style> 