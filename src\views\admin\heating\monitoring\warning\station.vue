<template>
  <div class="planning-container">
    <el-tabs v-model="activeTab" class="planning-tabs" @tab-change="handleChange">
      <el-tab-pane label="换热站监测" name="stationSynchronization">
        <stationSynchronization />
      </el-tab-pane>
      <el-tab-pane label="视频监控" name="video">
        <stationVideo />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import stationSynchronization from './components/stationSynchronization.vue'
import stationVideo from './components/stationVideo.vue'
import {useRouter} from "vue-router";
const router = useRouter();
// 当前激活的选项卡
const activeTab = ref('stationSynchronization')

const navigateToFlood = (tab) => {
    router.push({
        path: '/heating/monitoringMis/warning/station',
        query: {
            floodTab: tab
        }
    })
}

const handleChange = (tab) => {
  console.log('Tab clicked:---', tab)
  navigateToFlood(tab);
}

onMounted(() => {
  navigateToFlood('stationSynchronization');
});

</script>

<style scoped>
.planning-container {
  padding: 0;
}

.planning-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.mb-4 {
  margin-bottom: 1rem;
}

.bg-white {
  background-color: white;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style>