<template>
  <el-dialog
    v-model="dialogVisible"
    title="班次管理"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="duty-shift-dialog"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="班次名称">
          <el-input v-model="searchForm.shiftName" placeholder="输入班次名称" />
        </el-form-item>
        <el-form-item label="是否跨日" style="width: 150px;">
          <el-select v-model="searchForm.overNight" placeholder="全部" clearable>
            <el-option label="全部" value="" />
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="operation-area">
      <el-button type="primary" @click="handleAdd">+ 新增班次</el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      height="400"
    >
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column prop="shiftName" label="班次名称" min-width="120" />
      <el-table-column label="开始时间" min-width="100">
        <template #default="{ row }">
          {{ formatTime(row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" min-width="100">
        <template #default="{ row }">
          {{ formatTime(row.endTime) }}
        </template>
      </el-table-column>
      <el-table-column label="是否跨日" min-width="80">
        <template #default="{ row }">
          <el-tag :type="row.overNight ? 'warning' : 'success'">
            {{ row.overNight ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150" />
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-area">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchShiftList"
        @current-change="fetchShiftList"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>

    <!-- 班次新增/编辑弹窗 -->
    <ShiftFormDialog
      v-model:visible="formDialogVisible"
      :mode="formMode"
      :data="formData"
      @success="handleFormSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  getDutyShiftPage,
  deleteDutyShift,
  getDutyShiftDetail
} from '@/api/comprehensive';
import ShiftFormDialog from './ShiftFormDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表单
const searchForm = ref({
  shiftName: '',
  overNight: ''
});

// 表单弹窗
const formDialogVisible = ref(false);
const formMode = ref('add');
const formData = ref({});

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  // 只显示时分
  const time = new Date(timeStr);
  return time.toTimeString().substring(0, 5);
};

// 获取班次列表
const fetchShiftList = async () => {
  try {
    loading.value = true;
    const params = {
      shiftName: searchForm.value.shiftName,
      overNight: searchForm.value.overNight
    };
    
    const res = await getDutyShiftPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取班次列表失败:', error);
    ElMessage.error('获取班次列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchShiftList();
};

// 处理重置
const handleReset = () => {
  searchForm.value = {
    shiftName: '',
    overNight: ''
  };
  currentPage.value = 1;
  fetchShiftList();
};

// 处理新增
const handleAdd = () => {
  formMode.value = 'add';
  formData.value = {};
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getDutyShiftDetail(row.id);
    if (res && res.code === 200) {
      formMode.value = 'edit';
      formData.value = res.data;
      formDialogVisible.value = true;
    } else {
      ElMessage.error('获取班次详情失败');
    }
  } catch (error) {
    console.error('获取班次详情失败:', error);
    ElMessage.error('获取班次详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该班次吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDutyShift(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchShiftList();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除班次失败:', error);
      ElMessage.error('删除班次失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 表单成功回调
const handleFormSuccess = () => {
  fetchShiftList();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 组件挂载
onMounted(() => {
    fetchShiftList();
});
// 监听visible变化
computed(() => {
  if (props.visible) {
    fetchShiftList();
  }
});
</script>

<style scoped>
.duty-shift-dialog {
  font-family: PingFangSC, PingFang SC;
}

.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.operation-area {
  margin-bottom: 16px;
}

.pagination-area {
  margin-top: 16px;
  text-align: right;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}
</style> 