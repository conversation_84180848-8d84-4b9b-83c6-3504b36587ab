<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="protection-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="防护目标编码" prop="protectCode">
            <el-input v-model="formData.protectCode" placeholder="请输入防护目标编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="防护目标名称" prop="protectName">
            <el-input v-model="formData.protectName" placeholder="请输入防护目标名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑类型" prop="buildingType">
            <el-select v-model="formData.buildingType" placeholder="请选择" class="w-full" @change="handleBuildingTypeChange">
              <el-option v-for="item in buildingTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否重点防护目标" prop="isMajor">
            <el-radio-group v-model="formData.isMajor">
              <el-radio v-for="item in isMajorOptions" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑面积" prop="buildingArea">
            <div class="flex items-center">
              <el-input-number v-model="formData.buildingArea" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">m²</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="满负荷人数" prop="fullPeopleNumber">
            <el-input-number v-model="formData.fullPeopleNumber" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="建筑年份" prop="buildingYear">
            <el-select v-model="formData.buildingYear" placeholder="请选择" class="w-full">
              <el-option v-for="item in constructionYearOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属单位" prop="managementUnit">
            <el-input v-model="formData.managementUnit" placeholder="请输入所属单位" @input="handleManagementUnitChange" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系人" prop="contactUser">
            <el-input v-model="formData.contactUser" placeholder="请输入联系人" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置坐标">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveProtection,
  updateProtection
} from '@/api/drainage';
import { 
  PROTECTION_BUILDING_TYPE_OPTIONS, 
  IS_MAJOR_PROTECTION_OPTIONS,
  PROTECTION_CONSTRUCTION_YEAR_OPTIONS 
} from '@/constants/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const buildingTypeOptions = PROTECTION_BUILDING_TYPE_OPTIONS;
const isMajorOptions = IS_MAJOR_PROTECTION_OPTIONS;
const constructionYearOptions = PROTECTION_CONSTRUCTION_YEAR_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增防护目标信息',
    edit: '编辑防护目标信息',
    view: '防护目标信息详情'
  };
  return titles[props.mode] || '防护目标信息';
});

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  protectCode: '',
  protectName: '',
  buildingType: '',
  buildingTypeName: '',
  isMajor: '',
  buildingArea: 0,
  fullPeopleNumber: 0,
  buildingYear: '',
  managementUnit: '',
  managementUnitName: '',
  contactUser: '',
  contactInfo: '',
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  protectCode: [{ required: true, message: '请输入防护目标编码', trigger: 'blur' }],
  protectName: [{ required: true, message: '请输入防护目标名称', trigger: 'blur' }],
  buildingType: [{ required: true, message: '请选择建筑类型', trigger: 'change' }],
  isMajor: [{ required: true, message: '请选择是否重点防护目标', trigger: 'change' }],
  buildingArea: [{ required: true, message: '请输入建筑面积', trigger: 'blur' }],
  fullPeopleNumber: [{ required: true, message: '请输入满负荷人数', trigger: 'blur' }],
  buildingYear: [{ required: true, message: '请选择建筑年份', trigger: 'change' }],
  managementUnit: [{ required: true, message: '请输入所属单位', trigger: 'blur' }],
  contactUser: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
  contactInfo: [{ required: true, message: '请输入联系电话', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'buildingArea' || key === 'fullPeopleNumber') {
      formData[key] = 0;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理建筑类型变化
const handleBuildingTypeChange = (value) => {
  const selected = buildingTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.buildingTypeName = selected.label;
  }
};

// 处理管理单位变化
const handleManagementUnitChange = (value) => {
  formData.managementUnitName = value;
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 更新各字段的名称，基于选中的值
    const selectedBuildingType = buildingTypeOptions.find(item => item.value === formData.buildingType);
    if (selectedBuildingType) {
      formData.buildingTypeName = selectedBuildingType.label;
    }

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveProtection(submitData);
    } else if (props.mode === 'edit') {
      res = await updateProtection(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.protection-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style> 