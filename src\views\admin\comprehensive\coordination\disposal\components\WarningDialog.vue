<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="warning-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警标题" prop="warningTitle">
            <el-input v-model="formData.warningTitle" placeholder="请输入预警标题" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警类型" prop="warningType">
            <el-select v-model="formData.warningType" placeholder="请选择" class="w-full" @change="handleWarningTypeChange">
              <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预警级别" prop="warningLevel">
            <el-select v-model="formData.warningLevel" placeholder="请选择" class="w-full" @change="handleWarningLevelChange">
              <el-option v-for="item in warningLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预警时间" prop="warningTime">
            <el-date-picker
              v-model="formData.warningTime"
              type="datetime"
              placeholder="请选择预警时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发布单位" prop="publishUnitName">
            <el-input v-model="formData.publishUnitName" placeholder="请输入发布单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发布时间" prop="publishTime">
            <el-date-picker
              v-model="formData.publishTime"
              type="datetime"
              placeholder="请选择发布时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="地理位置" prop="warningLocation">
            <el-input v-model="formData.warningLocation" placeholder="请输入地理位置" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="预警描述" prop="warningDesc">
            <el-input
              v-model="formData.warningDesc"
              type="textarea"
              :rows="3"
              placeholder="请输入预警描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 关联预案 -->
      <el-row :gutter="20" v-if="relatedScheme.schemeTitle">
        <el-col :span="24">
          <el-form-item label="关联预案">
            <div class="related-scheme-container">
              <div class="scheme-title">{{ relatedScheme.schemeTitle }}</div>
              <div class="scheme-files" v-if="relatedScheme.fileUrls">
                <div 
                  v-for="(file, index) in getSchemeFileList(relatedScheme.fileUrls)" 
                  :key="index" 
                  class="scheme-file-item"
                >
                  <el-icon class="file-icon">
                    <Document v-if="isDocumentFile(file.name)" />
                    <Picture v-else-if="isImageFile(file.name)" />
                    <Files v-else />
                  </el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <el-button 
                    type="primary" 
                    link 
                    size="small" 
                    @click="handleSchemeFileDownload(file.url, file.name)"
                  >
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置单位" prop="dealUnit">
            <el-select
              v-model="formData.dealUnit"
              placeholder="请选择处置单位"
              class="w-full"
              @change="handleDealUnitChange"
            >
              <el-option
                v-for="item in flatDeptOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处置人员" prop="dealUsers">
            <el-select
              v-model="formData.dealUsers"
              placeholder="请选择处置人员"
              class="w-full"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :disabled="!formData.dealUnit"
            >
              <el-option
                v-for="item in dealUserOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="相关附件" prop="fileUrls">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <div class="upload-header">
                <el-upload
                  ref="uploadRef"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :file-list="[]"
                  :disabled="mode === 'view' || fileList.length >= 5"
                  :show-file-list="false"
                  multiple
                  accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                >
                  <el-button 
                    type="primary" 
                    :disabled="mode === 'view' || fileList.length >= 5"
                  >
                    选择文件
                  </el-button>
                </el-upload>
                <span class="upload-tip">支持格式：.rar .zip .doc .docx .pdf等，大小50M以内</span>
              </div>
              
              <!-- 文件列表表格 -->
              <div class="file-list-table" v-if="fileList.length > 0">
                <div class="table-header">
                  <div class="column file-name">文件名</div>
                  <div class="column file-size">大小</div>
                  <div class="column file-actions">操作</div>
                </div>
                <div 
                  class="table-row" 
                  v-for="(file, index) in fileList" 
                  :key="file.uid || index"
                >
                  <div class="column file-name">
                    <div class="file-info">
                      <el-icon class="file-icon">
                        <Document v-if="isDocumentFile(file.name)" />
                        <Picture v-else-if="isImageFile(file.name)" />
                        <Files v-else />
                      </el-icon>
                      <span class="file-name-text">{{ file.name }}</span>
                    </div>
                  </div>
                  <div class="column file-size">
                    {{ formatFileSize(file.size) }}
                  </div>
                  <div class="column file-actions">
                    <div class="action-buttons">
                      <el-button 
                        type="primary" 
                        link 
                        size="small"
                        @click="handleFileRemove(file, index)"
                        v-if="mode !== 'view'"
                      >
                        删除
                      </el-button>
                      <div class="upload-status" v-if="file.status === 'uploading'">
                        <el-icon class="is-loading">
                          <Loading />
                        </el-icon>
                        <span>上传中</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="推送方式" prop="pushMethod">
            <el-checkbox v-model="formData.notifySms">短信</el-checkbox>
            <el-checkbox v-model="formData.notifyApp" class="ml-4">APP</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">发 布</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files, Loading } from '@element-plus/icons-vue'
import {
  saveWarningInfo,
  updateWarningInfo,
  getRelatedScheme,
  WARNING_LEVEL_OPTIONS,
  WARNING_TYPE_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/api/comprehensive'
import { getDeptTree, searchUsers } from '@/api/system'
import { uploadFile } from '@/api/upload'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const uploadRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增预警',
    edit: '编辑预警',
    view: '预警详情'
  }
  return titles[props.mode] || '预警信息'
})

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const warningTypeOptions = ref(WARNING_TYPE_OPTIONS)
const warningLevelOptions = ref(WARNING_LEVEL_OPTIONS)
const deptTreeOptions = ref([])
const flatDeptOptions = ref([])
const dealUserOptions = ref([])

// 文件列表
const fileList = ref([])

// 关联预案数据
const relatedScheme = ref({})

// 表单数据
const formData = reactive({
  id: '',
  warningTitle: '',
  warningCode: '',
  warningType: '',
  warningTypeName: '',
  warningLevel: '',
  warningLevelName: '',
  warningTime: '',
  publishUnitName: '',  
  publishTime: '',
  warningLocation: '',
  warningDesc: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  dealUnit: '',
  dealUnitName: '',
  dealUsers: [],
  longitude: '',
  latitude: '',
  fileUrls: '',
  notifySms: true,
  notifyApp: true,
  dealUnitAndUserList: [],
  schemeId: '',
  startTime: '',
  endTime: '',
  remark: '',
  warningStatus: 7002301 // 默认待处置
})

// 表单验证规则
const formRules = {
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  warningTitle: [{ required: true, message: '请输入预警标题', trigger: 'blur' }],
  warningType: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  warningLevel: [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  warningTime: [{ required: true, message: '请选择预警时间', trigger: 'change' }],
  publishUnitName: [{ required: true, message: '请输入发布单位', trigger: 'blur' }],
  publishTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }],
  warningLocation: [{ required: true, message: '请输入地理位置', trigger: 'blur' }],
  warningDesc: [{ required: true, message: '请输入预警描述', trigger: 'blur' }],
  dealUnit: [{ required: true, message: '请选择处置单位', trigger: 'change' }],
  dealUsers: [{ required: true, message: '请选择处置人员', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'notifySms' || key === 'notifyApp') {
      formData[key] = true
    } else if (key === 'dealUsers' || key === 'dealUnitAndUserList') {
      formData[key] = []
    } else if (key === 'warningStatus') {
      formData[key] = 7002301
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.rar', '.zip']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 特殊处理dealUnitAndUserList回显
    if (newVal.dealUnitAndUserList && Array.isArray(newVal.dealUnitAndUserList) && newVal.dealUnitAndUserList.length > 0) {
      // 从dealUnitAndUserList中提取处置单位和人员信息
      formData.dealUnit = newVal.dealUnitAndUserList[0]?.dealUnit || ''
      formData.dealUnitName = newVal.dealUnitAndUserList[0]?.dealUnitName || ''
      formData.dealUsers = newVal.dealUnitAndUserList.map(item => item.dealUser).filter(user => user)
      
      // 如果是编辑模式，加载对应的处置人员选项
      if (props.mode === 'edit' && formData.dealUnit) {
        fetchDealUsers(formData.dealUnit)
      }
    }
    
    // 处理文件显示
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim())
      fileList.value = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim(),
        uid: Date.now() + index,
        status: 'success',
        size: 0
      }))
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : ''
}

// 处理所属行业变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.value.find(item => item.value === value)
  if (selected) {
    formData.relatedBusinessName = selected.label
  }
}

// 处理预警类型变化
const handleWarningTypeChange = (value) => {
  const selected = warningTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.warningTypeName = selected.label
  }
  
  // 获取关联预案
  if (value) {
    fetchRelatedScheme(value)
  }
}

// 处理预警级别变化
const handleWarningLevelChange = (value) => {
  const selected = warningLevelOptions.value.find(item => item.value === value)
  if (selected) {
    formData.warningLevelName = selected.label
  }
}

// 处理处置单位变化
const handleDealUnitChange = (value) => {
  const selected = flatDeptOptions.value.find(item => item.id === value)
  if (selected) {
    formData.dealUnitName = selected.name
  }
  formData.dealUsers = [] // 清空已选择的人员
  
  // 获取对应的处置人员
  if (value) {
    fetchDealUsers(value)
  }
}

// 处理文件变化
const handleFileChange = async (file) => {
  // 检查文件数量限制
  if (fileList.value.length >= 5) {
    ElMessage.warning('最多只能上传5个文件')
    return false
  }

  // 检查文件大小
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('上传文件大小不能超过 50MB!')
    return false
  }

  // 添加到文件列表，状态为上传中
  const fileItem = {
    name: file.name,
    size: file.size,
    uid: file.uid || Date.now(),
    status: 'uploading',
    raw: file,
    url: ''
  }
  
  fileList.value.push(fileItem)

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response && response.status === 200) {
      // 更新文件状态为成功
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value[index].url = response.data.url
        fileList.value[index].status = 'success'
      }
      
      // 更新formData中的fileUrls
      updateFileUrl()
      
      ElMessage.success('文件上传成功')
    } else {
      // 上传失败，移除文件
      const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
      if (index !== -1) {
        fileList.value.splice(index, 1)
      }
      ElMessage.error('文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 上传失败，移除文件
    const index = fileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    ElMessage.error('文件上传失败')
  }
}

// 处理文件移除
const handleFileRemove = (file, index) => {
  fileList.value.splice(index, 1)
  updateFileUrl()
  ElMessage.success('文件已移除')
}

// 更新文件URL字符串
const updateFileUrl = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.fileUrls = urls.join(',')
  })
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true
  bus.off("getCollectLocation", handleCollectLocation)
  bus.on("getCollectLocation", handleCollectLocation)
}

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 扁平化部门树
const flattenDeptTree = (tree, result = []) => {
  if (!tree || !Array.isArray(tree)) return result
  
  tree.forEach(node => {
    result.push({
      id: node.id,
      name: node.name
    })
    if (node.children && node.children.length > 0) {
      flattenDeptTree(node.children, result)
    }
  })
  return result
}

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree()
    if (res && res.status === 200) {
      deptTreeOptions.value = res.data || []
      // 扁平化部门树用于下拉选择
      flatDeptOptions.value = flattenDeptTree(res.data || [])
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

// 获取处置人员
const fetchDealUsers = async (deptId) => {
  try {
    const res = await searchUsers({ deptId })
    if (res && res.status === 200) {
      dealUserOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取处置人员失败:', error)
    dealUserOptions.value = []
  }
}

// 获取关联预案
const fetchRelatedScheme = async (warningType) => {
  try {
    const res = await getRelatedScheme(warningType)
    if (res && res.code === 200) {
      formData.schemeId = res.data?.id || ''
      relatedScheme.value = res.data || {}
    } else {
      relatedScheme.value = {}
    }
  } catch (error) {
    console.error('获取关联预案失败:', error)
    relatedScheme.value = {}
  }
}

// 处理预案文件下载
const handleSchemeFileDownload = (fileUrl, fileName) => {
  if (fileUrl) {
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = fileName || '预案文件'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 解析预案文件URL
const getSchemeFileList = (fileUrls) => {
  if (!fileUrls) return []
  return fileUrls.split(',').map((url, index) => ({
    name: `预案文件_${index + 1}${getFileExtension(url)}`,
    url: url.trim()
  })).filter(file => file.url)
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 构建处置单位和人员数据数组格式
    const dealUnitAndUserList = []
    if (formData.dealUnit && formData.dealUsers.length > 0) {
      formData.dealUsers.forEach(userId => {
        const user = dealUserOptions.value.find(u => u.id === userId)
        dealUnitAndUserList.push({
          dealUnit: formData.dealUnit,
          dealUnitName: formData.dealUnitName,
          dealUser: userId,
          dealUserName: user?.name || ''
        })
      })
    }

    // 构建提交数据
    const submitData = {
      ...formData,
      dealUnitAndUserList: dealUnitAndUserList
    }

    // 移除不需要提交的临时字段
    delete submitData.dealUnit
    delete submitData.dealUnitName
    delete submitData.dealUsers

    let res
    if (props.mode === 'add') {
      res = await saveWarningInfo(submitData)
    } else if (props.mode === 'edit') {
      res = await updateWarningInfo(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '发布成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '发布失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})

// 组件挂载时获取数据
onMounted(async () => {
  await fetchDeptTree()
})
</script>

<style scoped>
.warning-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.file-upload-container {
  width: 100%;
}

.upload-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}

/* 文件列表表格样式 */
.file-list-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #fff;
}

.table-header {
  display: flex;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.3s;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f5f7fa;
}

.column {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e4e7ed;
}

.column:last-child {
  border-right: none;
}

.file-name {
  flex: 1;
  min-width: 0;
}

.file-size {
  width: 100px;
  justify-content: center;
}

.file-actions {
  width: 120px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 12px;
}

.upload-status .el-icon {
  font-size: 14px;
}

.upload-status .is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 关联预案样式 */
.related-scheme-container {
  width: 100%;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.scheme-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.scheme-files {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scheme-file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.scheme-file-item .file-icon {
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.scheme-file-item .file-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 