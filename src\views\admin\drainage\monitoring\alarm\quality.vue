<template>
  <div class="drainage-quality-container">
    <!-- 统计数据展示区域 -->
    <div class="statistics-section">
      <div class="statistics-cards">
        <div class="stat-card total">
          <div class="stat-number">{{ statistics.totalDeviceCount || 0 }}</div>
          <div class="stat-label">全部设备</div>
        </div>
        <div class="stat-card normal">
          <div class="stat-number">{{ statistics.normalDeviceCount || 0 }}</div>
          <div class="stat-label">正常</div>
        </div>
        <div class="stat-card offline">
          <div class="stat-number">{{ statistics.offlineDeviceCount || 0 }}</div>
          <div class="stat-label">离线</div>
        </div>
        <div class="stat-card alarm">
          <div class="stat-number">{{ statistics.alarmDeviceCount || 0 }}</div>
          <div class="stat-label">报警</div>
        </div>
      </div>
      <div class="update-time">
        <span class="label">数据更新时间：</span>
        <span class="time">{{ updateTime }}</span>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">设备类型:</span>
          <el-select v-model="formData.deviceType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in deviceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">权属单位:</span>
          <el-select v-model="formData.ownershipUnit" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">所属区域:</span>
          <el-select v-model="formData.regionCode" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in areaOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">设备状态:</span>
          <el-select v-model="formData.onlineStatus" class="form-input" placeholder="全部">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.nameOrCode" class="form-input" placeholder="请输入设备名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="120" />
        <el-table-column prop="deviceTypeNameDisplay" label="设备类型" min-width="120" />
        <el-table-column label="监测对象" min-width="120">
          <template #default="{ row }">
            {{ row.monitorTargetName || row.monitorObjectName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="监测指标" min-width="120">
          <template #default="{ row }">
            {{ row.monitorIndexName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="采集频率" min-width="100">
          <template #default="{ row }">
            {{ row.collectFrequency ? `${row.collectFrequency}秒/次` : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="ownershipUnitName" label="权属单位" min-width="120" />
        <el-table-column label="位置" min-width="120">
          <template #default="{ row }">
            {{ row.address || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="当前监测值" min-width="120">
          <template #default="{ row }">
            <span class="monitor-value">
              {{ getLatestMonitorValue(row) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.onlineStatus)" size="small">
              {{ getStatusText(row.onlineStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleMonitorCurve(row)">监测曲线</el-button>
              <el-button type="primary" link @click.stop="handleRunRecords(row)">运行记录</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 监测详情弹窗 -->
    <QualityMonitorDialog
      v-model:visible="dialogVisible"
      :device-info="selectedDevice"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag } from 'element-plus';
import moment from 'moment';
import { 
  getMonitorDevicePage,
  getDeviceType,
  getDeviceTypeStatusStatistics,
  getEnterpriseList
} from '@/api/drainage';
import { AREA_OPTIONS } from '@/constants/gas';
import { ONLINE_STATUS_OPTIONS } from '@/constants/drainage';
import { misPosition } from '@/hooks/gishooks';
import QualityMonitorDialog from './components/QualityMonitorDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 统计数据
const statistics = ref({
  totalDeviceCount: 0,
  normalDeviceCount: 0,
  offlineDeviceCount: 0,
  alarmDeviceCount: 0,
  onlineRate: 0
});

// 更新时间
const updateTime = ref(moment().format('YYYY年MM月DD日 HH:mm'));

// 下拉选项数据
const deviceTypeOptions = ref([]);
const enterpriseOptions = ref([]);
const areaOptions = computed(() => {
  // 只取东明县的children
  const dongmingCounty = AREA_OPTIONS.find(item => item.name === '东明县');
  return dongmingCounty ? dongmingCounty.children : [];
});
const statusOptions = ONLINE_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  deviceType: '',
  ownershipUnit: '',
  regionCode: '',
  onlineStatus: '',
  nameOrCode: ''
});

// 弹窗相关
const dialogVisible = ref(false);
const selectedDevice = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 获取设备类型选项
const fetchDeviceTypes = async () => {
  try {
    const res = await getDeviceType(5); // type=5 代表排污水质监测
    if (res && res.code === 200) {
      deviceTypeOptions.value = (res.data || []).map(item => ({
        label: item.deviceTypeName || item.label,
        value: item.deviceType || item.value
      }));
    }
  } catch (error) {
    console.error('获取设备类型失败:', error);
  }
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getDeviceTypeStatusStatistics(5); // type=5 代表排污水质监测
    if (res && res.code === 200) {
      statistics.value = res.data || {};
      updateTime.value = moment().format('YYYY年MM月DD日 HH:mm');
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 获取企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName || item.name,
        value: item.enterpriseName || item.name
      }));
    }
  } catch (error) {
    console.error('获取企业列表失败:', error);
  }
};

// 获取设备分页数据
const fetchDeviceData = async () => {
  try {
    const params = {
      deviceTypeList: formData.value.deviceType ? [formData.value.deviceType] : 
                      deviceTypeOptions.value.map(item => item.value), // 默认查询所有设备类型
      ownershipUnit: formData.value.ownershipUnit,
      regionCode: formData.value.regionCode,
      onlineStatus: formData.value.onlineStatus !== '' ? formData.value.onlineStatus : undefined,
      nameOrCode: formData.value.nameOrCode
    };
    
    const res = await getMonitorDevicePage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      ...params
    });
    
    if (res && res.code === 200) {
      const records = res.data.records || [];
      // 处理设备类型显示名称
      tableData.value = records.map(item => ({
        ...item,
        deviceTypeNameDisplay: getDeviceTypeName(item.deviceType)
      }));
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取设备数据失败:', error);
    ElMessage.error('获取设备数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取设备类型名称
const getDeviceTypeName = (deviceType) => {
  const found = deviceTypeOptions.value.find(item => item.value === deviceType);
  return found ? found.label : deviceType;
};

// 获取最新监测值
const getLatestMonitorValue = (row) => {
  // 这里可以根据实际需求显示最新的监测值
  // 暂时返回占位符
  return '-';
};

// 获取状态标签类型
const getStatusTagType = (status) => {
  return status === 1 ? 'success' : 'danger';
};

// 获取状态文本
const getStatusText = (status) => {
  return status === 1 ? '在线' : '离线';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchDeviceData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    deviceType: '',
    ownershipUnit: '',
    regionCode: '',
    onlineStatus: '',
    nameOrCode: ''
  };
  currentPage.value = 1;
  fetchDeviceData();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchDeviceData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchDeviceData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理监测曲线
const handleMonitorCurve = (row) => {
  selectedDevice.value = row;
  dialogVisible.value = true;
};

// 处理运行记录
const handleRunRecords = (row) => {
  selectedDevice.value = row;
  dialogVisible.value = true;
  // 可以通过事件或props传递要显示运行记录tab
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchDeviceTypes(),
      fetchStatistics(),
      fetchEnterprises()
    ]);
    await fetchDeviceData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.drainage-quality-container {
  width: 100%;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计数据展示区域样式 */
.statistics-section {
  margin-bottom: 16px;
}

.statistics-cards {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}

.stat-card {
  flex: 1;
  padding: 20px;
  border-radius: 6px;
  text-align: center;
  color: white;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card.total {
  background: linear-gradient(135deg, #36CFC9, #1890FF);
}

.stat-card.normal {
  background: linear-gradient(135deg, #95DE64, #52C41A);
}

.stat-card.offline {
  background: linear-gradient(135deg, #FFD666, #FAAD14);
}

.stat-card.alarm {
  background: linear-gradient(135deg, #FF7875, #F5222D);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.update-time {
  text-align: right;
  color: #666;
  font-size: 12px;
}

.update-time .label {
  margin-right: 4px;
}

.update-time .time {
  color: #333;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.monitor-value {
  font-weight: 500;
  color: #1890ff;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 