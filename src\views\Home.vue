<template>
  <div class="home">
    <Header />
    <!-- 系统管理和用户信息栏 -->
    <div class="system-user-bar">
      <div class="left" @click="goToSystemManagement">
        <img class="icon" src="@/assets/images/screen/system-icon.png" />
        <span class="text">运营系统管理</span>
      </div>
      <div class="right">
        <img class="avatar" src="@/assets/images/screen/avatar.png" />
        <span class="username">{{ userName }}</span>
        <div class="dropdown" @click="toggleDropdown" ref="dropdownRef">
          <img class="arrow" src="@/assets/images/screen/arrow-down.png" />
          <!-- 下拉菜单 -->
          <div class="dropdown-menu" v-show="showDropdown">
            <div class="menu-item" @click="showPasswordDialog">
              <span>密码修改</span>
            </div>
            <div class="menu-item" @click="downloadApp">
              <span>下载APP</span>
            </div>
            <div class="menu-item" @click="handleLogout">
              <span>退出登录</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 密码修改弹窗 -->
    <el-dialog
      v-model="showDialog"
      title="修改密码"
      width="400px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdatePassword">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <div class="content">
      <div class="home-modules">
        <!-- 第一行 -->
        <div class="modules-row module-left">
          <!-- 驾驶舱 -->
          <div class="module-item" @click="goToScreen">
            <img src="@/assets/images/screen/home/<USER>" alt="驾驶舱" />
          </div>
         <!-- 综合业务 -->
         <div class="module-item zonghe-item moudle-padding" @click="handleClick('zonghe')">
            <img src="@/assets/images/screen/home/<USER>" alt="综合业务" />
          </div>
        </div>
        
        <!-- 第二行 -->
        <div class="modules-row">
          <!-- 供热专项 -->
          <div class="module-item" @click="handleClick('gongre')">
            <img src="@/assets/images/screen/home/<USER>" alt="供热专项" />
          </div>
          <!-- 桥梁专项 -->
          <div class="module-item" @click="handleClick('qiaoliang')">
            <img src="@/assets/images/screen/home/<USER>" alt="桥梁专项" />
          </div>
           <!-- 燃气专项 -->
           <div class="module-item" @click="handleClick('ranqi')">
            <img src="@/assets/images/screen/home/<USER>" alt="燃气专项" />
          </div>
          <!-- 排水专项 -->
          <div class="module-item" @click="handleClick('paishui')">
            <img src="@/assets/images/screen/home/<USER>" alt="排水专项" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import Header from '@/components/common/Header.vue'

const router = useRouter()
const userStore = useUserStore()
const isActive = ref(false)
const showDropdown = ref(false)
const dropdownRef = ref(null)
const showDialog = ref(false)

const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算用户名称
const userName = computed(() => {
  return userStore.userInfo?.nickName || '系统管理员'
})

// 获取用户信息
onMounted(async () => {
  await userStore.getUserInfo()
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})

const handleClickOutside = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    showDropdown.value = false
  }
}

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const showPasswordDialog = () => {
  showDialog.value = true
  showDropdown.value = false
}

const handleUpdatePassword = async () => {
  if (!userStore.userInfo?.userId) {
    ElMessage.error('用户信息不完整')
    return
  }
  
  const success = await userStore.updatePassword(
    userStore.userInfo.userId,
    passwordForm.value.newPassword
  )
  
  if (success) {
    ElMessage.success('密码修改成功')
    showDialog.value = false
    passwordForm.value = {
      newPassword: '',
      confirmPassword: ''
    }
  }
}

const downloadApp = () => {
  // TODO: 实现APP下载功能
  ElMessage.info('APP下载功能开发中')
  showDropdown.value = false
}

const handleLogout = async () => {
  await userStore.logout()
}

const goToScreen = () => {
  isActive.value = true
  setTimeout(() => {
    router.push('/screen')
  }, 300)
}

const handleClick = (type) => {
  // 根据专项类型跳转到对应的mis端首页路由
  const routeMap = {
    'ranqi': '/gas/home',
    'paishui': '/drainage/home',
    'gongre': '/heating/home',
    'qiaoliang': '/bridge/home',
    'zonghe': 'comprehensive/industry/supervision/notice'
  }
  
  if (routeMap[type]) {
    router.push(routeMap[type])
  } else {
    console.log('未找到对应路由:', type)
  }
}

// 运营系统管理跳转
const goToSystemManagement = () => {
  router.push('/system/settings/user/list')
}
</script>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-image: url('@/assets/images/screen/home_bg.png');
  background-size: 100% 100%;
}

/* 系统管理和用户信息栏样式 */
.system-user-bar {
  height: 24px;
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 0 24px;
  margin: -35px 0 0 0;
}

.system-user-bar .left {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.system-user-bar .icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.system-user-bar .text {
  color: #FFFFFF;
  font-size: 14px;
  font-family: PingFangTC-Regular;
}

.system-user-bar .right {
  display: flex;
  align-items: center;
}

.system-user-bar .avatar {
  width: 24px;
  height: 24px;
  margin: 0 10px 0 24px;
}

.system-user-bar .username {
  color: #FFFFFF;
  font-size: 14px;
  margin-right: 9px;
}

.system-user-bar .arrow {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 120px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  padding: 8px 0;
  margin-top: 8px;
  z-index: 1000;
}

.menu-item {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  color: #FFFFFF;
  cursor: pointer;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.content {
  position: relative;
  width: 100%;
  height: calc(100% - 92px);
  display: flex;
  justify-content: center;
  align-items: center;
}

.home-modules {
  display: flex;
  flex-direction: column;
  gap: 0px;
}
.module-left {
 margin-left: -30px; 
}
.modules-row {
  display: flex;
  gap: 30px;
  margin-bottom: 0px;
}

.zonghe-item {
  transform: scale(1.008);
}
.moudle-padding{
  margin-left: -62px;
}
.module-item {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.module-item:hover {
  transform: scale(1.05);
}

.module-item.active {
  transform: scale(0.95);
}

.module-item img {
  display: block;
  width: auto;
  height: auto;
}


</style>