<!-- 设备信息 -->
<template>
  <div class="device-alarm-info">
    <div
      class="normal-row"
      v-for="(item, index) in alarmInfo"
      :class="{ 'normal-row-100': item.type && item.type === 'row' }"
      :key="index"
    >
      <div class="title-item">{{ item.label }}：</div>
      <div
        class="value-item"
        :style="{
          color:
            item.props === 'alarmLevelName'|| item.props === 'alarmValue'
              ? alarmLevelColorMap[alarmLevel]
              : '#ffffff',
        }"
      >
        {{
          item.dataMap
            ? item.dataMap[item?.value ?? ""] || "--"
            : item.value || "--"
        }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { alarmInfoConfig } from "./config.js";
import {alarmLevelColorMap} from "@/components/GisMap/popup/device.js";
import {popupMonitorAlarmApiInfo} from "@/components/GisMap/popup/popupApi.js";

const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
});
const alarmInfo = ref([]);
const alarmLevel = ref("");

// 获取基础信息
const getAlarmInfo = async () => {
    alarmInfo.value = [];
    if (popupMonitorAlarmApiInfo[props.data?.layerId]){
        popupMonitorAlarmApiInfo[props.data?.layerId](props.data?.alarmId).then(res=>{
            alarmLevel.value = res?.data?.alarmLevel || "";
            alarmInfo.value = alarmInfoConfig.map((v) => {
                return {
                    ...v,
                    value: v.props === "alarmValue" && res?.data?.alarmValue && res?.data?.alarmValueUnit? `${res?.data?.alarmValue}（${res?.data?.alarmValueUnit}）`
                        : res?.data?.[v.props] || "",
                };
            });
        })
    }
};

watch(
    () => props.data,
    () => {
        getAlarmInfo();
    },
    {
        deep: true,
        immediate: true,
    }
);
</script>
<style lang="scss" scoped>
.device-alarm-info {
  height: 360px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  .normal-row {
    width: 50%;
    display: flex;
    margin-bottom: 15px;
    justify-content: space-between;
    font-size: 14px;
    &-100 {
      width: 100%;
    }
    .title-item {
      color: rgba(255, 255, 255, 0.6);
      text-align: right;
      min-width: 100px;
    }
    .value-item {
      flex: 1;
    }
  }
}
</style>
