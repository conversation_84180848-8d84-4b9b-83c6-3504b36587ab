<template>
  <div class="drainage-risk-network-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="stat-card red">
        <div class="stat-number">{{ statisticsData.majorCount }}</div>
        <div class="stat-label">重大风险</div>
      </div>
      <div class="stat-card orange">
        <div class="stat-number">{{ statisticsData.largeCount }}</div>
        <div class="stat-label">较大风险</div>
      </div>
      <div class="stat-card yellow">
        <div class="stat-number">{{ statisticsData.normalCount }}</div>
        <div class="stat-label">一般风险</div>
      </div>
      <div class="stat-card blue">
        <div class="stat-number">{{ statisticsData.lowCount }}</div>
        <div class="stat-label">低风险</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">管线类型:</span>
          <el-select v-model="formData.pipelineType" class="form-input" placeholder="全部">
            <el-option v-for="item in pipelineTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">风险等级:</span>
          <el-select v-model="formData.riskLevel" class="form-input" placeholder="全部">
            <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管控状态:</span>
          <el-select v-model="formData.pipelineStatus" class="form-input" placeholder="全部">
            <el-option v-for="item in pipelineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.pipelineCode" class="form-input" placeholder="输入管线编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="warning" class="operation-btn" @click="handleExport">导出</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleAssessmentConfig">评估指标配置</el-button> -->
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="riskCode" label="风险编码" min-width="120" />
        <el-table-column prop="pipelineCode" label="管线编码" min-width="120" />
        <el-table-column prop="pipelineType" label="管网类型" min-width="100">
          <template #default="{ row }">
            {{ getPipelineTypeName(row.pipelineType) }}
          </template>
        </el-table-column>
        <el-table-column prop="pipeDiameter" label="管径 (mm)" min-width="100" />
        <el-table-column prop="material" label="管材" min-width="100">
          <template #default="{ row }">
            {{ getMaterialName(row.material) }}
          </template>
        </el-table-column>
        <el-table-column prop="constructionTime" label="建设时间" min-width="120" />
        <el-table-column prop="roadName" label="所在道路" min-width="120" />
        <el-table-column prop="riskLevel" label="风险等级" min-width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getRiskLevelType(row.riskLevel)" 
              effect="plain"
              style="border-radius: 12px;"
            >
              {{ row.riskLevelName || getRiskLevelName(row.riskLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assessmentDate" label="评估时间" min-width="140" />
        <el-table-column prop="pipelineStatus" label="管控状态" min-width="100">
          <template #default="{ row }">
            {{ row.pipelineStatusName || getPipelineStatusName(row.pipelineStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="240">
          <template #default="{ row }">
            <div class="operation-btns">
              <!-- <el-button type="primary" link @click.stop="handleAssessmentRecord(row)">评估记录</el-button> -->
              <el-button type="primary" link @click.stop="handleEdit(row)">修改</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <RiskAssessmentDialog
      v-model:visible="dialogVisible"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag } from 'element-plus';
import { 
  getRiskAssessmentStatistics,
  getRiskAssessmentPage, 
  getRiskAssessmentDetail
} from '@/api/drainage';
import { 
  PIPELINE_TYPE_OPTIONS, 
  MATERIAL_OPTIONS, 
  RISK_LEVEL_OPTIONS, 
  PIPELINE_STATUS_OPTIONS,
  RISK_LEVEL_MAP,
  PIPELINE_STATUS_MAP
} from '@/constants/drainage';
import { misPosition } from '@/hooks/gishooks';
import RiskAssessmentDialog from './components/RiskAssessmentDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 统计数据
const statisticsData = ref({
  majorCount: 0,    // 重大风险
  largeCount: 0,    // 较大风险
  normalCount: 0,   // 一般风险
  lowCount: 0       // 低风险
});

// 下拉选项数据
const pipelineTypeOptions = PIPELINE_TYPE_OPTIONS;
const riskLevelOptions = RISK_LEVEL_OPTIONS;
const pipelineStatusOptions = PIPELINE_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  pipelineType: '',
  riskLevel: '',
  pipelineStatus: '',
  pipelineCode: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266', 
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 获取管线类型名称
const getPipelineTypeName = (value) => {
  const option = pipelineTypeOptions.find(item => item.value === value);
  return option ? option.label : value;
};

// 获取管材名称
const getMaterialName = (value) => {
  const option = MATERIAL_OPTIONS.find(item => item.value === value);
  return option ? option.label : value;
};

// 获取风险等级名称
const getRiskLevelName = (value) => {
  return RISK_LEVEL_MAP[value] || value;
};

// 获取管控状态名称
const getPipelineStatusName = (value) => {
  return PIPELINE_STATUS_MAP[value] || value;
};

// 获取风险等级标签类型
const getRiskLevelType = (value) => {
  const typeMap = {
    3002401: 'danger',   // 重大风险 - 红色
    3002402: 'warning',  // 较大风险 - 橙色
    3002403: '',         // 一般风险 - 黄色
    3002404: 'info'      // 低风险 - 蓝色
  };
  return typeMap[value] || '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    pipelineType: '',
    riskLevel: '',
    pipelineStatus: '',
    pipelineCode: ''
  };
  currentPage.value = 1;
  fetchRiskAssessmentData();
};

// 获取统计数据
const fetchStatisticsData = async () => {
  try {
    const res = await getRiskAssessmentStatistics();
    if (res && res.code === 200) {
      statisticsData.value = res.data || {
        majorCount: 0,
        largeCount: 0,
        normalCount: 0,
        lowCount: 0
      };
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};

// 获取风险评估分页数据
const fetchRiskAssessmentData = async () => {
  try {
    const params = {
      pipelineType: formData.value.pipelineType,
      riskLevel: formData.value.riskLevel,
      pipelineStatus: formData.value.pipelineStatus,
      pipelineCode: formData.value.pipelineCode
    };
    
    const res = await getRiskAssessmentPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取风险评估数据失败:', error);
    ElMessage.error('获取风险评估数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchRiskAssessmentData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchRiskAssessmentData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理评估记录
const handleAssessmentRecord = (row) => {
  console.log('评估记录', row);
  ElMessage.info('评估记录功能待实现');
};

// 处理修改
const handleEdit = async (row) => {
  try {
    const res = await getRiskAssessmentDetail(row.id);
    if (res && res.code === 200) {
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
  }
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理导出
const handleExport = () => {
  console.log('导出');
  ElMessage.info('导出功能待实现');
};

// 处理评估指标配置
const handleAssessmentConfig = () => {
  console.log('评估指标配置');
  ElMessage.info('评估指标配置功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchRiskAssessmentData(); 
  fetchStatisticsData(); // 重新获取统计数据
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatisticsData(),
      fetchRiskAssessmentData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);  
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.drainage-risk-network-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card.red {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
}

.stat-card.orange {
  background: linear-gradient(135deg, #ffa726, #ff9800);
}

.stat-card.yellow {
  background: linear-gradient(135deg, #ffeb3b, #ffc107);
  color: #333;
}

.stat-card.blue {
  background: linear-gradient(135deg, #42a5f5, #2196f3);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 