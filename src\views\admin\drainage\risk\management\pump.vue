<template>
  <div class="drainage-risk-pump-container">
    <!-- 统计数据区域 -->
    <div class="risk-statistics">
      <div class="stat-card red">
        <div class="stat-number">{{ statisticsData.largeCount }}个</div>
        <div class="stat-label">重大风险</div>
      </div>
      <div class="stat-card orange">
        <div class="stat-number">{{ statisticsData.majorCount }}个</div>
        <div class="stat-label">较大风险</div>
      </div>
      <div class="stat-card yellow">
        <div class="stat-number">{{ statisticsData.normalCount }}个</div>
        <div class="stat-label">一般风险</div>
      </div>
      <div class="stat-card blue">
        <div class="stat-number">{{ statisticsData.lowCount }}个</div>
        <div class="stat-label">低风险</div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">泵站类型:</span>
          <el-select v-model="formData.stationType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in pumpStationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">风险等级:</span>
          <el-select v-model="formData.riskLevel" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">管控状态:</span>
          <el-select v-model="formData.stationStatus" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in pipelineStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.stationName" class="form-input" placeholder="输入泵站名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleExport">导出</el-button>
        <!-- <el-button type="primary" class="operation-btn" @click="handleConfig">评估指标配置</el-button> -->
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="stationCode" label="泵站编码" min-width="120" />
        <el-table-column prop="stationName" label="泵站名称" min-width="120" />
        <el-table-column prop="stationTypeName" label="泵站类型" min-width="100" />
        <el-table-column label="设计污水/雨水排水能力 (m³/s)" min-width="180">
          <template #default="{ row }">
            {{ row.assessRiskValue}}
          </template>
        </el-table-column>
        <el-table-column prop="assessmentDate" label="投入运行时间" min-width="120">
          <template #default="{ row }">
            {{ row.assessmentDate }}
          </template>
        </el-table-column>
                 <el-table-column prop="location" label="位置" min-width="120">
           <template #default="{ row }">
             {{ row.location }}
           </template>
         </el-table-column>
        <el-table-column label="风险等级" min-width="100">
          <template #default="{ row }">
            <div class="risk-level-tag" :class="getRiskLevelClass(row.riskLevel)">
              {{ row.riskLevelName || getRiskLevelName(row.riskLevel) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="evaluationDate" label="评估日期" min-width="120">
          <template #default="{ row }">
            {{ row.evaluationDate }}
          </template>
        </el-table-column>
        <el-table-column label="管控状态" min-width="100">
          <template #default="{ row }">
            {{ row.stationStatusName || getStationStatusName(row.stationStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="280">
          <template #default="{ row }">
            <div class="operation-btns">
              <!-- <el-button type="primary" link @click.stop="handleRecord(row)">评估记录</el-button> -->
              <el-button type="primary" link @click.stop="handleEdit(row)">修改</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <PumpStationRiskDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage } from 'element-plus';
import { 
  getPumpStationRiskAssessmentStatistics,
  getPumpStationRiskAssessmentPage, 
  getPumpStationRiskAssessmentDetail
} from '@/api/drainage';
import { 
  PUMP_STATION_TYPE_OPTIONS,
  RISK_LEVEL_OPTIONS,
  RISK_LEVEL_MAP,
  PIPELINE_STATUS_OPTIONS,
  PIPELINE_STATUS_MAP
} from '@/constants/drainage';
import { misPosition } from '@/hooks/gishooks';
import PumpStationRiskDialog from './components/PumpStationRiskDialog.vue';

// 统计数据
const statisticsData = ref({
  largeCount: 0,
  lowCount: 0,
  majorCount: 0,
  normalCount: 0
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项
const pumpStationTypeOptions = PUMP_STATION_TYPE_OPTIONS;
const riskLevelOptions = RISK_LEVEL_OPTIONS;
const pipelineStatusOptions = PIPELINE_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  stationType: '',
  riskLevel: '',
  stationStatus: '',
  stationName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('edit'); // 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 获取风险等级样式类
const getRiskLevelClass = (riskLevel) => {
  const classMap = {
    3002401: 'major-risk', // 重大风险
    3002402: 'large-risk', // 较大风险
    3002403: 'normal-risk', // 一般风险
    3002404: 'low-risk' // 低风险
  };
  return classMap[riskLevel] || 'normal-risk';
};

// 获取风险等级名称
const getRiskLevelName = (riskLevel) => {
  return RISK_LEVEL_MAP[riskLevel] || '未知';
};

// 获取管控状态名称
const getStationStatusName = (stationStatus) => {
  return PIPELINE_STATUS_MAP[stationStatus] || '未知';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchTableData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    stationType: '',
    riskLevel: '',
    stationStatus: '',
    stationName: ''
  };
  currentPage.value = 1;
  fetchTableData();
};

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getPumpStationRiskAssessmentStatistics();
    if (res && res.code === 200) {
      statisticsData.value = res.data || {
        largeCount: 0,
        lowCount: 0,
        majorCount: 0,
        normalCount: 0
      };
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    // 使用模拟数据
    statisticsData.value = {
      largeCount: 2,
      lowCount: 5,
      majorCount: 3,
      normalCount: 8
    };
  }
};

// 获取表格数据
const fetchTableData = async () => {
  try {
    const params = {
      stationType: formData.value.stationType,
      riskLevel: formData.value.riskLevel,
      stationStatus: formData.value.stationStatus,
      stationName: formData.value.stationName
    };
    
    const res = await getPumpStationRiskAssessmentPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    // 使用模拟数据
    const mockData = [
      {
        id: 1,
        stationCode: 'FX1181772222',
        stationName: '****泵站',
        stationType: 3001401,
        stationTypeName: '污水泵站',
        assessRiskValue: '10',
        assessmentDate: '2010年5月',
        ownershipUnit: '****公司',
        location: '****位置',
        evaluationDate: '2023年1月5日',
        riskLevel: 3002401,
        riskLevelName: '重大风险',
        stationStatus: 3002503,
        stationStatusName: '未管控',
        riskDescription: '',
        riskControlMeasures: '',
        remarks: ''
      },
      {
        id: 2,
        stationCode: 'GX19222',
        stationName: '****泵站',
        stationType: 3001401,
        stationTypeName: '污水泵站',
        assessRiskValue: '10',
        assessmentDate: '2023年5月',
        ownershipUnit: '****公司',
        location: '****位置',
        evaluationDate: '2023年1月5日',
        riskLevel: 3002402,
        riskLevelName: '较大风险',
        stationStatus: 3002502,
        stationStatusName: '已管控',
        riskDescription: '',
        riskControlMeasures: '',
        remarks: ''
      },
      {
        id: 3,
        stationCode: 'GX19223',
        stationName: '****泵站',
        stationType: 3001402,
        stationTypeName: '雨水泵站',
        assessRiskValue: '10',
        assessmentDate: '2023年5月',
        ownershipUnit: '****公司',
        location: '****位置',
        evaluationDate: '2023年1月5日',
        riskLevel: 3002403,
        riskLevelName: '一般风险',
        stationStatus: 3002501,
        stationStatusName: '无需管控',
        riskDescription: '',
        riskControlMeasures: '',
        remarks: ''
      },
      {
        id: 4,
        stationCode: 'GX19224',
        stationName: '****泵站',
        stationType: 3001403,
        stationTypeName: '合流泵站',
        assessRiskValue: '10',
        assessmentDate: '2023年5月',
        ownershipUnit: '****公司',
        location: '****位置',
        evaluationDate: '2023年1月5日',
        riskLevel: 3002404,
        riskLevelName: '低风险',
        stationStatus: 3002501,
        stationStatusName: '无需管控',
        riskDescription: '',
        riskControlMeasures: '',
        remarks: ''
      }
    ];
    
    // 应用筛选条件
    let filteredData = mockData;
    if (params.stationType) {
      filteredData = filteredData.filter(item => item.stationType === params.stationType);
    }
    if (params.riskLevel) {
      filteredData = filteredData.filter(item => item.riskLevel === params.riskLevel);
    }
    if (params.stationStatus) {
      filteredData = filteredData.filter(item => item.stationStatus === params.stationStatus);
    }
    if (params.stationName) {
      filteredData = filteredData.filter(item => item.stationName.includes(params.stationName));
    }
    
    // 分页处理
    const startIndex = (currentPage.value - 1) * pageSize.value;
    const endIndex = startIndex + pageSize.value;
    tableData.value = filteredData.slice(startIndex, endIndex);
    total.value = filteredData.length;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchTableData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchTableData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理评估记录
const handleRecord = (row) => {
  ElMessage.info('评估记录功能待实现');
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getPumpStationRiskAssessmentDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取详情失败');
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    // 使用行数据作为编辑数据
    dialogMode.value = 'edit';
    dialogData.value = { ...row };
    dialogVisible.value = true;
  }
};

// 处理定位
const handleLocation = (row) => {
  if (row.longitude && row.latitude && row.longitude !== '' && row.latitude !== '') {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    };
  } else {
    ElMessage.warning('没有经纬度，无法定位！');
  }
};

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能待实现');
};

// 处理评估指标配置
const handleConfig = () => {
  ElMessage.info('评估指标配置功能待实现');
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchTableData();
  fetchStatistics(); // 重新获取统计数据
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatistics(),
      fetchTableData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.drainage-risk-pump-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计数据样式 */
.risk-statistics {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  color: white;
  position: relative;
}

.stat-card.red {
  background: linear-gradient(135deg, #FF4444, #CC0000);
}

.stat-card.orange {
  background: linear-gradient(135deg, #FFA500, #FF8C00);
}

.stat-card.yellow {
  background: linear-gradient(135deg, #FFD700, #FFA500);
}

.stat-card.blue {
  background: linear-gradient(135deg, #1890FF, #0066CC);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 风险等级标签样式 */
.risk-level-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  display: inline-block;
}

.risk-level-tag.major-risk {
  background: #FF4444;
}

.risk-level-tag.large-risk {
  background: #FFA500;
}

.risk-level-tag.normal-risk {
  background: #FFD700;
  color: #333;
}

.risk-level-tag.low-risk {
  background: #1890FF;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 