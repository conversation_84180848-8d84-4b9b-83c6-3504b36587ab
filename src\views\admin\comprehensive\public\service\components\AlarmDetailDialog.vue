<template>
  <el-dialog
    v-model="dialogVisible"
    title="报警详情"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="alarm-detail-dialog"
  >
    <div class="detail-container">
      <!-- 报警信息 -->
      <div class="detail-section">
        <h3 class="section-title">报警信息：</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">报警编号：</span>
            <span class="value">{{ data.alarmCode || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">报警标题：</span>
            <span class="value">{{ data.alarmTitle || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属区域：</span>
            <span class="value">{{ data.townName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">地理位置：</span>
            <span class="value">{{ data.address || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">报警描述：</span>
            <span class="value">{{ data.alarmDesc || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">报警来源：</span>
            <span class="value">{{ data.alarmSourceName || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">紧急程度：</span>
            <span class="value">
              <el-tag :type="getUrgentLevelType(data.urgentLevel)" size="small">
                {{ data.urgentLevelName || '-' }}
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <span class="label">发生时间：</span>
            <span class="value">{{ formatDateTime(data.alarmTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">上报人员：</span>
            <span class="value">{{ data.reportPerson || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">上报人联系方式：</span>
            <span class="value">{{ data.reportContact || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">上报时间：</span>
            <span class="value">{{ formatDateTime(data.reportTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">处置单位：</span>
            <span class="value">{{ data.handleUnitName || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 处置信息 -->
      <div class="detail-section">
        <h3 class="section-title">处置信息：</h3>
        <div class="timeline-container">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in statusList"
              :key="item.id || index"
              :timestamp="formatDateTime(item.createTime)"
              placement="top"
              :type="getTimelineType(item.alarmStatus)"
            >
              <el-card>
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="status-title">{{ item.alarmStatusName }}</span>
                    <el-tag 
                      v-if="item.handleStatusName" 
                      :type="getHandleStatusType(item.handleStatus)" 
                      size="small"
                    >
                      {{ item.handleStatusName }}
                    </el-tag>
                  </div>
                  <div class="timeline-description">{{ item.description || '-' }}</div>
                  <div class="timeline-details" v-if="item.handleUserName || item.handleUnitName">
                    <div v-if="item.handleUserName" class="detail-item">
                      <span class="detail-label">处理人员：</span>
                      <span>{{ item.handleUserName }}</span>
                    </div>
                    <div v-if="item.handleUnitName" class="detail-item">
                      <span class="detail-label">处理单位：</span>
                      <span>{{ item.handleUnitName }}</span>
                    </div>
                    <div v-if="item.dealTime" class="detail-item">
                      <span class="detail-label">处理时间：</span>
                      <span>{{ formatDateTime(item.dealTime) }}</span>
                    </div>
                  </div>
                  <div class="timeline-attachments" v-if="item.beforePicUrls || item.afterPicUrls || item.fileUrls">
                    <div v-if="item.beforePicUrls" class="attachment-group">
                      <span class="attachment-label">处置前照片：</span>
                      <div class="attachment-urls">{{ item.beforePicUrls }}</div>
                    </div>
                    <div v-if="item.afterPicUrls" class="attachment-group">
                      <span class="attachment-label">处置后照片：</span>
                      <div class="attachment-urls">{{ item.afterPicUrls }}</div>
                    </div>
                    <div v-if="item.fileUrls" class="attachment-group">
                      <span class="attachment-label">处置附件：</span>
                      <div class="attachment-urls">{{ item.fileUrls }}</div>
                    </div>
                  </div>
                  <div v-if="item.remark" class="timeline-remark">
                    <span class="detail-label">备注：</span>
                    <span>{{ item.remark }}</span>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          <div v-if="statusList.length === 0" class="empty-timeline">
            <el-empty description="暂无处置记录" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAlarmStatusList } from '@/api/comprehensive'
import moment from 'moment'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 状态时间线数据
const statusList = ref([])

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取紧急程度标签类型
const getUrgentLevelType = (level) => {
  switch (level) {
    case 7001401: // 特别重大（Ⅰ级）
      return 'danger'
    case 7001402: // 重大（Ⅱ级）
      return 'warning'
    case 7001403: // 较大（Ⅲ级）
      return 'info'
    case 7001404: // 一般（Ⅳ级）
      return 'success'
    default:
      return ''
  }
}

// 获取时间线类型
const getTimelineType = (status) => {
  switch (status) {
    case 7001601: // 发现报警
      return 'warning'
    case 7001602: // 处置反馈
      return 'primary'
    case 7001603: // 处置完成
      return 'success'
    default:
      return 'info'
  }
}

// 获取处理状态标签类型
const getHandleStatusType = (status) => {
  switch (status) {
    case 7001701: // 处置中
      return 'warning'
    case 7001702: // 已处置
      return 'success'
    default:
      return ''
  }
}

// 获取状态时间线
const fetchStatusList = async () => {
  if (!props.data.id) return
  
  try {
    const res = await getAlarmStatusList(props.data.id)
    if (res && res.code === 200) {
      // 按时间倒序排列，最新的在上面
      statusList.value = (res.data || []).sort((a, b) => 
        new Date(b.createTime) - new Date(a.createTime)
      )
    }
  } catch (error) {
    console.error('获取处置状态失败:', error)
    ElMessage.error('获取处置状态失败')
  }
}

// 监听data变化
watch(() => props.data, (newVal) => {
  if (newVal && newVal.id && props.visible) {
    fetchStatusList()
  }
}, { immediate: true, deep: true })

// 监听visible变化
watch(() => props.visible, (visible) => {
  if (visible && props.data.id) {
    fetchStatusList()
  }
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  statusList.value = []
}

// 组件挂载时如果已有数据则获取状态列表
onMounted(() => {
  if (props.visible && props.data.id) {
    fetchStatusList()
  }
})
</script>

<style scoped>
.alarm-detail-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-left: 4px solid #0277FD;
  padding-left: 12px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 120px;
  margin-right: 8px;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.timeline-container {
  min-height: 200px;
}

.timeline-content {
  padding: 12px 0;
}

.timeline-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-title {
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.timeline-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.timeline-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.timeline-attachments {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.attachment-group {
  margin-bottom: 8px;
}

.attachment-group:last-child {
  margin-bottom: 0;
}

.attachment-label {
  font-weight: 500;
  color: #666;
  display: block;
  margin-bottom: 4px;
}

.attachment-urls {
  color: #0277FD;
  font-size: 14px;
  word-break: break-all;
}

.timeline-remark {
  color: #999;
  font-style: italic;
}

.empty-timeline {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}

:deep(.el-card) {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 16px;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline-details {
    grid-template-columns: 1fr;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style> 