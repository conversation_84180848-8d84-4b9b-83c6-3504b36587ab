<template>
  <el-dialog
    v-model="dialogVisible"
    title="值班人员管理"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="duty-user-dialog"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="人员姓名">
          <el-input v-model="searchForm.userName" placeholder="输入人员姓名" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="searchForm.userPhone" placeholder="输入联系电话" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮 -->
    <div class="operation-area">
      <el-button type="primary" @click="handleAdd">+ 新增人员</el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      height="400"
    >
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column prop="userName" label="姓名" min-width="100" />
      <el-table-column prop="userPhone" label="联系电话" min-width="120" />
      <el-table-column prop="userRole" label="职务角色" min-width="100" />
      <el-table-column prop="belongUnitName" label="所属单位" min-width="120" />
      <el-table-column prop="remark" label="备注" min-width="120" />
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-area">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="fetchUserList"
        @current-change="fetchUserList"
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>

    <!-- 人员新增/编辑弹窗 -->
    <UserFormDialog
      v-model:visible="formDialogVisible"
      :mode="formMode"
      :data="formData"
      @success="handleFormSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  getDutyUserPage,
  deleteDutyUser,
  getDutyUserDetail
} from '@/api/comprehensive';
import UserFormDialog from './UserFormDialog.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索表单
const searchForm = ref({
  userName: '',
  userPhone: ''
});

// 表单弹窗
const formDialogVisible = ref(false);
const formMode = ref('add');
const formData = ref({});

// 获取用户列表
const fetchUserList = async () => {
  try {
    loading.value = true;
    const params = {
      userName: searchForm.value.userName,
      userPhone: searchForm.value.userPhone
    };
    
    const res = await getDutyUserPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取值班人员列表失败:', error);
    ElMessage.error('获取值班人员列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchUserList();
};

// 处理重置
const handleReset = () => {
  searchForm.value = {
    userName: '',
    userPhone: ''
  };
  currentPage.value = 1;
  fetchUserList();
};

// 处理新增
const handleAdd = () => {
  formMode.value = 'add';
  formData.value = {};
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getDutyUserDetail(row.id);
    if (res && res.code === 200) {
      formMode.value = 'edit';
      formData.value = res.data;
      formDialogVisible.value = true;
    } else {
      ElMessage.error('获取人员详情失败');
    }
  } catch (error) {
    console.error('获取人员详情失败:', error);
    ElMessage.error('获取人员详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该值班人员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteDutyUser(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchUserList();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除值班人员失败:', error);
      ElMessage.error('删除值班人员失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 表单成功回调
const handleFormSuccess = () => {
  fetchUserList();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 监听对话框显示状态
const handleDialogOpen = () => {
  if (props.visible) {
    fetchUserList();
  }
};

// 组件挂载
onMounted(() => {
    fetchUserList();
});

// 监听visible变化
computed(() => {
  if (props.visible) {
    fetchUserList();
  }
});
</script>

<style scoped>
.duty-user-dialog {
  font-family: PingFangSC, PingFang SC;
}

.search-area {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-form {
  margin: 0;
}

.operation-area {
  margin-bottom: 16px;
}

.pagination-area {
  margin-top: 16px;
  text-align: right;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}
</style> 