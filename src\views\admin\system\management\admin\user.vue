<template>
  <div class="user-management-container">
    <div class="content-layout">
      <!-- 左侧部门树 -->
      <div class="left-panel">
        <div class="search-box">
          <el-input 
            v-model="deptSearchText" 
            placeholder="输入组织名称检索"
            @input="handleDeptSearch"
            clearable
          />
        </div>
        <div class="tree-container">
          <el-tree
            ref="deptTreeRef"
            :data="filteredDeptTree"
            :props="{ children: 'children', label: 'name' }"
            node-key="id"
            :default-expand-all="false"
            :expand-on-click-node="false"
            :highlight-current="true"
            @node-click="handleDeptNodeClick"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span class="tree-node-text">{{ node.label }}</span>
                <!-- <span class="node-count">{{ data.userCount || 0 }}</span> -->
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧用户列表 -->
      <div class="right-panel">
        <!-- 搜索区域 -->
        <div class="user-search">
          <div class="search-form">
            <div class="form-item">
              <span class="label">所属角色:</span>
              <el-select v-model="searchForm.roleId" class="form-input" placeholder="全部">
                <el-option label="全部" value="" />
                <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="form-item">
              <el-input v-model="searchForm.name" class="form-input" placeholder="输入用户名称或手机号搜索" />
            </div>
            <div class="form-item">
              <el-button type="primary" class="search-btn" @click="handleSearch">搜索</el-button>
              <el-button class="reset-btn" @click="handleReset">重置</el-button>
            </div>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="table-header">
          <div class="button-group">
            <el-button type="primary" class="operation-btn" @click="handleAdd">新增用户</el-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <div class="table-container">
          <el-table 
            :data="tableData" 
            style="width: 100%" 
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName"
            height="100%"
            empty-text="暂无数据"
          >
            <el-table-column label="序号" min-width="60">
              <template #default="{ $index }">
                {{ (currentPage - 1) * pageSize + $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" min-width="100" />
            <el-table-column prop="nickName" label="用户名" min-width="120" />
            <el-table-column prop="major" label="职务" min-width="120" />
            <el-table-column prop="mobile" label="联系电话" min-width="130" />
            <el-table-column prop="deptName" label="所属部门" min-width="120" />
            <el-table-column prop="roleName" label="所属角色" min-width="120" />
            <el-table-column prop="belongProject" label="所属单位" min-width="180" />
            <el-table-column label="状态" min-width="80">
              <template #default="{ row }">
                <el-switch
                  v-model="row.state"
                  :active-value="'0'"
                  :inactive-value="'1'"
                  @change="handleStateChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" min-width="280">
              <template #default="{ row }">
                <div class="operation-btns">
                  <el-button type="primary" link @click="handleDetail(row)">查看</el-button>
                  <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                  <el-button type="primary" link @click="handleResetPassword(row)">重置密码</el-button>
                  <el-button type="primary" link @click="handleDelete(row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 用户弹窗 -->
    <UserDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      :selected-dept-id="selectedDeptId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getUserPage,
  getDeptTree,
  getAllRoles,
  getUserDetail,
  deleteUser,
  resetUserPassword,
  saveUser
} from '@/api/system';
import UserDialog from './components/UserDialog.vue';

// 部门相关
const deptTreeRef = ref(null);
const deptTree = ref([]);
const deptSearchText = ref('');
const selectedDeptId = ref('');

// 搜索表单
const searchForm = ref({
  roleId: '',
  name: ''
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项
const roleOptions = ref([]);

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 过滤后的部门树（用于搜索）
const filteredDeptTree = computed(() => {
  if (!deptSearchText.value) {
    return deptTree.value;
  }
  return filterDeptTree(deptTree.value, deptSearchText.value);
});

// 部门树搜索过滤
const filterDeptTree = (tree, searchText) => {
  const filtered = [];
  
  tree.forEach(node => {
    if (node.deptName.includes(searchText)) {
      filtered.push({ ...node });
    } else if (node.children && node.children.length > 0) {
      const filteredChildren = filterDeptTree(node.children, searchText);
      if (filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren
        });
      }
    }
  });
  
  return filtered;
};

// 处理部门搜索
const handleDeptSearch = () => {
  // 搜索逻辑已在computed中处理
};

// 处理部门节点点击
const handleDeptNodeClick = (data) => {
  selectedDeptId.value = data.id;
  fetchUserData();
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchUserData();
};

// 处理重置
const handleReset = () => {
  searchForm.value = {
    roleId: '',
    name: ''
  };
  currentPage.value = 1;
  fetchUserData();
};

// 获取用户分页数据
const fetchUserData = async () => {
  try {
    const params = {
      deptId: selectedDeptId.value,
      roleId: searchForm.value.roleId,
      name: searchForm.value.name
    };
    
    const res = await getUserPage(currentPage.value, pageSize.value, params);
    
    if (res && res.status === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
      
      // 处理用户数据，确保角色名称显示正确
      tableData.value = tableData.value.map(user => ({
        ...user,
        roleName: user.role && user.role.length > 0 ? user.role[0].name : '',
        state: user.state?.toString() || '0'
      }));
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    ElMessage.error('获取用户数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree();
    if (res && res.data) {
      deptTree.value = res.data;
      // 默认选中第一个顶级部门
      if (deptTree.value.length > 0) {
        selectedDeptId.value = deptTree.value[0].id;
        // 设置默认选中状态
        setTimeout(() => {
          if (deptTreeRef.value) {
            deptTreeRef.value.setCurrentKey(selectedDeptId.value);
          }
        }, 100);
      }
    }
  } catch (error) {
    console.error('获取部门树失败', error);
  }
};

// 获取角色列表
const fetchRoles = async () => {
  try {
    const res = await getAllRoles();
    if (res && res.data) {
      roleOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取角色列表失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchUserData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchUserData();
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getUserDetail(row.id);
    if (res && res.status === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取用户详情失败');
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('获取用户详情失败');
  }
};

// 处理查看详情
const handleDetail = async (row) => {
  try {
    const res = await getUserDetail(row.id);
    if (res && res.status === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取用户详情失败');
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('获取用户详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该用户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteUser(row.id);
      if (res && res.status === 200) {
        ElMessage.success('删除成功');
        fetchUserData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      ElMessage.error('删除用户失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理重置密码
const handleResetPassword = (row) => {
  ElMessageBox.confirm('确定要重置该用户的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await resetUserPassword(row.id);
      if (res && res.status === 200) {
        ElMessage.success('密码重置成功');
      } else {
        ElMessage.error(res?.msg || '密码重置失败');
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      ElMessage.error('重置密码失败');
    }
  }).catch(() => {
    // 取消重置
  });
};

// 处理状态变化
const handleStateChange = async (row) => {
  try {
    const userData = { ...row };
    const res = await saveUser(userData);
    if (res && res.status === 200) {
      ElMessage.success(row.state === '0' ? '启用成功' : '停用成功');
    } else {
      // 状态变更失败，恢复原状态
      row.state = row.state === '0' ? '1' : '0';
      ElMessage.error(res?.msg || '状态变更失败');
    }
  } catch (error) {
    // 状态变更失败，恢复原状态
    row.state = row.state === '0' ? '1' : '0';
    console.error('状态变更失败:', error);
    ElMessage.error('状态变更失败');
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchUserData();
};

// 组件挂载时初始化数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchDeptTree(),
      fetchRoles()
    ]);
    // 等待部门树加载完成后再获取用户数据
    setTimeout(() => {
      fetchUserData();
    }, 200);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.user-management-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.content-layout {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  min-height: 0;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  background: white;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.search-box {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.tree-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-node-text {
  color: #333;
}

.node-count {
  color: #999;
  font-size: 12px;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 搜索区域样式 */
.user-search {
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 树组件样式优化 */
:deep(.el-tree-node__content) {
  height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #e3f2fd;
  color: #0277FD;
}
</style> 