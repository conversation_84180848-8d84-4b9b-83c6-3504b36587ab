<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="plan-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预案名称" prop="schemeName">
            <el-input v-model="formData.schemeName" placeholder="请输入预案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件分类" prop="eventType">
            <el-select v-model="formData.eventType" placeholder="请选择" class="w-full" @change="handleEventTypeChange">
              <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="响应等级" prop="responseLevel">
            <el-select v-model="formData.responseLevel" placeholder="请选择" class="w-full" @change="handleResponseLevelChange">
              <el-option v-for="item in responseLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预案级别" prop="schemeLevel">
            <el-select v-model="formData.schemeLevel" placeholder="请选择" class="w-full" @change="handleSchemeLevelChange">
              <el-option v-for="item in schemeLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="预案分类" prop="schemeClassification">
            <el-select v-model="formData.schemeClassification" placeholder="请选择" class="w-full">
              <el-option v-for="item in schemeClassificationOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="制定单位" prop="drawupUnit">
            <el-input v-model="formData.drawupUnit" placeholder="请输入制定单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="下发部门" prop="issuedUnit">
            <el-select v-model="formData.issuedUnit" placeholder="请选择" class="w-full" @change="handleIssuedUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关键词" prop="keyword">
            <el-input v-model="formData.keyword" placeholder="请输入关键词" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="fileUrls">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              list-type="text"
              :limit="10"
              :disabled="mode === 'view'"
              multiple
            >
              <el-button type="primary" v-if="mode !== 'view'">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip text-red">
                  支持doc、docx、pdf等文件上传，多个文件，大小不能超过100M
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencyPlan,
  updateEmergencyPlan,
  getAllEnterpriseList
} from '@/api/heating';
import { 
  EVENT_TYPE_OPTIONS, 
  RESPONSE_LEVEL_OPTIONS, 
  SCHEME_LEVEL_OPTIONS,
  SCHEME_CLASSIFICATION_OPTIONS
} from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import { uploadFile } from '@/api/upload';
import bus from '@/utils/mitt';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);
const fileList = ref([]);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增预案',
    edit: '编辑预案',
    view: '预案详情'
  };
  return titles[props.mode] || '预案信息';
});

// 下拉选项数据
const eventTypeOptions = ref(EVENT_TYPE_OPTIONS);
const responseLevelOptions = ref(RESPONSE_LEVEL_OPTIONS);
const schemeLevelOptions = ref(SCHEME_LEVEL_OPTIONS);
const schemeClassificationOptions = ref(SCHEME_CLASSIFICATION_OPTIONS);
const enterpriseOptions = ref([]);
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  schemeName: '',
  eventType: '',
  eventTypeName: '',
  responseLevel: '',
  responseLevelName: '',
  schemeLevel: '',
  schemeLevelName: '',
  schemeClassification: '',
  drawupUnit: '',
  issuedUnit: '',
  issuedUnitName: '',
  keyword: '',
  address: '',
  longitude: '',
  latitude: '',
  fileUrls: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  schemeName: [{ required: true, message: '请输入预案名称', trigger: 'blur' }],
  eventType: [{ required: true, message: '请选择事件分类', trigger: 'change' }],
  responseLevel: [{ required: true, message: '请选择响应等级', trigger: 'change' }],
  schemeLevel: [{ required: true, message: '请选择预案级别', trigger: 'change' }],
  schemeClassification: [{ required: true, message: '请选择预案分类', trigger: 'change' }],
  drawupUnit: [{ required: true, message: '请输入制定单位', trigger: 'blur' }],
  issuedUnit: [{ required: true, message: '请选择下发部门', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  fileList.value = [];
};

// 处理事件分类变化
const handleEventTypeChange = (value) => {
  const selected = eventTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.eventTypeName = selected.label;
  }
};

// 处理响应等级变化
const handleResponseLevelChange = (value) => {
  const selected = responseLevelOptions.value.find(item => item.value === value);
  if (selected) {
    formData.responseLevelName = selected.label;
  }
};

// 处理预案级别变化
const handleSchemeLevelChange = (value) => {
  const selected = schemeLevelOptions.value.find(item => item.value === value);
  if (selected) {
    formData.schemeLevelName = selected.label;
  }
};

// 处理下发部门变化
const handleIssuedUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.value === value);
  if (selected) {
    formData.issuedUnitName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.error('上传文件大小不能超过 100MB!');
    return;
  }

  // 检查文件类型
  const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  const isAllowedType = allowedTypes.includes(file.raw.type) || file.name.toLowerCase().endsWith('.doc') || file.name.toLowerCase().endsWith('.docx') || file.name.toLowerCase().endsWith('.pdf');
  if (!isAllowedType) {
    ElMessage.error('只支持上传 doc、docx、pdf 格式的文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.fileUrls ? formData.fileUrls.split(',') : [];
      urls.push(response.data.url);
      formData.fileUrls = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 文件移除处理
const handleFileRemove = (file, fileList) => {
  if (formData.fileUrls) {
    const urls = formData.fileUrls.split(',');
    const index = urls.findIndex(url => url.includes(file.name) || file.url === url);
    if (index > -1) {
      urls.splice(index, 1);
      formData.fileUrls = urls.join(',');
    }
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 获取企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getAllEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      }));
    }
  } catch (error) {
    console.error('获取企业列表失败', error);
  }
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 事件分类
  const selectedEventType = eventTypeOptions.value.find(item => item.value === formData.eventType);
  if (selectedEventType) {
    formData.eventTypeName = selectedEventType.label;
  }

  // 响应等级
  const selectedResponseLevel = responseLevelOptions.value.find(item => item.value === formData.responseLevel);
  if (selectedResponseLevel) {
    formData.responseLevelName = selectedResponseLevel.label;
  }

  // 预案级别
  const selectedSchemeLevel = schemeLevelOptions.value.find(item => item.value === formData.schemeLevel);
  if (selectedSchemeLevel) {
    formData.schemeLevelName = selectedSchemeLevel.label;
  }

  // 下发部门
  const selectedEnterprise = enterpriseOptions.value.find(item => item.value === formData.issuedUnit);
  if (selectedEnterprise) {
    formData.issuedUnitName = selectedEnterprise.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 处理文件显示
    if (newVal.fileUrls) {
      fileList.value = newVal.fileUrls.split(',').map((url, index) => ({
        name: `file_${index}`,
        url: url,
        uid: Date.now() + index
      }));
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencyPlan(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencyPlan(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
});
</script>

<style scoped>
.plan-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}

.text-red {
  color: #ff4d4f;
}
</style> 