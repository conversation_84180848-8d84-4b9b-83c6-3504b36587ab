<template>
  <div class="expert-container">
    <el-tabs v-model="activeTab" class="expert-tabs">
      <el-tab-pane label="专家申请" name="apply">
        <ExpertApply />
      </el-tab-pane>
      <el-tab-pane label="专家咨询" name="consult">
        <ExpertConsult />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ExpertApply from './components/ExpertApply.vue'
import ExpertConsult from './components/ExpertConsult.vue'

// 当前激活的选项卡
const activeTab = ref('apply')

onMounted(() => {
  console.log('专家咨询组件已挂载')
})
</script>

<style scoped>
.expert-container {
  padding: 0;
}

.expert-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}
</style>