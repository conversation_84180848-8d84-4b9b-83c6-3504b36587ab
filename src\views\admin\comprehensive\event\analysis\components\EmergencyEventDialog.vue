<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="emergency-event-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件标题" prop="eventTitle">
            <el-input v-model="formData.eventTitle" placeholder="请输入事件标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件编号" prop="eventCode">
            <el-input v-model="formData.eventCode" placeholder="请输入事件编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件分级" prop="eventLevel">
            <el-select v-model="formData.eventLevel" placeholder="请选择" class="w-full" @change="handleEventLevelChange">
              <el-option v-for="item in eventLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事件来源" prop="eventSource">
            <el-select v-model="formData.eventSource" placeholder="请选择" class="w-full" @change="handleEventSourceChange">
              <el-option v-for="item in eventSourceOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事件分类" prop="eventType">
            <el-select v-model="formData.eventType" placeholder="请选择" class="w-full" @change="handleEventTypeChange">
              <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="处置状态" prop="eventStatus">
            <el-select v-model="formData.eventStatus" placeholder="请选择" class="w-full" @change="handleEventStatusChange">
              <el-option v-for="item in eventStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="处置单位" prop="ownershipUnit">
            <el-tree-select
              v-model="formData.ownershipUnit"
              :data="deptOptions"
              :props="{
                value: 'id',
                label: 'deptName',
                children: 'children'
              }"
              placeholder="请选择处置单位"
              class="w-full"
              @change="handleDeptChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="事件时间" prop="eventTime">
            <el-date-picker
              v-model="formData.eventTime"
              type="datetime"
              placeholder="选择日期时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="接收时间" prop="receiveTime">
            <el-date-picker
              v-model="formData.receiveTime"
              type="datetime"
              placeholder="选择日期时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处置时间" prop="handleTime">
            <el-date-picker
              v-model="formData.handleTime"
              type="datetime"
              placeholder="选择日期时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="是否人员伤亡" prop="isCasualty">
            <el-select v-model="formData.isCasualty" placeholder="请选择" class="w-full">
              <el-option v-for="item in isCasualtyOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系方式" prop="contactInfo">
            <el-input v-model="formData.contactInfo" placeholder="请输入联系方式" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="formData.isCasualty">
        <el-col :span="12">
          <el-form-item label="死亡人数" prop="deathNum">
            <el-input-number v-model="formData.deathNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="受伤人数" prop="injuredNum">
            <el-input-number v-model="formData.injuredNum" :min="0" class="w-full" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="事件描述">
            <el-input v-model="formData.eventDesc" type="textarea" :rows="3" placeholder="请输入事件描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remarks" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveEmergencyEvent,
  updateEmergencyEvent
} from '@/api/comprehensive';
import { getDeptListTree } from '@/api/system';
import {
  EVENT_LEVEL_OPTIONS,
  EVENT_SOURCE_OPTIONS,
  EVENT_TYPE_OPTIONS,
  EVENT_STATUS_OPTIONS,
  RELATED_BUSINESS_OPTIONS,
  IS_CASUALTY_OPTIONS
} from '@/constants/comprehensive';
import { AREA_OPTIONS } from '@/constants/gas';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';

// 使用从常量文件导入的选项
const eventLevelOptions = EVENT_LEVEL_OPTIONS;
const eventSourceOptions = EVENT_SOURCE_OPTIONS;
const eventTypeOptions = EVENT_TYPE_OPTIONS;
const eventStatusOptions = EVENT_STATUS_OPTIONS;
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS;
const isCasualtyOptions = IS_CASUALTY_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增应急事件',
    edit: '编辑应急事件',
    view: '应急事件详情'
  };
  return titles[props.mode] || '应急事件';
});

// 下拉选项数据
const deptOptions = ref([]);

// 行政区划选项
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  eventTitle: '',
  eventCode: '',
  eventDesc: '',
  eventLevel: '',
  eventLevelName: '',
  eventSource: '',
  eventSourceName: '',
  eventType: '',
  eventTypeName: '',
  eventStatus: '',
  eventStatusName: '',
  eventTime: '',
  receiveTime: '',
  handleTime: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  contactInfo: '',
  isCasualty: false,
  deathNum: 0,
  injuredNum: 0,
  address: '',
  longitude: '',
  latitude: '',
  remarks: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: '',
  descOrName: ''
});

// 表单验证规则
const formRules = {
  eventTitle: [{ required: true, message: '请输入事件标题', trigger: 'blur' }],
  eventCode: [{ required: true, message: '请输入事件编号', trigger: 'blur' }],
  eventLevel: [{ required: true, message: '请选择事件分级', trigger: 'change' }],
  eventSource: [{ required: true, message: '请选择事件来源', trigger: 'change' }],
  eventType: [{ required: true, message: '请选择事件分类', trigger: 'change' }],
  eventStatus: [{ required: true, message: '请选择处置状态', trigger: 'change' }],
  relatedBusiness: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  ownershipUnit: [{ required: true, message: '请选择处置单位', trigger: 'change' }],
  eventTime: [{ required: true, message: '请选择事件时间', trigger: 'change' }],
  receiveTime: [{ required: true, message: '请选择接收时间', trigger: 'change' }],
  contactInfo: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  isCasualty: [{ required: true, message: '请选择是否人员伤亡', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'deathNum' || key === 'injuredNum') {
      formData[key] = 0;
    } else if (key === 'isCasualty') {
      formData[key] = false;
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 事件分级
  const selectedEventLevel = eventLevelOptions.find(item => item.value === formData.eventLevel);
  if (selectedEventLevel) {
    formData.eventLevelName = selectedEventLevel.label;
  }

  // 事件来源
  const selectedEventSource = eventSourceOptions.find(item => item.value === formData.eventSource);
  if (selectedEventSource) {
    formData.eventSourceName = selectedEventSource.label;
  }

  // 事件分类
  const selectedEventType = eventTypeOptions.find(item => item.value === formData.eventType);
  if (selectedEventType) {
    formData.eventTypeName = selectedEventType.label;
  }

  // 处置状态
  const selectedEventStatus = eventStatusOptions.find(item => item.value === formData.eventStatus);
  if (selectedEventStatus) {
    formData.eventStatusName = selectedEventStatus.label;
  }

  // 所属行业
  const selectedRelatedBusiness = relatedBusinessOptions.find(item => item.value === formData.relatedBusiness);
  if (selectedRelatedBusiness) {
    formData.relatedBusinessName = selectedRelatedBusiness.label;
  }

  // 处置单位
  const selectedDept = findDeptById(deptOptions.value, formData.ownershipUnit);
  if (selectedDept) {
    formData.ownershipUnitName = selectedDept.deptName;
  }
};

// 根据部门ID查找部门信息
const findDeptById = (depts, id) => {
  for (const dept of depts) {
    if (dept.id === id) {
      return dept;
    }
    if (dept.children) {
      const found = findDeptById(dept.children, id);
      if (found) return found;
    }
  }
  return null;
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理事件分级变化
const handleEventLevelChange = (value) => {
  const selected = eventLevelOptions.find(item => item.value === value);
  if (selected) {
    formData.eventLevelName = selected.label;
  }
};

// 处理事件来源变化
const handleEventSourceChange = (value) => {
  const selected = eventSourceOptions.find(item => item.value === value);
  if (selected) {
    formData.eventSourceName = selected.label;
  }
};

// 处理事件分类变化
const handleEventTypeChange = (value) => {
  const selected = eventTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.eventTypeName = selected.label;
  }
};

// 处理处置状态变化
const handleEventStatusChange = (value) => {
  const selected = eventStatusOptions.find(item => item.value === value);
  if (selected) {
    formData.eventStatusName = selected.label;
  }
};

// 处理所属行业变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.find(item => item.value === value);
  if (selected) {
    formData.relatedBusinessName = selected.label;
  }
};

// 处理部门变化
const handleDeptChange = (value) => {
  const selected = findDeptById(deptOptions.value, value);
  if (selected) {
    formData.ownershipUnitName = selected.deptName;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 获取部门树
const fetchDeptTree = async () => {
  try {
    const res = await getDeptListTree({});
    if (res && res.data) {
      deptOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取部门树失败', error);
  }
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };
    // 设置描述或名称字段
    submitData.descOrName = submitData.eventTitle;

    let res;
    if (props.mode === 'add') {
      res = await saveEmergencyEvent(submitData);
    } else if (props.mode === 'edit') {
      res = await updateEmergencyEvent(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});

// 组件挂载时获取数据
onMounted(() => {
  fetchDeptTree();
});
</script>

<style scoped>
.emergency-event-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}
</style> 