<template>
  <div class="heating-hazard-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 日期筛选区域 -->
    <div class="filter-section">
      <div class="date-filter">
        <label class="filter-label">日期：</label>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange"
          class="date-picker" />
        <el-button type="primary" :class="{ active: quickDateType === 'recent7' }" @click="setQuickDate('recent7')"
          class="quick-btn">
          近7日
        </el-button>
        <el-button type="primary" :class="{ active: quickDateType === 'recent30' }" @click="setQuickDate('recent30')"
          class="quick-btn">
          最近30天
        </el-button>
        <el-button type="success" @click="exportPage" class="export-btn" :loading="exportLoading">
          导出
        </el-button>
      </div>
    </div>

    <!-- 内容区域 - 用于截图 -->
    <div ref="captureRef" class="capture-content">
      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <!-- 隐患数量统计 -->
        <div class="stats-row">
          <!-- 左侧圆形总数卡片 -->
          <div class="total-stats">
            <div class="total-card">
              <div class="total-number">{{ dangerStatistics.totalCount || 0 }}</div>
              <div class="total-label">全部隐患</div>
            </div>
          </div>

          <!-- 右侧状态卡片网格 -->
          <div class="status-stats">
            <div class="status-card unRectify">
              <div class="status-header">
                <h4>未整改</h4>
              </div>
              <div class="status-content">
                <div class="status-number">{{ dangerStatistics.unRectifyCount || 0 }}</div>
                <div class="status-rate">{{ dangerStatistics.unRectifyRate || '0%' }}</div>
              </div>
            </div>

            <div class="status-card rectifying">
              <div class="status-header">
                <h4>整改中</h4>
              </div>
              <div class="status-content">
                <div class="status-number">{{ dangerStatistics.rectifyingCount || 0 }}</div>
                <div class="status-rate">{{ dangerStatistics.rectifyingRate || '0%' }}</div>
              </div>
            </div>

            <div class="status-card review">
              <div class="status-header">
                <h4>待复查</h4>
              </div>
              <div class="status-content">
                <div class="status-number">{{ dangerStatistics.reviewCount || 0 }}</div>
                <div class="status-rate">{{ dangerStatistics.reviewRate || '0%' }}</div>
              </div>
            </div>

            <div class="status-card rectified">
              <div class="status-header">
                <h4>已整改</h4>
              </div>
              <div class="status-content">
                <div class="status-number">{{ dangerStatistics.rectifiedCount || 0 }}</div>
                <div class="status-rate">{{ dangerStatistics.rectifiedRate || '0%' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 隐患趋势图 -->
        <div class="chart-card trend-chart">
          <div class="chart-header">
            <h3>隐患趋势</h3>
          </div>
          <div class="chart-content">
            <div ref="trendChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 核心指标 -->
        <div class="chart-card core-indicators">
          <div class="chart-header">
            <h3>核心指标</h3>
          </div>
          <div class="indicators-content">
            <div class="indicator-item">
              <div class="completion-chart">
                <div class="chart-container">
                  <div class="progress-circle" :style="{ '--progress': completionProgress }">
                    <span class="progress-text">{{ coreStatistics.completionRate || '20%' }}</span>
                  </div>
                </div>
                <div class="chart-info">
                  <h4>整改完成率</h4>
                  <p class="trend-text">环比 {{ coreStatistics.completionAnalysis || '下降20%' }}</p>
                </div>
              </div>
            </div>

            <div class="indicator-item">
              <div class="time-display">
                <div class="time-number">{{ formatDuration(coreStatistics.avgHandlingDuration) }}</div>
                <div class="time-label">平均整改时长</div>
                <div class="time-trend">环比 {{ coreStatistics.avgHandlingDurationAnalysis || '上升20%' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="tables-section">
        <!-- 隐患等级统计图 -->
        <div class="table-card level-chart">
          <div class="table-header">
            <h3>隐患等级</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color total"></span>
                总数
              </span>
              <span class="legend-item">
                <span class="legend-color rectified"></span>
                已整改
              </span>
              <span class="legend-item">
                <span class="legend-color rate-line"></span>
                占比
              </span>
            </div>
          </div>
          <div class="table-content">
            <div ref="levelChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 隐患类型表格 -->
        <div class="table-card">
          <div class="table-header">
            <h3>隐患类型</h3>
          </div>
          <div class="table-content">
            <el-table :data="typeTableData" stripe class="type-table" v-loading="typeLoading">
              <el-table-column prop="index" label="排序" width="60" align="center">
                <template #default="{ $index }">
                  <span class="rank-number">{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="dangerTypeName" label="类型名称" min-width="150" show-overflow-tooltip />
              <el-table-column prop="totalCount" label="隐患总数" width="100" align="center" />
              <el-table-column prop="rectifiedCount" label="已整改" width="100" align="center" />
              <el-table-column label="整改完成率" width="120" align="center">
                <template #default="{ row }">
                  <div class="completion-rate-cell">
                    <span class="completion-rate">{{ row.rectifiedRate || '0' }}%</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 企业隐患表格 -->
      <div class="enterprise-section">
        <div class="table-card">
          <div class="table-header">
            <h3>企业隐患</h3>
          </div>
          <div class="table-content">
            <el-table :data="enterpriseTableData" stripe class="enterprise-table" v-loading="enterpriseLoading">
              <el-table-column prop="index" label="排序" width="60" align="center">
                <template #default="{ $index }">
                  <span class="rank-number">{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="enterpriseName" label="企业名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="totalCount" label="隐患总数" width="100" align="center" />
              <el-table-column prop="rectifiedCount" label="已整改" width="100" align="center" />
              <el-table-column label="整改完成率" width="120" align="center">
                <template #default="{ row }">
                  <div class="completion-rate-cell">
                    <span class="completion-rate">{{ row.rectifiedRate || '0' }}%</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import moment from 'moment'
import html2canvas from 'html2canvas'
import {
  getHeatDangerStatistics,
  getHeatDangerTrendStatistics,
  getHeatDangerCoreStatistics,
  getHeatDangerLevelStatistics,
  getHeatDangerTypeStatistics,
  getHeatDangerEnterpriseStatistics
} from '@/api/heating'

// 响应式数据
const dateRange = ref([])
const quickDateType = ref('recent7')
const loading = ref(false)
const typeLoading = ref(false)
const enterpriseLoading = ref(false)
const exportLoading = ref(false)

// 统计数据
const dangerStatistics = reactive({
  totalCount: 0,
  rectifiedCount: 0,
  rectifiedRate: '0%',
  unRectifyCount: 0,
  unRectifyRate: '0%',
  rectifyingCount: 0,
  rectifyingRate: '0%',
  reviewCount: 0,
  reviewRate: '0%'
})

const coreStatistics = reactive({
  completionRate: '20%',
  avgHandlingDuration: '3小时5分钟',
  completionAnalysis: '下降20%',
  completionAnalysisTrend: 'up',
  avgHandlingDurationAnalysis: '上升20%',
  avgHandlingDurationAnalysisTrend: 'up'
})

const typeTableData = ref([])
const enterpriseTableData = ref([])

// 图表引用
const trendChartRef = ref(null)
const levelChartRef = ref(null)
const captureRef = ref(null)
let trendChart = null
let levelChart = null

// 计算属性
const completionProgress = computed(() => {
  const rate = parseFloat(coreStatistics.completionRate) || 20
  return Math.min(rate, 100)
})

// 方法
const formatDuration = (duration) => {
  if (!duration) return '3小时5分钟'
  return duration
}

const setQuickDate = (type) => {
  quickDateType.value = type
  const today = moment()

  if (type === 'recent7') {
    dateRange.value = [
      today.clone().subtract(6, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  } else if (type === 'recent30') {
    dateRange.value = [
      today.clone().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  }

  loadAllData()
}

const handleDateChange = () => {
  quickDateType.value = ''
  loadAllData()
}

const getDateParams = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return {}
  }
  return {
    startDate: dateRange.value[0],
    endDate: dateRange.value[1]
  }
}

// 页面截图导出功能
const exportPage = async () => {
  if (!captureRef.value) {
    ElMessage.error('页面元素未准备就绪')
    return
  }

  exportLoading.value = true

  try {
    const canvas = await html2canvas(captureRef.value, {
      backgroundColor: '#f5f7fa',
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: false
    })

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `供热隐患态势数据分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 加载隐患统计数据
const loadDangerStatistics = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatDangerStatistics(params)
    if (response.code === 200 && response.data) {
      Object.assign(dangerStatistics, response.data)
    }
  } catch (error) {
    console.error('加载隐患统计数据失败:', error)
    ElMessage.error('加载隐患统计数据失败')
  }
}

// 加载核心指标数据
const loadCoreStatistics = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatDangerCoreStatistics(params)
    if (response.code === 200 && response.data) {
      Object.assign(coreStatistics, response.data)
    }
  } catch (error) {
    console.error('加载核心指标数据失败:', error)
    ElMessage.error('加载核心指标数据失败')
  }
}

// 加载趋势图表数据
const loadTrendChart = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatDangerTrendStatistics(params)
    if (response.code === 200 && response.data) {
      renderTrendChart(response.data)
    }
  } catch (error) {
    console.error('加载趋势图表数据失败:', error)
    ElMessage.error('加载趋势图表数据失败')
  }
}

// 加载等级图表数据
const loadLevelChart = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatDangerLevelStatistics(params)
    if (response.code === 200 && response.data) {
      renderLevelChart(response.data)
    }
  } catch (error) {
    console.error('加载等级图表数据失败:', error)
    ElMessage.error('加载等级图表数据失败')
  }
}

// 加载类型表格数据
const loadTypeTable = async () => {
  try {
    typeLoading.value = true
    const params = getDateParams()
    const response = await getHeatDangerTypeStatistics(params)
    if (response.code === 200 && response.data) {
      typeTableData.value = response.data.slice(0, 5) // 只显示前5条
    }
  } catch (error) {
    console.error('加载类型表格数据失败:', error)
    ElMessage.error('加载类型表格数据失败')
  } finally {
    typeLoading.value = false
  }
}

// 加载企业表格数据
const loadEnterpriseTable = async () => {
  try {
    enterpriseLoading.value = true
    const params = getDateParams()
    const response = await getHeatDangerEnterpriseStatistics(1, 3, params)
    if (response.code === 200 && response.data && response.data.records) {
      enterpriseTableData.value = response.data.records
    }
  } catch (error) {
    console.error('加载企业表格数据失败:', error)
    ElMessage.error('加载企业表格数据失败')
  } finally {
    enterpriseLoading.value = false
  }
}

// 渲染趋势图表
const renderTrendChart = (data) => {
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }

  const dates = data.map(item => moment(item.date).format('MM/DD'))
  const counts = data.map(item => item.totalCount || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: [
      {
        name: '隐患总数',
        type: 'line',
        data: counts,
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 渲染等级图表
const renderLevelChart = (data) => {
  if (!levelChart) {
    levelChart = echarts.init(levelChartRef.value)
  }

  const categories = data.map(item => item.dangerLevelName || '未知等级')
  const totalData = data.map(item => item.totalCount || 0)
  const rectifiedData = data.map(item => item.rectifiedCount || 0)
  const rateData = data.map(item => parseFloat(item.rate) || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5'
          }
        }
      },
      {
        type: 'value',
        name: '占比(%)',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总数',
        type: 'bar',
        data: totalData,
        itemStyle: {
          color: '#5dade2'
        },
        barWidth: '20%'
      },
      {
        name: '已整改',
        type: 'bar',
        data: rectifiedData,
        itemStyle: {
          color: '#26de81'
        },
        barWidth: '20%'
      },
      {
        name: '占比',
        type: 'line',
        yAxisIndex: 1,
        data: rateData,
        lineStyle: {
          color: '#ff6b6b'
        },
        itemStyle: {
          color: '#ff6b6b'
        }
      }
    ]
  }

  levelChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
  if (levelChartRef.value) {
    levelChart = echarts.init(levelChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    levelChart?.resize()
  })
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadDangerStatistics(),
      loadCoreStatistics(),
      loadTrendChart(),
      loadLevelChart(),
      loadTypeTable(),
      loadEnterpriseTable()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 设置默认日期为近7天
  setQuickDate('recent7')

  // 初始化图表
  await initCharts()

  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (levelChart) {
    levelChart.dispose()
    levelChart = null
  }
  window.removeEventListener('resize', () => {
    trendChart?.resize()
    levelChart?.resize()
  })
})
</script>

<style scoped>
.heating-hazard-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 280px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.date-picker {
  width: 300px;
}

.quick-btn {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.quick-btn.active {
  background: #409eff;
  color: white;
}

.export-btn {
  margin-left: auto;
}

/* 截图内容区域 */
.capture-content {
  background-color: #f5f7fa;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
}

/* 总数卡片 */
.total-stats {
  flex: 0 0 200px;
}

.total-card {
  background: white;
  border-radius: 50%;
  width: 180px;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  color: white;
}

.total-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 8px;
}

.total-label {
  font-size: 16px;
  opacity: 0.9;
}

/* 状态卡片网格 */
.status-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 1;
  background: #e4e7ed;
}

.status-card {
  background: white;
  padding: 20px;
  color: white;
}

.status-card.unRectify {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.status-card.rectifying {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.status-card.review {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.status-card.rectified {
  background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
}

.status-header h4 {
  font-size: 14px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.status-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.status-rate {
  font-size: 14px;
  opacity: 0.8;
}

/* 图表区域 */
.charts-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.total {
  background: #5dade2;
}

.legend-color.rectified {
  background: #26de81;
}

.legend-color.rate-line {
  background: #ff6b6b;
}

.chart-content .chart-container {
  height: 300px;
  min-height: 250px;
}

/* 核心指标区域 */
.indicators-content {
  display: flex;
  gap: 20px;
}

.indicator-item {
  flex: 1;
}

.completion-chart {
  display: flex;
  align-items: center;
  gap: 20px;
}

.chart-container {
  flex-shrink: 0;
}

.progress-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(#409eff 0deg, #409eff calc(var(--progress) * 3.6deg), #e4e7ed calc(var(--progress) * 3.6deg), #e4e7ed 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: white;
  position: absolute;
}

.progress-text {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  position: relative;
  z-index: 1;
}

.chart-info h4 {
  font-size: 16px;
  color: #303133;
  margin: 0 0 8px 0;
}

.trend-text {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.time-display {
  text-align: center;
}

.time-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.time-label {
  font-size: 16px;
  color: #303133;
  margin-bottom: 5px;
}

.time-trend {
  font-size: 14px;
  color: #909399;
}

/* 表格区域 */
.tables-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.enterprise-section {
  margin-bottom: 20px;
}

.table-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 20px 20px 0 20px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.table-content {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.rank-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background: #f0f2f5;
  color: #606266;
  font-weight: bold;
}

.completion-rate-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.completion-rate {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  background: #52c41a;
  color: white;
  min-width: 30px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-section {
    flex-direction: column;
  }

  .tables-section {
    flex-direction: column;
  }

  .chart-content .chart-container {
    height: 280px;
  }
}

@media (max-width: 1200px) {
  .stats-row {
    flex-direction: column;
  }

  .status-stats {
    grid-template-columns: repeat(4, 1fr);
  }

  .indicators-content {
    flex-direction: column;
    align-items: center;
  }

  .chart-content .chart-container {
    height: 260px;
  }
}

@media (max-height: 1050px) {
  .heating-hazard-analysis-container {
    padding: 15px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 300px);
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .chart-content .chart-container {
    height: 240px;
  }
}

@media (max-height: 800px) {
  .heating-hazard-analysis-container {
    padding: 10px;
  }

  .chart-content .chart-container {
    height: 220px;
  }

  .table-card {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
  }

  .status-stats {
    grid-template-columns: 1fr;
  }

  .date-filter {
    flex-wrap: wrap;
  }

  .chart-legend {
    flex-direction: column;
    gap: 10px;
  }

  .chart-content .chart-container {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .heating-hazard-analysis-container {
    padding: 10px;
  }

  .date-picker {
    width: 100%;
  }

  .quick-btn {
    flex: 1;
  }

  .export-btn {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }

  .chart-content .chart-container {
    height: 180px;
  }

  .total-card {
    width: 150px;
    height: 150px;
  }

  .total-number {
    font-size: 28px;
  }
}

/* 表格优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 