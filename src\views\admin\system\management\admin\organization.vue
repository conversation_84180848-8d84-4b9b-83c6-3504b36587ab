<template>
  <div class="organization-container">
    <!-- 搜索区域 -->
    <div class="organization-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属角色:</span>
          <el-select v-model="formData.roleId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.userName" class="form-input" placeholder="输入用户名称或姓名搜索" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- 左侧部门树 -->
      <div class="left-panel">
        <div class="panel-header">
          <el-button type="primary" size="small" class="add-dept-btn" @click="handleAddRootDept">
            新增单位
          </el-button>
        </div>
        <div class="tree-container">
          <el-tree
            ref="deptTreeRef"
            :data="deptTreeData"
            :props="deptTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            :highlight-current="true"
            @node-click="handleDeptClick"
            class="dept-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ node.label }}</span>
                <div class="node-actions">
                  <el-button 
                    type="primary" 
                    link 
                    size="small" 
                    @click.stop="handleAddChildDept(data)"
                    title="新增子部门"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-button>
                  <el-button 
                    type="primary" 
                    link 
                    size="small" 
                    @click.stop="handleEditDept(data)"
                    title="编辑"
                  >
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button 
                    type="danger" 
                    link 
                    size="small" 
                    @click.stop="handleDeleteDept(data)"
                    title="删除"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      
      <!-- 右侧用户列表 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>{{ selectedDeptName }}用户列表</h3>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-container">
          <el-table 
            :data="tableData" 
            style="width: 100%" 
            :header-cell-style="headerCellStyle"
            :row-class-name="tableRowClassName" 
            @row-click="handleRowClick" 
            height="100%"
            empty-text="暂无数据"
          >
            <el-table-column label="序号" min-width="60">
              <template #default="{ $index }">
                {{ (currentPage - 1) * pageSize + $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" min-width="100" />
            <el-table-column prop="nickName" label="职务" min-width="120" />
            <el-table-column prop="mobile" label="联系电话" min-width="120" />
            <el-table-column prop="roleName" label="用户名" min-width="120" />
            <el-table-column label="所属角色" min-width="120">
              <template #default="{ row }">
                {{ getRolesText(row.role) }}
              </template>
            </el-table-column>
            <el-table-column label="所属单位" min-width="150">
              <template #default="{ row }">
                {{ getUnitText(row.deptName) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="80">
              <template #default="{ row }">
                <el-tag :type="row.state ? 'success' : 'danger'">
                  {{ row.state ? '正常' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 部门对话框 -->
    <DeptDialog
      v-model:visible="deptDialogVisible"
      :mode="deptDialogMode"
      :data="deptDialogData"
      :parentDept="selectedParentDept"
      @success="handleDeptDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElTree, ElTag } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { 
  getDeptTree, 
  deleteDept, 
  getDeptDetail,
  getUserPage
} from '@/api/system';
import { DEPT_TYPE_TEXT_MAP, DEPT_STATUS_TEXT_MAP } from '@/constants/system';
import DeptDialog from './components/DeptDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(0);
const tableData = ref([]);

// 角色选项数据
const roleOptions = ref([]);

// 表单数据
const formData = reactive({
  roleId: '',
  userName: '',
  deptId: ''
});

// 部门树相关
const deptTreeRef = ref(null);
const deptTreeData = ref([]);
const deptTreeProps = reactive({
  children: 'children',
  label: 'name',
  value: 'id'
});

// 选中的部门
const selectedDept = ref({});
const selectedDeptName = ref('全部部门');
const selectedParentDept = ref({});

// 部门对话框相关
const deptDialogVisible = ref(false);
const deptDialogMode = ref('add'); // 'add' | 'edit' | 'view'
const deptDialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchUserData();
};

// 处理重置
const handleReset = () => {
  formData.roleId = '';
  formData.userName = '';
  currentPage.value = 1;
  fetchUserData();
};

// 获取部门树数据
const fetchDeptTree = async () => {
  try {
    const res = await getDeptTree();
    if (res && res.data) {
      deptTreeData.value = res.data;
      // 默认选中第一个一级部门
      if (res.data.length > 0) {
        selectedDept.value = res.data[0];
        selectedDeptName.value = res.data[0].name;
        formData.deptId = res.data[0].id;
        fetchUserData();
      }
    }
  } catch (error) {
    console.error('获取部门树失败:', error);
    ElMessage.error('获取部门树失败');
  }
};

// 获取用户分页数据
const fetchUserData = async () => {
  try {
    const params = {
      deptId: formData.deptId,
      roleId: formData.roleId,
      name: formData.userName
    };
    
    const res = await getUserPage(currentPage.value, pageSize.value, params);
    
    if (res && res.status === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
    ElMessage.error('获取用户数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchUserData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchUserData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理部门节点点击
const handleDeptClick = (data) => {
  selectedDept.value = data;
  selectedDeptName.value = data.name;
  formData.deptId = data.id;
  currentPage.value = 1;
  fetchUserData();
};

// 处理新增根部门
const handleAddRootDept = () => {
  deptDialogMode.value = 'add';
  deptDialogData.value = {};
  selectedParentDept.value = {};
  deptDialogVisible.value = true;
};

// 处理新增子部门
const handleAddChildDept = (data) => {
  deptDialogMode.value = 'add';
  deptDialogData.value = {};
  selectedParentDept.value = data;
  deptDialogVisible.value = true;
};

// 处理编辑部门
const handleEditDept = async (data) => {
  try {
    const res = await getDeptDetail(data.id);
    if (res && res.status === 200) {
      deptDialogMode.value = 'edit';
      deptDialogData.value = res.data;
      selectedParentDept.value = {};
      deptDialogVisible.value = true;
    } else {
      ElMessage.error('获取部门详情失败');
    }
  } catch (error) {
    console.error('获取部门详情失败:', error);
    ElMessage.error('获取部门详情失败');
  }
};

// 处理删除部门
const handleDeleteDept = (data) => {
  ElMessageBox.confirm(
    `确定要删除部门"${data.name}"吗？删除后该部门及其子部门都将被删除！`, 
    '提示', 
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const res = await deleteDept(data.id);
      if (res && res.status === 200) {
        ElMessage.success('删除成功');
        fetchDeptTree();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除部门失败:', error);
      ElMessage.error('删除部门失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理部门对话框成功提交
const handleDeptDialogSuccess = () => {
  fetchDeptTree();
};

// 获取角色文本
const getRolesText = (roles) => {
  if (!roles || !Array.isArray(roles) || roles.length === 0) {
    return '管理员';
  }
  return roles.map(role => role.name).join('、');
};

// 获取单位文本（根据部门名称确定单位信息）
const getUnitText = (deptName) => {
  if (!deptName) return '住建局/燃气办';
  
  // 根据部门名称判断所属单位
  if (deptName.includes('燃气') || deptName.includes('办公室')) {
    return '住建局/燃气办';
  }
  return '住建局/燃气办';
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchDeptTree();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.organization-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.organization-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 主体内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  width: 300px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #EBEEF5;
  background-color: #FAFAFA;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.add-dept-btn {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
}

.tree-container {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.dept-tree {
  background: transparent;
}

:deep(.el-tree-node__content) {
  height: 32px;
  margin-bottom: 4px;
}

:deep(.el-tree-node__content:hover) {
  background-color: #F5F7FA;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #E6F7FF;
  color: #0277FD;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-actions .el-button {
  padding: 0;
  width: 20px;
  height: 20px;
}

/* 右侧面板样式 */
.right-panel {
  flex: 1;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  padding: 0 16px 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 