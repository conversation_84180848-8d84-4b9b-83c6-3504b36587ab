<template>
  <div class="login-log-container">
    <!-- 搜索区域 -->
    <div class="login-log-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">时间:</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="form-input date-range"
          />
        </div>
        <div class="form-item">
          <span class="label">用户名:</span>
          <el-input v-model="formData.username" class="form-input" placeholder="输入用户名、姓名" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">提交</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="username" label="姓名" min-width="100" />
        <el-table-column prop="username" label="用户名" min-width="100" />
        <el-table-column prop="operation" label="登陆系统" min-width="120" />
        <el-table-column prop="createTime" label="登陆时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="登陆描述" min-width="100">
          <template #default="{ row }">
            <span :class="getStatusClass(row.status)">
              {{ getStatusText(row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP地址" min-width="120" />
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElDatePicker, ElInput } from 'element-plus'
import { getLoginLogPage } from '@/api/system'
import moment from 'moment'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 搜索表单数据
const formData = ref({
  username: ''
})

// 日期范围
const dateRange = ref([])

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchLoginLogData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    username: ''
  }
  dateRange.value = []
  currentPage.value = 1
  fetchLoginLogData()
}

// 获取登陆日志分页数据
const fetchLoginLogData = async () => {
  try {
    const params = {
      username: formData.value.username
    }
    
    // 处理时间范围参数
    if (dateRange.value && dateRange.value.length === 2) {
      params.updateTime = dateRange.value[0] + ' - ' + dateRange.value[1]
    }
    
    const res = await getLoginLogPage(currentPage.value, pageSize.value, params)
    
    if (res && res.status === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res?.msg || '获取登陆日志失败')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取登陆日志失败:', error)
    ElMessage.error('获取登陆日志失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchLoginLogData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchLoginLogData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1:
      return '登陆成功'
    case 0:
      return '登陆失败'
    default:
      return '未知状态'
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 1:
      return 'status-success'
    case 0:
      return 'status-error'
    default:
      return 'status-default'
  }
}

// 在组件挂载后获取数据
onMounted(() => {
  fetchLoginLogData()
})
</script>

<style scoped>
.login-log-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.login-log-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

.date-range {
  width: 320px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

:deep(.el-date-editor) {
  height: 32px;
}

:deep(.el-date-editor .el-input__wrapper) {
  height: 32px;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

/* 状态样式 */
.status-success {
  color: #67C23A;
}

.status-error {
  color: #F56C6C;
}

.status-default {
  color: #909399;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 