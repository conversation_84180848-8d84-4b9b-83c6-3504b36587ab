<template>
  <div class="heating-home">
    <!-- 顶部卡片统计 -->
    <div class="top-section">
      <!-- 左侧管网信息 -->
      <div class="network-cards">
        <!-- 供热管网 -->
        <div class="network-card" style="background: #F1F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/heating/guanwang.png" alt="供热管网">
          </div>
          <div class="content">
            <div class="title">供热管网</div>
            <div class="data">
              <span class="value">{{ homeData.overview.pipeLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/rani.png" alt="供热管网图表">
          </div>
        </div>

        <!-- 换热站 -->
        <div class="network-card" style="background: #FFF3F1;">
          <div class="icon-box">
            <img src="@/assets/images/mis/heating/huanrezhan.png" alt="换热站">
          </div>
          <div class="content">
            <div class="title">换热站</div>
            <div class="data">
              <span class="value">{{ homeData.overview.stationCount }}</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/gaoya.png" alt="换热站图表">
          </div>
        </div>

        <!-- 热源厂 -->
        <div class="network-card" style="background: #FFF8F0;">
          <div class="icon-box">
            <img src="@/assets/images/mis/heating/reyuanchang.png" alt="热源厂">
          </div>
          <div class="content">
            <div class="title">热源厂</div>
            <div class="data">
              <span class="value">{{ homeData.overview.factoryCount }}</span>
              <span class="unit">座</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/zhongya.png" alt="热源厂图表">
          </div>
        </div>

        <!-- 供热用户 -->
        <div class="network-card" style="background: #F1F5FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/heating/yonghu.png" alt="供热用户">
          </div>
          <div class="content">
            <div class="title">供热用户</div>
            <div class="data">
              <span class="value">{{ homeData.overview.buildingCount }}</span>
              <span class="unit">户</span>
            </div>
          </div>
          <div class="chart-img">
            <img src="@/assets/images/mis/gas/diya.png" alt="供热用户图表">
          </div>
        </div>
      </div>

      <!-- 右侧报警信息 -->
      <div class="alarm-info">
        <div class="alarm-row">
          <div class="alarm-title">今日报警</div>
          <div class="alarm-value alarm-today">{{ homeData.alarmCount.todayCount }}</div>
        </div>
        <div class="alarm-row">
          <div class="alarm-title">本月报警</div>
          <div class="alarm-value alarm-month">{{ homeData.alarmCount.monthCount }}</div>
        </div>
      </div>
    </div>
    <!-- 第二行：待处理报警和巡检记录 -->
    <div class="second-row">
      <!-- 左侧：待处理报警区域 -->
      <div class="pending-alarm-section">
        <div class="section-header">
          <div class="section-title">待处理报警</div>
        </div>

        <!-- 报警分级统计 -->
        <div class="alarm-levels">
          <div class="level-card level-one">
            <div class="level-name">一级报警</div>
            <div class="level-value">{{ homeData.unhandleAlarm.level1count }}</div>
          </div>
          <div class="level-card level-two">
            <div class="level-name">二级报警</div>
            <div class="level-value">{{ homeData.unhandleAlarm.level2count }}</div>
          </div>
          <div class="level-card level-three">
            <div class="level-name">三级报警</div>
            <div class="level-value">{{ homeData.unhandleAlarm.level3count }}</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="alarm-list">
          <div class="alarm-item" v-for="(item, index) in homeData.unhandleAlarm.alarmInfoPage.records" :key="index">
            <div class="alarm-info-detail">
              <div class="alarm-title">{{ item.deviceName || '设备出现报警信息' }}</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ item.address || '未知位置' }}</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>{{ formatAlarmTime(item.alarmTime) }}</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag" :class="getAlarmLevelClass(item.alarmLevel)">
              {{ item.alarmLevelName || getAlarmLevelName(item.alarmLevel) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：巡检记录 -->
      <div class="inspection-table-section">
        <div class="section-header">
          <div class="section-title">近30日巡检记录</div>
        </div>

        <div class="inspection-table-container">
          <el-table :data="inspectionTableData" style="width: 100%"
            :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
            :row-class-name="tableRowClassName" highlight-current-row>
            <el-table-column prop="index" label="序号" width="70" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="180"></el-table-column>
            <el-table-column prop="location" label="位置" min-width="300"></el-table-column>
            <el-table-column prop="count" label="次数" width="100" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 底部统计和风险面板 -->
    <div class="bottom-section">
      <!-- 左侧统计图表 -->
      <div class="statistics-charts">
        <div class="section-header">
          <div class="section-title">报警统计</div>
          <div class="action">
            <el-radio-group v-model="timeRange" size="small">
              <el-radio-button label="7">近7日</el-radio-button>
              <el-radio-button label="30">近30日</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="statistics-data">
          <div class="stat-item">
            <div class="stat-value">{{ homeData.alarmStatistics.alarmCount }}</div>
            <div class="stat-label">全部报警</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ homeData.alarmStatistics.handleCount }}</div>
            <div class="stat-label">已处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ homeData.alarmStatistics.handleRate }}%</div>
            <div class="stat-label">处理完成率</div>
          </div>
        </div>

        <div ref="trendChartRef" class="trend-chart-container"></div>
      </div>

      <!-- 右侧管网风险面板 -->
        <div class="risk-container">
          <div class="section-header">
            <div class="section-title">管网风险分布</div>
            <div class="action">
              <el-radio-group v-model="riskType" size="small">
                <el-radio-button label="pipeline">管线</el-radio-button>
                <el-radio-button label="sewage">换热站</el-radio-button>
                <el-radio-button label="pump">热源厂</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="risk-content">
            <!-- 风险分布图表 -->
            <div class="risk-chart">
              <div v-if="riskType === 'pipeline'" class="risk-distribution">
                <div class="risk-item risk-high">
                  <div class="risk-label">高风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.pipeline.bigRiskLength, 'pipeline') + '%' }">{{ homeData.riskStatistics.pipeline.bigRiskLength }}km</div>
                </div>
                <div class="risk-item risk-medium">
                  <div class="risk-label">中风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.pipeline.largerRiskLength, 'pipeline') + '%' }">{{ homeData.riskStatistics.pipeline.largerRiskLength }}km</div>
                </div>
                <div class="risk-item risk-low">
                  <div class="risk-label">一般风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.pipeline.generalRiskLength, 'pipeline') + '%' }">{{ homeData.riskStatistics.pipeline.generalRiskLength }}km</div>
                </div>
                <div class="risk-item risk-normal">
                  <div class="risk-label">低风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.pipeline.lowRiskLength, 'pipeline') + '%' }">{{ homeData.riskStatistics.pipeline.lowRiskLength }}km</div>
                </div>
              </div>

              <div v-if="riskType === 'sewage'" class="risk-distribution">
                <div class="risk-item risk-high">
                  <div class="risk-label">高风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.station.bigRiskLength, 'station') + '%' }">{{ homeData.riskStatistics.station.bigRiskLength }}座</div>
                </div>
                <div class="risk-item risk-medium">
                  <div class="risk-label">中风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.station.largerRiskLength, 'station') + '%' }">{{ homeData.riskStatistics.station.largerRiskLength }}座</div>
                </div>
                <div class="risk-item risk-low">
                  <div class="risk-label">一般风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.station.generalRiskLength, 'station') + '%' }">{{ homeData.riskStatistics.station.generalRiskLength }}座</div>
                </div>
                <div class="risk-item risk-normal">
                  <div class="risk-label">低风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.station.lowRiskLength, 'station') + '%' }">{{ homeData.riskStatistics.station.lowRiskLength }}座</div>
                </div>
              </div>

              <div v-if="riskType === 'pump'" class="risk-distribution">
                <div class="risk-item risk-high">
                  <div class="risk-label">高风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.factory.bigRiskLength, 'factory') + '%' }">{{ homeData.riskStatistics.factory.bigRiskLength }}座</div>
                </div>
                <div class="risk-item risk-medium">
                  <div class="risk-label">中风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.factory.largerRiskLength, 'factory') + '%' }">{{ homeData.riskStatistics.factory.largerRiskLength }}座</div>
                </div>
                <div class="risk-item risk-low">
                  <div class="risk-label">一般风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.factory.generalRiskLength, 'factory') + '%' }">{{ homeData.riskStatistics.factory.generalRiskLength }}座</div>
                </div>
                <div class="risk-item risk-normal">
                  <div class="risk-label">低风险</div>
                  <div class="risk-bar" :style="{ width: calculateRiskPercent(homeData.riskStatistics.factory.lowRiskLength, 'factory') + '%' }">{{ homeData.riskStatistics.factory.lowRiskLength }}座</div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch } from 'vue'
import { Location, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
// 请根据项目实际情况调整导入路径
import request from '@/utils/request' // 假设项目使用request工具，请根据实际情况修改

// 窗口尺寸状态
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 根据窗口大小计算是否应该截断长文本
const shouldTruncate = computed(() => {
  return windowSize.value.width < 1440
})

// 处理窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (trendChart) {
    trendChart.resize()
  }
}

// 响应式数据对象
const homeData = reactive({
  // 顶部卡片统计数据
  overview: {
    pipeLength: 0,
    factoryCount: 0,
    stationCount: 0,
    buildingCount: 0
  },
  // 报警数量统计
  alarmCount: {
    todayCount: 0,
    monthCount: 0
  },
  // 待处置报警统计
  unhandleAlarm: {
    level1count: 0,
    level2count: 0,
    level3count: 0,
    alarmInfoPage: {
      records: []
    }
  },
  // 报警排名数据（用于巡检记录表格）
  alarmRank: {
    records: []
  },
  // 报警统计数据
  alarmStatistics: {
    alarmCount: 0,
    handleCount: 0,
    handleRate: 0,
    alarmTrendStatistics: []
  },
  // 风险统计数据
  riskStatistics: {
    pipeline: {
      bigRiskLength: 0,
      largerRiskLength: 0,
      generalRiskLength: 0,
      lowRiskLength: 0
    },
    station: {
      bigRiskLength: 0,
      largerRiskLength: 0,
      generalRiskLength: 0,
      lowRiskLength: 0
    },
    factory: {
      bigRiskLength: 0,
      largerRiskLength: 0,
      generalRiskLength: 0,
      lowRiskLength: 0
    }
  }
})

// 巡检记录表格数据（从接口数据转换而来）
const inspectionTableData = computed(() => {
  return homeData.alarmRank.records.map((item, index) => ({
    index: index + 1,
    deviceName: item.deviceName || '***设备',
    location: item.address || '***位置',
    count: item.alarmCount || 0
  }))
})

// 表格行的类名 - 用于实现斑马纹效果
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 1 ? 'striped-row' : ''
}

// 时间范围选择
const timeRange = ref('7')

// 风险类型选择
const riskType = ref('pipeline')

// 图表引用
const trendChartRef = ref(null)
let trendChart = null

// API调用方法
const fetchOverview = async () => {
  try {
    const response = await request.get('/heat/homePage/overview')
    if (response.code === 200) {
      homeData.overview = response.data
    }
  } catch (error) {
    console.error('获取概览数据失败:', error)
  }
}

const fetchAlarmCount = async () => {
  try {
    const response = await request.get('/heat/homePage/alarm/count')
    if (response.code === 200) {
      homeData.alarmCount = response.data
    }
  } catch (error) {
    console.error('获取报警数量失败:', error)
  }
}

const fetchUnhandleAlarm = async () => {
  try {
    const response = await request.get('/heat/homePage/alarm/unhandleStatistics', {
      params: { pageNum: 1, pageSize: 10 }
    })
    if (response.code === 200) {
      homeData.unhandleAlarm = response.data
    }
  } catch (error) {
    console.error('获取待处置报警统计失败:', error)
  }
}

const fetchAlarmRank = async () => {
  try {
    const response = await request.get('/heat/homePage/alarm/rank', {
      params: { pageNum: 1, pageSize: 10 }
    })
    if (response.code === 200) {
      homeData.alarmRank = response.data
    }
  } catch (error) {
    console.error('获取报警排名失败:', error)
  }
}

const fetchAlarmStatistics = async (dayIndex = 7) => {
  try {
    const response = await request.get('/heat/homePage/alarm/statistics', {
      params: { dayIndex }
    })
    if (response.code === 200) {
      homeData.alarmStatistics = response.data
      // 更新图表数据
      initTrendChart()
    }
  } catch (error) {
    console.error('获取报警统计失败:', error)
  }
}

const fetchPipelineStatistics = async () => {
  try {
    const response = await request.get('/heat/homePage/pipeline/statistics')
    if (response.code === 200) {
      homeData.riskStatistics.pipeline = response.data
    }
  } catch (error) {
    console.error('获取管线风险统计失败:', error)
  }
}

const fetchStationStatistics = async () => {
  try {
    const response = await request.get('/heat/homePage/station/statistics')
    if (response.code === 200) {
      homeData.riskStatistics.station = response.data
    }
  } catch (error) {
    console.error('获取换热站风险统计失败:', error)
  }
}

const fetchFactoryStatistics = async () => {
  try {
    const response = await request.get('/heat/homePage/factory/statistics')
    if (response.code === 200) {
      homeData.riskStatistics.factory = response.data
    }
  } catch (error) {
    console.error('获取热源厂风险统计失败:', error)
  }
}

// 获取所有数据
const fetchAllData = async () => {
  await Promise.all([
    fetchOverview(),
    fetchAlarmCount(),
    fetchUnhandleAlarm(),
    fetchAlarmRank(),
    fetchAlarmStatistics(parseInt(timeRange.value)),
    fetchPipelineStatistics(),
    fetchStationStatistics(),
    fetchFactoryStatistics()
  ])
}

// 工具方法
// 格式化报警时间
const formatAlarmTime = (timeStr) => {
  if (!timeStr) return '--'
  try {
    const date = new Date(timeStr)
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    return `${month}月${day}日 ${hour}:${minute}`
  } catch (error) {
    return timeStr
  }
}

// 获取报警级别样式类
const getAlarmLevelClass = (level) => {
  switch (level) {
    case '1':
    case 1:
      return 'level-1-tag'
    case '2':
    case 2:
      return 'level-2-tag'
    case '3':
    case 3:
      return 'level-3-tag'
    default:
      return 'level-3-tag'
  }
}

// 获取报警级别名称
const getAlarmLevelName = (level) => {
  switch (level) {
    case '1':
    case 1:
      return '一级报警'
    case '2':
    case 2:
      return '二级报警'
    case '3':
    case 3:
      return '三级报警'
    default:
      return '三级报警'
  }
}

// 计算风险百分比
const calculateRiskPercent = (value, type) => {
  if (!value || value === 0) return 0
  
  // 根据类型和数值计算合理的百分比
  const maxValue = type === 'pipeline' ? 50 : 20 // 管线最大50km，其他最大20座
  const percent = Math.min((value / maxValue) * 100, 100)
  return Math.max(percent, 10) // 最小显示10%，确保有视觉效果
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChart) {
      trendChart.dispose()
    }

    trendChart = echarts.init(trendChartRef.value)

    // 使用接口数据
    const trendData = homeData.alarmStatistics.alarmTrendStatistics || []
    const dates = trendData.map(item => item.date || '')
    const counts = trendData.map(item => item.totalCount || 0)

    // 如果没有数据，使用默认数据以保持图表显示
    const finalDates = dates.length > 0 ? dates : ['8/1', '8/2', '8/3', '8/4', '8/5', '8/6', '8/7']
    const finalCounts = counts.length > 0 ? counts : [25, 40, 20, 35, 60, 45, 30]

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: finalDates
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '报警数',
          type: 'line',
          smooth: true,
          data: finalCounts,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    trendChart.setOption(option)
  }
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  fetchAlarmStatistics(parseInt(newVal))
})

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // 获取所有数据
  fetchAllData()
})

onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', handleResize)

  // 释放图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})
</script>

<style scoped>
.heating-home {
  padding: 1px;
  height: 99%;
  overflow: auto;
}

/* 顶部卡片统计样式 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.network-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 1556px;
  height: 140px;
  background: #FFFFFF;
  padding: 15px;
  border-radius: 4px;
}

.network-card {
  display: flex;
  width: 292px;
  height: 110px;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
}

.icon-box {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-box img {
  width: 56px;
  height: 56px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.data {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #000000;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.chart-img {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.chart-img img {
  max-width: 84px;
  max-height: 64px;
}

/* 为不同的图表设置不同的大小 */
.network-card:nth-child(1) .chart-img img {
  width: 84px;
  height: 64px;
}

.network-card:nth-child(2) .chart-img img,
.network-card:nth-child(4) .chart-img img {
  width: 78px;
  height: 51px;
}

.network-card:nth-child(3) .chart-img img {
  width: 64px;
  height: 64px;
}

/* 右侧报警信息样式 */
.alarm-info {
  width: 300px;
  height: 140px;
  background: linear-gradient(180deg, #FFE9E9 0%, #FFF7F7 100%);
  border-radius: 4px;
  border: 1px solid #EFF0F2;
  display: flex;
  gap: 78px;
  padding: 39px 47px;
}

.alarm-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.alarm-row:last-child {
  margin-bottom: 0;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 4px;
  white-space: nowrap;
}

.alarm-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 32px;
}

.alarm-today {
  color: #FF1414;
}

.alarm-month {
  color: #333333;
}

/* 第二行布局样式 */
.second-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

/* 左侧：待处理报警区域样式 */
.pending-alarm-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 右侧：巡检记录区域样式 */
.inspection-table-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
}

/* 报警分级卡片样式 */
.alarm-levels {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-card {
  width: 278px;
  height: 75px;
  border-radius: 4px;
  border: 1px solid #F3F3F3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.level-one {
  background: linear-gradient(315deg, #FFFAFA 0%, #FF6565 100%);
}

.level-two {
  background: linear-gradient(135deg, #FFA149 0%, #FFFDFB 100%);
}

.level-three {
  background: linear-gradient(135deg, #8FBAFF 0%, #F3F8FF 100%);
}

.level-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
}

/* 报警列表样式 */
.alarm-list {
  max-height: 180px;
  overflow-y: auto;
  padding-right: 8px;
}

.alarm-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #FFFFFF;
  margin-bottom: 12px;
  border-radius: 4px;
  border-left: 4px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.alarm-info-detail {
  display: flex;
  flex-direction: column;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.alarm-location-time {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
}

.alarm-location,
.alarm-time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  display: flex;
  align-items: center;
}

.alarm-location :deep(svg),
.alarm-time :deep(svg) {
  margin-right: 4px;
  font-size: 16px;
  color: #909399;
}

.alarm-level-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.level-1-tag {
  background-color: #FFEFEF;
  color: #FF1414;
}

.level-2-tag {
  background-color: #FFF6EC;
  color: #FF7C00;
}

.level-3-tag {
  background-color: #EDF5FF;
  color: #2D7EFF;
}

/* 表格样式 */
.inspection-table-container {
  height: calc(100% - 40px);
  overflow-y: auto;
}

/* 表格斑马纹效果 */
:deep(.striped-row) {
  background-color: #F5F7FA;
}
/* 底部统计和风险面板 */
.bottom-section {
  display: flex;
  gap: 16px;
}

/* 左侧统计图表 */
.statistics-charts {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

.action {
  display: flex;
  align-items: center;
}

.statistics-data {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
}

.trend-chart-container {
  height: 220px;
}

/* 右侧管网风险面板 */
.risk-container {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}
.risk-content {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.risk-distribution {
  width: 100%;
  padding: 20px 0;
}

.risk-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.risk-label {
  width: 80px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-right: 16px;
}

.risk-bar {
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  transition: width 0.5s ease;
}

.risk-high .risk-bar {
  background: linear-gradient(90deg, #FF4D4F 0%, #FF7875 100%);
}

.risk-medium .risk-bar {
  background: linear-gradient(90deg, #FF7A45 0%, #FF9C6E 100%);
}

.risk-low .risk-bar {
  background: linear-gradient(90deg, #FFC53D 0%, #FFD666 100%);
}

.risk-normal .risk-bar {
  background: linear-gradient(90deg, #73D13D 0%, #95DE64 100%);
}
@media (min-height: 900px) and (max-height: 940px) {
  .heating-home {
    height: 76%;
    overflow: auto;
  }
}
</style>