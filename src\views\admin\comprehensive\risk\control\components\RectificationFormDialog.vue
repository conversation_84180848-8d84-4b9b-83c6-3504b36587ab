<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="rectification-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="整改状态" prop="handleStatus">
            <el-select v-model="formData.handleStatus" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in handleStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="整改责任人" prop="handleUserName">
            <el-input v-model="formData.handleUserName" placeholder="请输入整改责任人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="整改时间" prop="dealTime">
            <el-date-picker
              v-model="formData.dealTime"
              type="datetime"
              placeholder="请选择整改时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否按期" prop="onSchedule">
            <el-select v-model="formData.onSchedule" placeholder="请选择" class="w-full">
              <el-option
                v-for="item in onScheduleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="整改描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入整改描述"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="整改图片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              list-type="picture-card"
              :limit="9"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传 .jpg .jpeg .png .gif .bmp .JPG .JPEG .PNG .GIF .BMP .svg .SVG
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  addHiddenDangerHandle
} from '@/api/comprehensive'
import { RECTIFICATION_STATUS_OPTIONS, ON_SCHEDULE_OPTIONS } from '@/constants/comprehensive'
import { uploadFile } from '@/api/upload'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  dangerId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const fileList = ref([])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增整改记录',
    edit: '编辑整改记录'
  }
  return titles[props.mode] || '整改记录'
})

// 下拉选项数据
const handleStatusOptions = ref(RECTIFICATION_STATUS_OPTIONS)
const onScheduleOptions = ref(ON_SCHEDULE_OPTIONS)

// 表单数据
const formData = reactive({
  id: '',
  dangerId: '',
  handleStatus: '',
  handleStatusName: '',
  handleUserName: '',
  dealTime: '',
  description: '',
  remark: '',
  onSchedule: true,
  picUrls: ''
})

// 表单验证规则
const formRules = {
  handleStatus: [{ required: true, message: '请选择整改状态', trigger: 'change' }],
  handleUserName: [{ required: true, message: '请输入整改责任人', trigger: 'blur' }],
  dealTime: [{ required: true, message: '请选择整改时间', trigger: 'change' }],
  description: [{ required: true, message: '请输入整改描述', trigger: 'blur' }],
  onSchedule: [{ required: true, message: '请选择是否按期', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'onSchedule') {
      formData[key] = true
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理图片显示
    if (newVal.picUrls) {
      fileList.value = newVal.picUrls.split(',').map((url, index) => ({
        name: `image_${index}`,
        url: url.trim(),
        uid: Date.now() + index
      })).filter(item => item.url)
    }
  } else if (props.mode === 'add') {
    resetForm()
    formData.dangerId = props.dangerId
  }
}, { immediate: true, deep: true })

// 监听dangerId变化
watch(() => props.dangerId, (newVal) => {
  if (newVal && props.mode === 'add') {
    formData.dangerId = newVal
  }
})

// 监听下拉选择变化，更新对应的名称字段
watch(() => formData.handleStatus, (val) => {
  if (val) {
    const selected = handleStatusOptions.value.find(item => item.value === val)
    if (selected) formData.handleStatusName = selected.label
  }
})

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.picUrls ? formData.picUrls.split(',') : []
      urls.push(response.data.url)
      formData.picUrls = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    const res = await addHiddenDangerHandle(submitData)

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}
</script>

<style scoped>
.rectification-form-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 