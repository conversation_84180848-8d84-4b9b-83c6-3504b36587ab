<template>
  <div class="inspection-container">
    <!-- 搜索区域 -->
    <div class="inspection-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属专项:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">任务状态:</span>
          <el-select v-model="formData.taskStatus" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in taskStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.taskName" class="form-input" placeholder="输入任务人员" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="taskCode" label="任务编码" min-width="120" />
        <el-table-column prop="taskName" label="任务名称" min-width="120" />
        <el-table-column prop="relatedBusinessName" label="所属专项" min-width="100" />
        <el-table-column prop="startTime" label="巡检周期" min-width="140">
          <template #default="{ row }">
            {{ formatPeriod(row.startTime, row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="frequencyName" label="巡检频次" min-width="100" />
        <el-table-column prop="taskUserName" label="巡检人员" min-width="100" />
        <el-table-column prop="taskStatusName" label="任务状态" min-width="100" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <InspectionDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getInspectionTaskPage, 
  deleteInspectionTask, 
  getInspectionTaskDetail
} from '@/api/comprehensive';
import { 
  RELATED_BUSINESS_OPTIONS,
  INSPECTION_TASK_STATUS_OPTIONS 
} from '@/constants/comprehensive';
import InspectionDialog from './components/InspectionDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS;
const taskStatusOptions = INSPECTION_TASK_STATUS_OPTIONS;

// 表单数据
const formData = ref({
  relatedBusiness: '',
  taskStatus: '',
  taskName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 格式化周期显示
const formatPeriod = (startTime, endTime) => {
  if (startTime && endTime) {
    return `${startTime} 至 ${endTime}`;
  }
  return '';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchInspectionData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    taskStatus: '',
    taskName: ''
  };
  currentPage.value = 1;
  fetchInspectionData();
};

// 获取巡检任务分页数据
const fetchInspectionData = async () => {
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      taskStatus: formData.value.taskStatus,
      taskName: formData.value.taskName
    };
    
    const res = await getInspectionTaskPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取巡检任务数据失败:', error);
    ElMessage.error('获取巡检任务数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchInspectionData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchInspectionData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getInspectionTaskDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取巡检任务详情失败');
    }
  } catch (error) {
    console.error('获取巡检任务详情失败:', error);
    ElMessage.error('获取巡检任务详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getInspectionTaskDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取巡检任务详情失败');
    }
  } catch (error) {
    console.error('获取巡检任务详情失败:', error);
    ElMessage.error('获取巡检任务详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该巡检任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteInspectionTask(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchInspectionData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除巡检任务失败:', error);
      ElMessage.error('删除巡检任务失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchInspectionData();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await fetchInspectionData();
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.inspection-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.inspection-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>