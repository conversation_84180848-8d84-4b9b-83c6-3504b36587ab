<template>
  <div class="emergency-plan-container">
    <!-- 搜索区域 -->
    <div class="emergency-plan-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">预案类别:</span>
          <el-select v-model="formData.schemeType" class="form-input" placeholder="请选择预案类别">
            <el-option label="全部" value="" />
            <el-option v-for="item in schemeTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">关联行业:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="请选择关联行业">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">匹配事件类型:</span>
          <el-select v-model="formData.relatedWarningType" class="form-input" placeholder="请选择匹配事件类型">
            <el-option label="全部" value="" />
            <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预案标题:</span>
          <el-input v-model="formData.schemeTitle" class="form-input" placeholder="请输入预案标题" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="schemeTitle" label="预案标题" min-width="150" />
        <el-table-column prop="relatedBusinessName" label="关联行业" min-width="100" />
        <el-table-column prop="relatedWarningTypeName" label="匹配事件类型" min-width="120" />
        <el-table-column prop="schemeTypeName" label="匹配作件类型" min-width="120" />
        <el-table-column label="附件" min-width="80">
          <template #default="{ row }">
            <el-button 
              v-if="row.fileUrls" 
              type="primary" 
              link 
              @click.stop="handleViewAttachment(row)"
            >
              查看附件
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="issuedUnit" label="发布机构" min-width="120" />
        <el-table-column prop="issuedTime" label="发布日期" min-width="120">
          <template #default="{ row }">
            {{ row.issuedTime ? row.issuedTime.split(' ')[0] : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <PlanDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getEmergencySchemiePage, 
  deleteEmergencyScheme, 
  getEmergencySchemeDetail
} from '@/api/comprehensive';
import {
  RELATED_BUSINESS_OPTIONS,
  EMERGENCY_WARNING_TYPE_OPTIONS,
  SCHEME_TYPE_OPTIONS
} from '@/constants/comprehensive';
import PlanDialog from './components/PlanDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS);
const warningTypeOptions = ref(EMERGENCY_WARNING_TYPE_OPTIONS);
const schemeTypeOptions = ref(SCHEME_TYPE_OPTIONS);

// 表单数据
const formData = ref({
  schemeType: '',
  relatedBusiness: '',
  relatedWarningType: '',
  schemeTitle: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchPlanData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    schemeType: '',
    relatedBusiness: '',
    relatedWarningType: '',
    schemeTitle: ''
  };
  currentPage.value = 1;
  fetchPlanData();
};

// 获取应急预案分页数据
const fetchPlanData = async () => {
  try {
    const params = {
      schemeType: formData.value.schemeType,
      relatedBusiness: formData.value.relatedBusiness,
      relatedWarningType: formData.value.relatedWarningType,
      schemeTitle: formData.value.schemeTitle
    };
    
    const res = await getEmergencySchemiePage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取应急预案数据失败:', error);
    ElMessage.error('获取应急预案数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchPlanData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchPlanData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getEmergencySchemeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取预案详情失败');
    }
  } catch (error) {
    console.error('获取预案详情失败:', error);
    ElMessage.error('获取预案详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getEmergencySchemeDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取预案详情失败');
    }
  } catch (error) {
    console.error('获取预案详情失败:', error);
    ElMessage.error('获取预案详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该应急预案吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencyScheme(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchPlanData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除预案失败:', error);
      ElMessage.error('删除预案失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理查看附件
const handleViewAttachment = (row) => {
  if (row.fileUrls) {
    const urls = row.fileUrls.split(',').filter(url => url.trim());
    if (urls.length > 0) {
      // 如果只有一个文件，直接打开
      if (urls.length === 1) {
        window.open(urls[0], '_blank');
      } else {
        // 多个文件，可以展示文件列表选择
        ElMessageBox.confirm(
          `该预案包含${urls.length}个附件，是否打开第一个附件？`,
          '查看附件',
          {
            confirmButtonText: '打开',
            cancelButtonText: '取消',
            type: 'info'
          }
        ).then(() => {
          window.open(urls[0], '_blank');
        });
      }
    }
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchPlanData();
};

// 在组件挂载后获取数据
onMounted(() => {
  fetchPlanData();
});
</script>

<style scoped>
.emergency-plan-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.emergency-plan-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>