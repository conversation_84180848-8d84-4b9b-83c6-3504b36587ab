<template>
  <div class="bridge-component-container">
    <!-- 搜索区域 -->
    <div class="bridge-component-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属桥梁:</span>
          <el-select v-model="formData.bridgeId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in bridgeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">所属部件:</span>
          <el-select v-model="formData.partType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in partTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.nameOrCode" class="form-input" placeholder="输入构件名称/编码" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%" empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="componentCode" label="构件编码" min-width="120" />
        <el-table-column prop="componentName" label="构件名称" min-width="120" />
        <el-table-column prop="partTypeName" label="所属部件" min-width="100" />
        <el-table-column prop="componentLocation" label="构件位置" min-width="150" />
        <el-table-column label="位置坐标" min-width="120">
          <template #default="{ row }">
            <span v-if="row.longitude && row.latitude">
              {{ row.longitude }}, {{ row.latitude }}
            </span>
            <span v-else>
              {{ row.geomText }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
    </div>

    <!-- 对话框区域 -->
    <ComponentDialog v-model:visible="dialogVisible" :mode="dialogMode" :data="dialogData"
      @success="handleDialogSuccess" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import {
  getBridgeComponentPage,
  deleteBridgeComponent,
  getBridgeComponentDetail,
  getBridgeBasicInfoList
} from '@/api/bridge';
import { SUBJECT_PART_TYPE_OPTIONS } from '@/constants/bridge';
import { misPosition } from "@/hooks/gishooks";
import ComponentDialog from './components/ComponentDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const bridgeOptions = ref([]);
const partTypeOptions = SUBJECT_PART_TYPE_OPTIONS;

// 表单数据
const formData = ref({
  bridgeId: '',
  partType: '',
  nameOrCode: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchComponentData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    bridgeId: '',
    partType: '',
    nameOrCode: ''
  };
  currentPage.value = 1;
  fetchComponentData();
};

// 获取构件分页数据
const fetchComponentData = async () => {
  try {
    const params = {
      bridgeId: formData.value.bridgeId,
      partType: formData.value.partType,
      nameOrCode: formData.value.nameOrCode
    };

    const res = await getBridgeComponentPage(currentPage.value, pageSize.value, params);

    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取构件数据失败:', error);
    ElMessage.error('获取构件数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const res = await getBridgeBasicInfoList({});
    if (res && res.data) {
      bridgeOptions.value = res.data.map(item => ({
        label: item.bridgeName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchComponentData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchComponentData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getBridgeComponentDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取构件详情失败');
    }
  } catch (error) {
    console.error('获取构件详情失败:', error);
    ElMessage.error('获取构件详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getBridgeComponentDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取构件详情失败');
    }
  } catch (error) {
    console.error('获取构件详情失败:', error);
    ElMessage.error('获取构件详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该构件信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteBridgeComponent(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchComponentData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除构件失败:', error);
      ElMessage.error('删除构件失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.geomText &&
    row.geomText.trim() !== ''
  ) {
    // 假设geomText格式为"经度,纬度"或类似格式
    const coords = row.geomText.split(',');
    if (coords.length >= 2) {
      const longitude = parseFloat(coords[0]);
      const latitude = parseFloat(coords[1]);
      if (!isNaN(longitude) && !isNaN(latitude)) {
        misPosition.value = {
          longitude: longitude,
          latitude: latitude
        }
      } else {
        ElMessage.warning('坐标格式不正确，无法定位！')
      }
    } else {
      ElMessage.warning('坐标格式不正确，无法定位！')
    }
  } else {
    ElMessage.warning('没有位置坐标，无法定位！')
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchComponentData();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchBridges(),
      fetchComponentData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.bridge-component-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.bridge-component-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>