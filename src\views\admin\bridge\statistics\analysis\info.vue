<template>
  <div class="bridge-statistics-container">
    <!-- 统计卡片区域 -->
    <div class="statistics-cards">
      <div class="stat-card total-card">
        <div class="stat-icon">
          <i class="el-icon-s-data"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ bridgeTotalCount }}</div>
          <div class="stat-label">桥梁总数</div>
        </div>
      </div>
    </div>

    <!-- 图表统计区域 -->
    <div class="charts-grid">
      <!-- 桥梁类型统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>桥梁类型</h3>
        </div>
        <div class="chart-container">
          <div ref="bridgeTypeChart" class="chart"></div>
        </div>
      </div>

      <!-- 桥梁年龄统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>桥梁年龄</h3>
        </div>
        <div class="chart-container">
          <div ref="bridgeAgeChart" class="chart"></div>
        </div>
      </div>

      <!-- 技术状况统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>技术状况</h3>
        </div>
        <div class="chart-container">
          <div ref="techStatusChart" class="chart"></div>
        </div>
      </div>

      <!-- 养护等级统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>养护等级</h3>
        </div>
        <div class="chart-container">
          <div ref="maintainLevelChart" class="chart"></div>
        </div>
      </div>

      <!-- 养护类型统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>养护类型</h3>
        </div>
        <div class="chart-container">
          <div ref="maintainTypeChart" class="chart"></div>
        </div>
      </div>

      <!-- 养护单位统计 -->
      <div class="chart-card full-width">
        <div class="chart-header">
          <h3>养护单位</h3>
        </div>
        <div class="table-container">
          <el-table 
            :data="maintainUnitData" 
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column prop="rank" label="序号" width="80" align="center">
              <template v-slot="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="单位名称" min-width="200"></el-table-column>
            <el-table-column prop="count" label="桥梁总数" width="120" align="center"></el-table-column>
            <el-table-column prop="percent" label="占比" width="100" align="center">
              <template v-slot="scope">
                {{ scope.row.percent }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading :visible="loading" text="加载数据中..."></el-loading>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { getBridgeStatistics, STATISTICS_TYPE } from '@/api/bridge'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const bridgeTotalCount = ref(0)

// 图表实例
const bridgeTypeChart = ref(null)
const bridgeAgeChart = ref(null)
const techStatusChart = ref(null)
const maintainLevelChart = ref(null)
const maintainTypeChart = ref(null)

// 统计数据
const bridgeTypeData = ref([])
const bridgeAgeData = ref([])
const techStatusData = ref([])
const maintainLevelData = ref([])
const maintainTypeData = ref([])
const maintainUnitData = ref([])

// 图表实例存储
let chartInstances = {}

// 获取统计数据
const fetchStatisticsData = async () => {
  loading.value = true
  try {
    // 并行获取所有统计数据
    const [
      bridgeTypeRes,
      bridgeAgeRes,
      techStatusRes,
      maintainTypeRes,
      maintainLevelRes,
      maintainUnitRes
    ] = await Promise.all([
      getBridgeStatistics(STATISTICS_TYPE.BRIDGE_TYPE),
      getBridgeStatistics(STATISTICS_TYPE.BRIDGE_AGE),
      getBridgeStatistics(STATISTICS_TYPE.TECH_STATUS),
      getBridgeStatistics(STATISTICS_TYPE.MAINTAIN_TYPE),
      getBridgeStatistics(STATISTICS_TYPE.MAINTAIN_LEVEL),
      getBridgeStatistics(STATISTICS_TYPE.MAINTAIN_UNIT)
    ])

    // 处理响应数据
    if (bridgeTypeRes.code === 200) {
      bridgeTotalCount.value = bridgeTypeRes.data.bridgeTotalCount || 0
      bridgeTypeData.value = bridgeTypeRes.data.statisticsList || []
    }
    
    if (bridgeAgeRes.code === 200) {
      bridgeAgeData.value = bridgeAgeRes.data.statisticsList || []
    }
    
    if (techStatusRes.code === 200) {
      techStatusData.value = techStatusRes.data.statisticsList || []
    }
    
    if (maintainTypeRes.code === 200) {
      maintainTypeData.value = maintainTypeRes.data.statisticsList || []
    }
    
    if (maintainLevelRes.code === 200) {
      maintainLevelData.value = maintainLevelRes.data.statisticsList || []
    }
    
    if (maintainUnitRes.code === 200) {
      maintainUnitData.value = maintainUnitRes.data.statisticsList || []
    }

    // 渲染图表
    await nextTick()
    initAllCharts()
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 初始化所有图表
const initAllCharts = () => {
  initBridgeTypeChart()
  initBridgeAgeChart()
  initTechStatusChart()
  initMaintainLevelChart()
  initMaintainTypeChart()
}

// 桥梁类型饼图
const initBridgeTypeChart = () => {
  const chartDom = bridgeTypeChart.value
  if (!chartDom || bridgeTypeData.value.length === 0) return

  const myChart = echarts.init(chartDom)
  chartInstances.bridgeType = myChart

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      itemGap: 20,
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '桥梁类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: bridgeTypeData.value.map(item => ({
          value: item.count,
          name: item.name || item.code
        })),
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']
      }
    ]
  }

  myChart.setOption(option)
}

// 桥梁年龄柱状图
const initBridgeAgeChart = () => {
  const chartDom = bridgeAgeChart.value
  if (!chartDom || bridgeAgeData.value.length === 0) return

  const myChart = echarts.init(chartDom)
  chartInstances.bridgeAge = myChart

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: bridgeAgeData.value.map(item => item.name || item.code),
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          fontSize: 11,
          rotate: 15
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '桥梁数量',
        type: 'bar',
        barWidth: '60%',
        data: bridgeAgeData.value.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }

  myChart.setOption(option)
}

// 技术状况柱状图
const initTechStatusChart = () => {
  const chartDom = techStatusChart.value
  if (!chartDom || techStatusData.value.length === 0) return

  const myChart = echarts.init(chartDom)
  chartInstances.techStatus = myChart

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: techStatusData.value.map(item => item.name || item.code),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '桥梁数量',
        type: 'bar',
        barWidth: '60%',
        data: techStatusData.value.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ffd666' },
            { offset: 0.5, color: '#f7ba2a' },
            { offset: 1, color: '#f7ba2a' }
          ])
        }
      }
    ]
  }

  myChart.setOption(option)
}

// 养护等级柱状图
const initMaintainLevelChart = () => {
  const chartDom = maintainLevelChart.value
  if (!chartDom || maintainLevelData.value.length === 0) return

  const myChart = echarts.init(chartDom)
  chartInstances.maintainLevel = myChart

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: maintainLevelData.value.map(item => item.name || item.code),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '桥梁数量',
        type: 'bar',
        barWidth: '60%',
        data: maintainLevelData.value.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#87d068' },
            { offset: 0.5, color: '#52c41a' },
            { offset: 1, color: '#52c41a' }
          ])
        }
      }
    ]
  }

  myChart.setOption(option)
}

// 养护类型饼图
const initMaintainTypeChart = () => {
  const chartDom = maintainTypeChart.value
  if (!chartDom || maintainTypeData.value.length === 0) return

  const myChart = echarts.init(chartDom)
  chartInstances.maintainType = myChart

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      itemGap: 15,
      textStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: '养护类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'outside',
          formatter: '{d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        data: maintainTypeData.value.map(item => ({
          value: item.count,
          name: item.name || item.code
        })),
        color: ['#ff7875', '#ffc069', '#95de64', '#69c0ff', '#b37feb', '#ff9c6e']
      }
    ]
  }

  myChart.setOption(option)
}

// 窗口大小改变时重新调整图表
const handleResize = () => {
  Object.values(chartInstances).forEach(chart => {
    if (chart && typeof chart.resize === 'function') {
      chart.resize()
    }
  })
}

// 组件挂载
onMounted(() => {
  fetchStatisticsData()
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  Object.values(chartInstances).forEach(chart => {
    if (chart && typeof chart.dispose === 'function') {
      chart.dispose()
    }
  })
})
</script>

<style scoped>
.bridge-statistics-container {
  padding: 20px;
  background-color: #f5f5f5;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.page-title {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.statistics-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.total-card {
  min-width: 200px;
}

.stat-icon {
  font-size: 40px;
  margin-right: 15px;
  opacity: 0.8;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.full-width {
  grid-column: 1 / -1;
}

.chart-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 20px;
}

.chart {
  width: 100%;
  height: 300px;
}

.table-container {
  padding: 20px;
}

.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart {
    height: 250px;
  }
  
  .bridge-statistics-container {
    padding: 10px;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    min-width: auto;
  }
  
  .stat-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

@media (max-width: 480px) {
  .statistics-cards {
    flex-direction: column;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .chart {
    height: 200px;
  }
}
</style>