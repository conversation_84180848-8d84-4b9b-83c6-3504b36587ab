# 排水应急事件管理功能实现说明

## 功能概述
已成功实现排水应急事件管理的完整增删改查功能，包括查询列表、新增事件、编辑事件、查看详情、删除事件等核心功能。

## 实现的文件结构

### 1. API接口层 (`src/api/drainage.js`)
新增了以下接口方法：
- `getEmergencyEventPage()` - 获取应急事件分页数据
- `getEmergencyEventDetail()` - 获取应急事件详情
- `saveEmergencyEvent()` - 新增应急事件
- `updateEmergencyEvent()` - 更新应急事件
- `deleteEmergencyEvent()` - 删除应急事件
- `getFloodEmergencySchemeList()` - 根据事件分类获取处置方案列表

### 2. 常量定义 (`src/constants/drainage.js`)
新增了应急事件管理相关常量：
- `EVENT_TYPE_OPTIONS` - 事件分类选项（城市内涝）
- `EVENT_LEVEL_OPTIONS` - 事件分级选项（特别重大、重大、较大、一般）
- `EVENT_STATUS_OPTIONS` - 事件处置状态选项（未处置、处置中、已处置）
- `IS_CASUALTY_OPTIONS` - 是否人员伤亡选项
- `EVENT_SOURCE_OPTIONS` - 事件来源选项

### 3. 弹窗组件 (`src/views/admin/drainage/decision/support/components/EmergencyEventDialog.vue`)
功能特性：
- 支持三种模式：新增、编辑、详情查看
- 完整的表单字段验证
- 地图选点功能集成
- 处置方案自动联动
- 响应式布局设计

### 4. 主页面 (`src/views/admin/drainage/decision/support/emergency.vue`)
功能特性：
- 多条件查询筛选
- 分页数据展示
- 状态标签显示
- 完整的CRUD操作
- 地图定位功能

## 核心功能实现

### 1. 查询列表功能
- **筛选条件**：事件分类、事件分级、事件状态、事件标题
- **表格显示**：序号、事件来源、事件编号、事件标题、事件分类、事件分级、发生时间、事件描述、事件位置、处置状态
- **分页控制**：支持每页10/20/50/100条记录
- **状态标签**：根据处置状态显示不同颜色的标签

### 2. 新增事件功能
- **基本信息**：事件来源、事件标题、事件描述
- **分类分级**：事件分类、事件分级
- **时间信息**：发生时间、接收时间、处理完成时间
- **位置信息**：事件位置、位置坐标（支持地图选点）
- **人员伤亡**：是否伤亡、死亡人数、受伤人数
- **联系信息**：上报人员联系方式
- **处置信息**：关联处置方案、事件处置状态
- **备注信息**：备注

### 3. 编辑事件功能
- 获取事件详情数据回填表单
- 支持所有字段的修改
- 表单验证和数据提交

### 4. 查看详情功能
- 只读模式显示事件详细信息
- 表单字段禁用编辑

### 5. 删除事件功能
- 二次确认删除操作
- 删除成功后刷新列表

### 6. 地图定位功能
- 表格中的定位按钮
- 支持经纬度定位
- 地图选点功能

## 表单字段映射

### 主要字段说明
| 字段名 | 类型 | 说明 | 必填 |
|--------|------|------|------|
| eventSourceName | String | 事件来源 | 是 |
| eventTitle | String | 事件标题 | 是 |
| eventDesc | String | 事件描述 | 是 |
| eventType | Number | 事件分类 | 是 |
| eventLevel | Number | 事件分级 | 是 |
| eventTime | String | 发生时间 | 是 |
| ownershipUnit | String | 事件位置 | 否 |
| longitude | String | 经度 | 否 |
| latitude | String | 纬度 | 否 |
| isCasualty | String | 是否人员伤亡 | 是 |
| deathNum | Number | 死亡人数 | 否 |
| injuredNum | Number | 受伤人数 | 否 |
| contactInfo | String | 联系方式 | 否 |
| receiveTime | String | 接收时间 | 否 |
| handleTime | String | 处理完成时间 | 否 |
| eventStatus | Number | 事件处置状态 | 是 |
| remarks | String | 备注 | 否 |

### 数据格式处理
- **时间字段**：使用moment.js格式化为`YYYY-MM-DD HH:mm:ss`
- **下拉选项**：使用数字编码，显示时转换为对应名称
- **人员伤亡**：字符串类型，'0'表示否，'1'表示是

## 样式设计

### 1. 布局风格
- 参考`building.vue`的布局设计
- 搜索区域 + 操作按钮 + 数据表格 + 分页组件
- 响应式设计，支持不同屏幕尺寸

### 2. 弹窗样式
- 参考`BuildingDialog.vue`的设计规范
- 1000px宽度的模态对话框
- 统一的表单布局和样式
- 蓝色主题按钮设计

### 3. 状态标签
- 未处置：红色（danger）
- 处置中：橙色（warning）
- 已处置：绿色（success）

## 技术特性

### 1. 响应式设计
- 使用Vue 3 Composition API
- 响应式数据绑定
- 组件化开发

### 2. 表单验证
- Element Plus表单验证
- 必填字段检查
- 自定义验证规则

### 3. 错误处理
- API调用异常处理
- 用户友好的错误提示
- 加载状态管理

### 4. 地图集成
- 支持地图选点功能
- 经纬度坐标显示
- 定位按钮快速跳转

## 代码质量

### 1. 代码规范
- 统一的命名规范
- 清晰的注释说明
- 模块化设计

### 2. 性能优化
- 按需加载组件
- 合理的数据结构
- 高效的渲染机制

### 3. 可维护性
- 分离关注点
- 可复用的组件设计
- 易于扩展的架构

## 验证结果
- ✅ 代码构建成功，无语法错误
- ✅ 所有功能模块完整实现
- ✅ UI界面与设计稿一致
- ✅ API接口调用正常
- ✅ 表单验证工作正常

## 使用说明

### 1. 访问页面
导航到：排水管理 -> 辅助决策支持 -> 应急事件管理

### 2. 基本操作
- **查询**：设置筛选条件后点击"查询"按钮
- **重置**：点击"重置"按钮清空筛选条件
- **新增**：点击"+ 新增"按钮打开新增弹窗
- **编辑**：点击表格行中的"编辑"按钮
- **详情**：点击表格行中的"详情"按钮
- **删除**：点击表格行中的"删除"按钮并确认
- **定位**：点击表格行中的"定位"按钮在地图上显示位置

### 3. 表单填写
- 必填字段标有红色星号
- 时间字段支持日期时间选择器
- 地图选点通过点击坐标输入框旁的定位按钮
- 人员伤亡选择"是"时显示死亡和受伤人数输入框
- 处置方案根据事件分类自动匹配

## 扩展建议

### 1. 功能扩展
- 添加文件上传功能（现场照片、相关文档）
- 增加事件处置流程跟踪
- 支持事件状态变更日志
- 添加统计分析功能

### 2. 界面优化
- 添加更多筛选条件
- 支持表格列的自定义显示
- 增加数据导出功能
- 优化移动端显示

### 3. 性能优化
- 实现虚拟滚动大数据表格
- 添加数据缓存机制
- 优化API请求频率
- 实现增量数据更新 