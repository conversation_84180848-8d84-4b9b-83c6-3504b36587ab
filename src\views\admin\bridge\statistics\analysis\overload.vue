<template>
  <div class="bridge-overload-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <el-form :model="filterForm" ref="filterFormRef" inline class="filter-form">
          <el-form-item label="桥梁选择" prop="bridgeId">
            <el-select v-model="filterForm.bridgeId" placeholder="请选择桥梁" style="width: 200px;" clearable>
              <el-option
                v-for="bridge in bridgeList"
                :key="bridge.id"
                :label="bridge.bridgeName"
                :value="bridge.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围" prop="dateRange">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="searchLoading">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="content-section">
      <!-- 第一行：重量分布和次数分布 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>超载重量分布</h3>
            <div class="chart-info">
              <span class="total-count">总计：{{ getDistributionTotal(weightDistributionData) }}次</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="weightDistributionChartRef" class="chart-container"></div>
            <div class="chart-legend-right">
              <div v-for="item in weightDistributionData" :key="item.code" class="legend-item">
                <span class="legend-color" :style="{ backgroundColor: getWeightColor(item.code) }"></span>
                <span class="legend-text">{{ item.name }}</span>
                <span class="legend-count">{{ item.count }}次</span>
                <span class="legend-percent">({{ item.percent }}%)</span>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>超载次数分布</h3>
            <div class="chart-info">
              <span class="total-count">总计：{{ getDistributionTotal(countDistributionData) }}次</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="countDistributionChartRef" class="chart-container"></div>
            <div class="chart-legend-right">
              <div v-for="item in countDistributionData" :key="item.code" class="legend-item">
                <span class="legend-color" :style="{ backgroundColor: getCountColor(item.code) }"></span>
                <span class="legend-text">{{ item.name }}</span>
                <span class="legend-count">{{ item.count }}次</span>
                <span class="legend-percent">({{ item.percent }}%)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行：来源分布和时间分布 -->
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>超载车辆来源分布</h3>
            <div class="chart-info">
              <span class="total-count">车辆数：{{ getDistributionTotal(sourceDistributionData) }}辆</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="sourceDistributionChartRef" class="chart-container bar-chart"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>超载车辆载重时间分布</h3>
            <div class="chart-info">
              <span class="total-count">数据点：{{ getDistributionTotal(timeDistributionData) }}个</span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="timeDistributionChartRef" class="chart-container scatter-chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import moment from 'moment'
import {
  getBridgeBasicInfoList,
  getOverloadWeightStatistics,
  getOverloadCountStatistics,
  getOverloadSourceStatistics,
  getOverloadTimeStatistics
} from '@/api/bridge'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

// 表单引用
const filterFormRef = ref(null)

// 筛选表单
const filterForm = reactive({
  bridgeId: '',
  dateRange: []
})

// 基础数据
const bridgeList = ref([])

// 统计数据
const weightDistributionData = ref([])
const countDistributionData = ref([])
const sourceDistributionData = ref([])
const timeDistributionData = ref([])

// 图表引用
const weightDistributionChartRef = ref(null)
const countDistributionChartRef = ref(null)
const sourceDistributionChartRef = ref(null)
const timeDistributionChartRef = ref(null)

// 图表实例
let weightDistributionChart = null
let countDistributionChart = null
let sourceDistributionChart = null
let timeDistributionChart = null

// 计算属性 - 获取分布统计总数
const getDistributionTotal = (data) => {
  return data.reduce((total, item) => total + (item.count || 0), 0)
}

// 获取重量分布颜色
const getWeightColor = (code) => {
  const colorMap = {
    '1': '#67c23a',
    '2': '#e6a23c',
    '3': '#f56c6c',
    '4': '#909399'
  }
  return colorMap[code] || '#409eff'
}

// 获取次数分布颜色
const getCountColor = (code) => {
  const colorMap = {
    '1': '#409eff',
    '2': '#67c23a',
    '3': '#e6a23c',
    '4': '#f56c6c'
  }
  return colorMap[code] || '#909399'
}

// 加载桥梁列表
const loadBridgeList = async () => {
  try {
    const response = await getBridgeBasicInfoList()
    if (response.code === 200 && response.data) {
      bridgeList.value = Array.isArray(response.data) ? response.data : []
    }
  } catch (error) {
    console.error('加载桥梁列表失败:', error)
    ElMessage.error('加载桥梁列表失败')
  }
}

// 获取查询参数
const getQueryParams = () => {
  const params = {
    bridgeId: filterForm.bridgeId || '',
    startDate: '',
    endDate: ''
  }

  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    params.startDate = filterForm.dateRange[0]
    params.endDate = filterForm.dateRange[1]
  }

  return params
}

// 加载重量分布数据
const loadWeightDistribution = async () => {
  try {
    const params = getQueryParams()
    const response = await getOverloadWeightStatistics(params)
    if (response.code === 200 && response.data) {
      weightDistributionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderWeightDistributionChart()
    }
  } catch (error) {
    console.error('加载重量分布数据失败:', error)
    ElMessage.error('加载重量分布数据失败')
  }
}

// 加载次数分布数据
const loadCountDistribution = async () => {
  try {
    const params = getQueryParams()
    const response = await getOverloadCountStatistics(params)
    if (response.code === 200 && response.data) {
      countDistributionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderCountDistributionChart()
    }
  } catch (error) {
    console.error('加载次数分布数据失败:', error)
    ElMessage.error('加载次数分布数据失败')
  }
}

// 加载来源分布数据
const loadSourceDistribution = async () => {
  try {
    const params = getQueryParams()
    const response = await getOverloadSourceStatistics(params)
    if (response.code === 200 && response.data) {
      sourceDistributionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderSourceDistributionChart()
    }
  } catch (error) {
    console.error('加载来源分布数据失败:', error)
    ElMessage.error('加载来源分布数据失败')
  }
}

// 加载时间分布数据
const loadTimeDistribution = async () => {
  try {
    const params = getQueryParams()
    const response = await getOverloadTimeStatistics(params)
    if (response.code === 200 && response.data) {
      timeDistributionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderTimeDistributionChart()
    }
  } catch (error) {
    console.error('加载时间分布数据失败:', error)
    ElMessage.error('加载时间分布数据失败')
  }
}

// 渲染重量分布图表
const renderWeightDistributionChart = () => {
  if (!weightDistributionChart) {
    weightDistributionChart = echarts.init(weightDistributionChartRef.value)
  }

  const data = weightDistributionData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getWeightColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}次 ({d}%)'
    },
    series: [
      {
        name: '重量分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  weightDistributionChart.setOption(option)
}

// 渲染次数分布图表
const renderCountDistributionChart = () => {
  if (!countDistributionChart) {
    countDistributionChart = echarts.init(countDistributionChartRef.value)
  }

  const data = countDistributionData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getCountColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}次 ({d}%)'
    },
    series: [
      {
        name: '次数分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  countDistributionChart.setOption(option)
}

// 渲染来源分布图表（柱状图）
const renderSourceDistributionChart = () => {
  if (!sourceDistributionChart) {
    sourceDistributionChart = echarts.init(sourceDistributionChartRef.value)
  }

  const xAxisData = sourceDistributionData.value.map(item => item.name)
  const seriesData = sourceDistributionData.value.map((item, index) => ({
    value: item.count || 0,
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'][index % 5] },
        { offset: 1, color: ['#5470c620', '#91cc7520', '#fac85820', '#ee666620', '#73c0de20'][index % 5] }
      ])
    }
  }))

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: '{b}: {c}辆'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '车辆数(辆)'
    },
    series: [
      {
        name: '车辆数',
        type: 'bar',
        barWidth: '60%',
        data: seriesData,
        animationDelay: (idx) => idx * 100
      }
    ],
    animationEasing: 'elasticOut',
    animationDelayUpdate: (idx) => idx * 100
  }

  sourceDistributionChart.setOption(option)
}

// 渲染时间分布图表（散点图）
const renderTimeDistributionChart = () => {
  if (!timeDistributionChart) {
    timeDistributionChart = echarts.init(timeDistributionChartRef.value)
  }

  // 将时间数据转换为散点图格式
  const scatterData = timeDistributionData.value.map((item, index) => [
    index, // x轴：序号
    item.count || 0, // y轴：数量
    item.name // 用于tooltip显示
  ])

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const [x, y, name] = params.data
        return `${name}<br/>数量: ${y}个`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeDistributionData.value.map(item => item.name),
      name: '时间段',
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '载重数量(个)',
      scale: true
    },
    series: [
      {
        name: '载重时间分布',
        type: 'scatter',
        symbolSize: (value) => Math.max(8, value[1] / 5), // 根据数量调整点的大小
        itemStyle: {
          color: '#5470c6',
          opacity: 0.8
        },
        emphasis: {
          itemStyle: {
            color: '#c23531',
            opacity: 1
          }
        },
        data: scatterData
      }
    ]
  }

  timeDistributionChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  if (weightDistributionChartRef.value) {
    weightDistributionChart = echarts.init(weightDistributionChartRef.value)
  }
  if (countDistributionChartRef.value) {
    countDistributionChart = echarts.init(countDistributionChartRef.value)
  }
  if (sourceDistributionChartRef.value) {
    sourceDistributionChart = echarts.init(sourceDistributionChartRef.value)
  }
  if (timeDistributionChartRef.value) {
    timeDistributionChart = echarts.init(timeDistributionChartRef.value)
  }

  // 监听窗口大小变化
  const handleResize = () => {
    weightDistributionChart?.resize()
    countDistributionChart?.resize()
    sourceDistributionChart?.resize()
    timeDistributionChart?.resize()
  }
  
  window.addEventListener('resize', handleResize)
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadWeightDistribution(),
      loadCountDistribution(),
      loadSourceDistribution(),
      loadTimeDistribution()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = async () => {
  searchLoading.value = true
  try {
    await loadAllData()
  } finally {
    searchLoading.value = false
  }
}

// 重置处理
const handleReset = () => {
  filterFormRef.value?.resetFields()
  filterForm.bridgeId = ''
  filterForm.dateRange = []
  handleSearch()
}

// 生命周期
onMounted(async () => {
  // 初始化时间范围为近7天
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss')
  filterForm.dateRange = [startTime, endTime]

  // 加载基础数据
  await loadBridgeList()

  // 初始化图表
  await initCharts()

  // 加载统计数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (weightDistributionChart) {
    weightDistributionChart.dispose()
    weightDistributionChart = null
  }
  if (countDistributionChart) {
    countDistributionChart.dispose()
    countDistributionChart = null
  }
  if (sourceDistributionChart) {
    sourceDistributionChart.dispose()
    sourceDistributionChart = null
  }
  if (timeDistributionChart) {
    timeDistributionChart.dispose()
    timeDistributionChart = null
  }
  
  window.removeEventListener('resize', () => {
    weightDistributionChart?.resize()
    countDistributionChart?.resize()
    sourceDistributionChart?.resize()
    timeDistributionChart?.resize()
  })
})
</script>

<style scoped>
.bridge-overload-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
}

.filter-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.filter-form {
  margin: 0;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 24px;
}

:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

/* 内容区域 */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-row {
  display: flex;
  gap: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
  min-height: 500px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-count {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.chart-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.chart-container {
  height: 400px;
  /* flex: 1; */
  min-width: 300px;
}

.chart-container.bar-chart,
.chart-container.scatter-chart {
  width: 100%;
}

.chart-legend-right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 14px;
  color: #606266;
  min-width: 80px;
}

.legend-count {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  min-width: 40px;
}

.legend-percent {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .chart-row {
    flex-direction: column;
  }

  .chart-content {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-container {
    height: 350px;
    min-width: auto;
  }

  .chart-legend-right {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .bridge-overload-analysis-container {
    padding: 15px;
  }

  .filter-card {
    padding: 20px;
  }

  :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .filter-form {
    display: flex;
    flex-direction: column;
  }

  .chart-card {
    padding: 20px;
    min-height: 450px;
  }

  .chart-container {
    height: 300px;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>