# 角色管理功能实现说明

## 已实现功能

### 📁 文件结构
```
src/
├── api/system.js                     # 系统管理API接口
├── constants/system.js               # 系统管理常量定义
└── views/admin/system/management/admin/
    ├── role.vue                      # 角色管理主页面
    └── components/
        └── RoleDialog.vue            # 角色对话框组件
```

### 🔧 API接口 (src/api/system.js)
- ✅ `getRolePage()` - 分页查询角色列表
- ✅ `saveRole()` - 保存角色
- ✅ `updateRole()` - 更新角色
- ✅ `deleteRole()` - 删除角色
- ✅ `getPermissionTree()` - 获取权限树
- ✅ `distributePermission()` - 分配功能权限
- ✅ `distributeDataScope()` - 分配数据权限
- ✅ `getDeptTree()` - 获取部门树
- ✅ `getRolePermissions()` - 获取角色权限
- ✅ `searchUsers()` - 查询关联用户

### 📊 常量定义 (src/constants/system.js)
- ✅ `DATA_SCOPE_OPTIONS` - 数据范围选项
- ✅ `ROLE_STATUS_OPTIONS` - 角色状态选项
- ✅ `STATUS_TEXT_MAP` - 状态文本映射
- ✅ `DATA_SCOPE_TEXT_MAP` - 数据范围文本映射

### 🖥️ 主页面功能 (role.vue)
- ✅ 角色列表展示
- ✅ 条件查询（角色名称）
- ✅ 分页功能
- ✅ 状态开关控制（正常/停用）
- ✅ 新增角色
- ✅ 编辑角色
- ✅ 查看角色详情
- ✅ 权限设置
- ✅ 删除角色（仅停用状态可删除）

### 🔧 对话框组件功能 (RoleDialog.vue)
#### 基础功能
- ✅ 新增角色表单
- ✅ 编辑角色表单
- ✅ 查看角色详情（只读模式）
- ✅ 表单验证（必填项检查）

#### 权限设置（标签页模式）
- ✅ **基础角色** - 显示角色基本信息
- ✅ **功能权限** - 权限树展示和勾选
- ✅ **数据权限** - 数据范围选择（5种类型）
  - 仅本人数据
  - 本部门数据
  - 本部门及以下数据
  - 指定部门数据（可选择具体部门）
  - 全部数据
- ✅ **关联用户** - 显示当前角色下的用户列表

### 🎨 UI设计特点
- ✅ 响应式布局
- ✅ 符合设计稿样式
- ✅ 参考building.vue布局风格
- ✅ 使用Element UI组件库
- ✅ 统一的颜色主题 (#0277FD)
- ✅ 表格隔行变色
- ✅ 操作按钮禁用状态处理

### 📋 业务逻辑
1. **状态控制**
   - 正常状态可切换为停用
   - 停用状态可切换为正常
   - 只有停用状态的角色才能删除

2. **权限设置**
   - 支持树形权限结构
   - 支持半选状态
   - 数据权限与功能权限分离管理
   - 指定部门权限可选择具体部门

3. **表单验证**
   - 角色名称必填
   - 描述必填
   - 数据权限范围必选

### 🔄 数据流程
1. **查询流程**: 页面加载 → 调用API → 渲染表格
2. **新增流程**: 点击新增 → 填写表单 → 自动添加当前用户deptId → 提交 → 刷新列表
3. **编辑流程**: 点击编辑 → 加载数据 → 修改表单 → 提交 → 刷新列表
4. **权限设置**: 选择角色 → 加载权限数据 → 修改权限 → 保存 → 刷新

### ⚠️ 注意事项
1. 时间字段使用 moment.js 格式化为 'YYYY-MM-DD HH:mm:ss'
2. 权限树和部门树需要等待接口返回数据后再渲染
3. 状态切换有确认对话框，取消时需要回滚状态
4. 删除操作只对停用状态的角色有效
5. **新增角色时会自动添加当前登录用户的deptId字段**
6. 对话框打开时会自动获取用户信息以确保deptId可用

### 🚀 使用方式
1. 导航到 "系统管理 > 角色管理" 页面
2. 可进行角色的增删改查操作
3. 通过权限设置配置角色的功能权限和数据权限
4. 通过状态开关控制角色的启用/停用状态

## 技术栈
- Vue 3 Composition API
- Element Plus UI组件库
- Moment.js 时间处理
- 响应式设计
- 组件化开发 