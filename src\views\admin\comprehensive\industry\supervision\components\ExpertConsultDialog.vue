<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="expert-consult-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <!-- 回复模式：只显示回复内容 -->
      <template v-if="isReplyMode">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="回复内容" prop="replyContent">
              <el-input
                v-model="formData.replyContent"
                type="textarea"
                :rows="10"
                placeholder="请输入回复内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 非回复模式：显示完整表单 -->
      <template v-else>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="专家姓名" prop="expertId">
              <el-select v-model="formData.expertId" placeholder="请选择专家" class="w-full" @change="handleExpertChange">
                <el-option 
                  v-for="item in expertOptions" 
                  :key="item.id" 
                  :label="item.expertName" 
                  :value="item.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询人" prop="consultUser">
              <el-input v-model="formData.consultUser" placeholder="请输入咨询人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="咨询人联系电话" prop="consultContact">
              <el-input v-model="formData.consultContact" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="mode === 'view'">
            <el-form-item label="咨询状态">
              <el-select v-model="formData.consultStatus" placeholder="请选择状态" class="w-full" disabled>
                <el-option 
                  v-for="item in consultStatusOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="咨询内容" prop="consultContent">
              <el-input
                v-model="formData.consultContent"
                type="textarea"
                :rows="4"
                placeholder="请输入咨询内容"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="mode === 'view' && formData.replyContent">
          <el-col :span="24">
            <el-form-item label="回复内容">
              <el-input
                v-model="formData.replyContent"
                type="textarea"
                :rows="4"
                placeholder="请输入回复内容"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import {
  saveExpertConsult,
  updateExpertConsult,
  getEmergencyExpertList,
} from '@/api/comprehensive'
import { CONSULT_STATUS_OPTIONS } from '@/constants/comprehensive'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view', 'reply'
    validator: (value) => ['add', 'edit', 'view', 'reply'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增专家咨询',
    edit: '编辑专家咨询',
    view: '专家咨询详情',
    reply: '回复'
  }
  return titles[props.mode] || '专家咨询'
})

// 是否为回复模式
const isReplyMode = computed(() => props.mode === 'reply')

// 下拉选项数据
const expertOptions = ref([])
const consultStatusOptions = ref(CONSULT_STATUS_OPTIONS)

// 表单数据
const formData = reactive({
  id: '',
  expertId: '',
  expertName: '',
  consultUser: '',
  consultContact: '',
  consultContent: '',
  consultStatus: 7001101, // 默认未回复
  consultStatusName: '',
  consultTime: '',
  replyContent: '',
  replyTime: '',
  remark: ''
})

// 表单验证规则
const formRules = computed(() => {
  if (isReplyMode.value) {
    // 回复模式的验证规则
    return {
      replyContent: [{ required: true, message: '请输入回复内容', trigger: 'blur' }]
    }
  } else {
    // 普通模式的验证规则
    return {
      expertId: [{ required: true, message: '请选择专家', trigger: 'change' }],
      consultUser: [{ required: true, message: '请输入咨询人姓名', trigger: 'blur' }],
      consultContact: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      consultContent: [{ required: true, message: '请输入咨询内容', trigger: 'blur' }]
    }
  }
})

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'consultStatus') {
      formData[key] = 7001101
    } else {
      formData[key] = ''
    }
  })
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理专家选择变化
const handleExpertChange = (value) => {
  const selected = expertOptions.value.find(item => item.id === value)
  if (selected) {
    formData.expertName = selected.expertName
  }
}

// 获取应急专家列表
const fetchExpertList = async () => {
  try {
    const res = await getEmergencyExpertList({})
    if (res && res.code === 200) {
      expertOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取专家列表失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }
    
    // 设置咨询时间（新增时）
    if (props.mode === 'add') {
      submitData.consultTime = moment().format('YYYY-MM-DD HH:mm:ss')
    }
    
    // 设置回复时间（回复时）
    if (isReplyMode.value && submitData.replyContent) {
      submitData.replyTime = moment().format('YYYY-MM-DD HH:mm:ss')
      submitData.consultStatus = 7001102 // 设置为已回复
    }

    // 更新状态名称
    const selectedStatus = consultStatusOptions.value.find(item => item.value === submitData.consultStatus)
    if (selectedStatus) {
      submitData.consultStatusName = selectedStatus.label
    }

    let res
    if (props.mode === 'add') {
      res = await saveExpertConsult(submitData)
    } else if (props.mode === 'edit' || props.mode === 'reply') {
      res = await updateExpertConsult(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : (isReplyMode.value ? '回复成功' : '更新成功'))
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : (isReplyMode.value ? '回复失败' : '更新失败')))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchExpertList()
})
</script>

<style scoped>
.expert-consult-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 