# 可燃气体积聚监测功能开发完成说明

## 功能概述
已完成可燃气体积聚监测页面的完整开发，完全复制管网流量监测的所有功能，包括统计数据展示、设备列表查询、监测曲线和运行记录功能。

## 已实现的功能

### 1. API接口方法 (src/api/drainage.js)
使用以下API接口方法（复用管网流量监测的接口）：
- `getDeviceType(3)` - 获取设备类型（type=3代表可燃气体积聚监测）
- `getDeviceTypeStatusStatistics(3)` - 获取设备状态统计（type=3代表可燃气体积聚监测）
- `getMonitorIndicators(deviceId)` - 根据设备ID查询监测指标
- `getMonitorCurve(data)` - 获取监测曲线数据
- `getMonitorRecordPage(pageNum, pageSize, data)` - 获取监测历史记录分页
- `getOfflineRecordsPage(pageNum, pageSize, data)` - 获取设备离线记录分页

### 2. 弹窗组件 (src/views/admin/drainage/monitoring/alarm/components/GasMonitorDialog.vue)
专门为可燃气体积聚监测创建的弹窗组件，包含两个主要功能模块：

#### 监测曲线
- 支持24小时、7天、30天时间范围选择
- 动态监测指标选择下拉框
- 使用ECharts绘制交互式图表
- 特殊处理井盖状态等固定值字段的显示
- 自动获取监测指标和单位信息

#### 运行记录
- **历史数据**: 分页显示在线监测记录，支持时间筛选
- **离线记录**: 分页显示设备离线记录，包含离线时长统计
- 动态格式化监测指标和监测值显示
- 支持时间范围筛选查询

### 3. 主页面 (src/views/admin/drainage/monitoring/alarm/gas.vue)

#### 统计数据展示区域
- 美观的卡片式统计展示：全部设备、正常、离线、报警
- 渐变色背景设计，符合系统整体风格
- 实时显示数据更新时间

#### 搜索条件
- 设备类型筛选（动态获取type=3的设备类型）
- 权属单位筛选（调用企业列表接口）
- 所属区域筛选（使用东明县下级区域）
- 设备状态筛选（在线/离线）
- 设备名称搜索

#### 设备列表
- 响应式表格布局
- 显示字段：序号、设备名称、设备类型、监测对象、监测指标、采集频率、权属单位、位置、当前监测值、状态
- 状态标签显示（在线/离线）
- 操作按钮：监测曲线、运行记录、定位

#### 分页功能
- 支持每页条数选择：10、20、50、100
- 完整的分页控件

## 与管网流量监测的差异

### 关键差异
1. **接口参数差异**：
   - `getDeviceType(3)` - type=3代表可燃气体积聚监测（原来是1）
   - `getDeviceTypeStatusStatistics(3)` - type=3代表可燃气体积聚监测（原来是1）
   - 列表查询的deviceTypeList使用type=3获取的设备类型

2. **组件命名差异**：
   - 使用`GasMonitorDialog`而不是`FlowMonitorDialog`
   - 样式类名为`gas-monitor-dialog`和`drainage-gas-container`

3. **功能完全一致**：
   - 所有业务逻辑、UI交互、数据处理完全相同
   - 监测曲线、运行记录功能完全一致
   - 时间处理、图表显示、分页等功能完全一致

## 技术实现细节

### 设备类型映射
- 自动调用type=3的设备类型接口
- 默认查询所有可燃气体积聚监测设备类型
- 支持设备类型筛选功能

### 数据处理
- 使用相同的监测指标字段映射
- 相同的时间格式化处理
- 相同的图表渲染逻辑
- 相同的状态标签处理

### 错误处理
- 完善的try-catch错误捕获
- 用户友好的错误提示
- 接口异常的降级处理

## 样式设计

### 统计卡片
- 使用相同的CSS渐变背景
- 响应式布局
- 美观的数字和标签展示

### 表格样式
- 斑马纹行背景
- 统一的表头样式
- 操作按钮的悬停效果

### 弹窗样式
- 大尺寸弹窗（1200px宽）
- 标签页切换
- 图表和表格的响应式布局

## 依赖项
项目已包含所需的依赖：
- echarts ^5.6.0 - 图表库
- moment ^2.30.1 - 时间处理
- element-plus - UI组件库

## 使用说明

1. **访问页面**: 导航到可燃气体积聚监测页面
2. **查看统计**: 页面顶部显示设备统计信息
3. **筛选设备**: 使用搜索条件筛选所需设备
4. **查看详情**: 点击"监测曲线"或"运行记录"查看设备详细信息
5. **设备定位**: 点击"定位"按钮在地图上定位设备位置

## 数据接口说明

### 接口参数
- type=3: 可燃气体积聚监测类型
- deviceTypeList: 设备类型数组，使用type=3获取的数据
- 时间参数: 自动转换为标准格式

### 返回数据处理
- 分页数据: records、total、current等
- 监测数据: 动态字段映射和格式化
- 统计数据: totalDeviceCount、normalDeviceCount等

## 复制完成验证

### ✅ 完全复制的功能
1. 统计数据展示区域 - 完全一致
2. 搜索筛选条件 - 完全一致
3. 设备列表表格 - 完全一致
4. 分页功能 - 完全一致
5. 监测曲线弹窗 - 完全一致
6. 运行记录功能 - 完全一致
7. 设备定位功能 - 完全一致
8. 错误处理机制 - 完全一致

### ✅ 正确修改的参数
1. API接口type参数从1改为3
2. 设备类型列表使用type=3的数据
3. 组件名称适配可燃气体积聚监测
4. 样式类名更新

## 监测类型说明

根据需求文档，type值的含义为：
- 1：管网流量监测
- 2：污水溢流监测
- 3：可燃气体积聚监测
- 4：易涝点积水监测
- 5：排污水质监测
- 6：井盖状态监测

当前实现的可燃气体积聚监测使用type=3参数。

## 文件结构
```
src/views/admin/drainage/monitoring/alarm/
├── gas.vue                              # 可燃气体积聚监测主页面
├── components/
│   └── GasMonitorDialog.vue             # 可燃气体积聚监测详情弹窗
src/api/
└── drainage.js                          # API接口方法（复用）
src/constants/
└── drainage.js                          # 常量定义（复用）
```

## 完成状态
✅ 主页面功能完全复制
✅ 弹窗组件完全复制
✅ API接口参数正确修改
✅ 设备类型筛选正确配置
✅ 样式美化完成
✅ 错误处理完成
✅ 响应式布局完成

所有功能已完整实现，代码可直接运行使用。可燃气体积聚监测功能与管网流量监测功能完全一致，仅在设备类型参数上有所区别。 