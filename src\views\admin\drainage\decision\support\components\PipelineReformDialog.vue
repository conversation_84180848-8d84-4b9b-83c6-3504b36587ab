<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="pipeline-reform-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="管线编号" prop="pipelineId">
            <el-select v-model="formData.pipelineId" placeholder="请选择管线" class="w-full" @change="handlePipelineChange">
              <el-option v-for="item in pipelineOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问题类型" prop="problemType">
            <el-select v-model="formData.problemType" placeholder="请选择" class="w-full" @change="handleProblemTypeChange">
              <el-option v-for="item in problemTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="改造方案" prop="reformScheme">
            <el-input v-model="formData.reformScheme" type="textarea" :rows="4" placeholder="请输入改造方案详情" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  savePipelineReformScheme,
  updatePipelineReformScheme,
  getPipelineList
} from '@/api/drainage';
import { PIPELINE_PROBLEM_TYPE_OPTIONS } from '@/constants/drainage';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增管网改造方案',
    edit: '编辑管网改造方案',
    view: '管网改造方案详情'
  };
  return titles[props.mode] || '管网改造方案';
});

// 下拉选项数据
const problemTypeOptions = PIPELINE_PROBLEM_TYPE_OPTIONS;
const pipelineOptions = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  pipelineId: '',
  problemType: '',
  problemTypeName: '',
  reformScheme: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: ''
});

// 表单验证规则
const formRules = {
  pipelineId: [{ required: true, message: '请选择管线', trigger: 'change' }],
  problemType: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
  reformScheme: [{ required: true, message: '请输入改造方案', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理管线变化
const handlePipelineChange = (value) => {
  // 这里可以根据需要获取管线详情
  console.log('管线变化:', value);
};

// 处理问题类型变化
const handleProblemTypeChange = (value) => {
  const selected = problemTypeOptions.find(item => item.value === value);
  if (selected) {
    formData.problemTypeName = selected.label;
  }
};

// 获取管线列表
const fetchPipelineList = async () => {
  try {
    const res = await getPipelineList({});
    if (res && res.data) {
      pipelineOptions.value = res.data.map(item => ({
        label: `${item.pipelineCode || item.id} - ${item.pipelineType || ''}`,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取管线列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 设置问题类型名称
    const selected = problemTypeOptions.find(item => item.value === formData.problemType);
    if (selected) {
      formData.problemTypeName = selected.label;
    }

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await savePipelineReformScheme(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePipelineReformScheme(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPipelineList();
});
</script>

<style scoped>
.pipeline-reform-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}
</style> 