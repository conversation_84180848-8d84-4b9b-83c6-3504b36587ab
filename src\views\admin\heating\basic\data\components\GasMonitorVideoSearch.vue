<template>
  <div class="gas-monitor-video-search">
    <div class="search-form">
      <div class="form-item">
        <span class="label">设备状态:</span>
        <el-select v-model="formData.productStatic" class="form-input" placeholder="请选择">
          <el-option label="全部" value="all" />
          <el-option label="使用中" value="001" />
          <el-option label="未使用" value="002" />
          <el-option label="已废弃" value="003" />
        </el-select>
      </div>
      <div class="form-item" style="margin-left: auto;">
        <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
        <el-button class="reset-btn" @click="handleReset">重置</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElSelect, ElOption, ElButton } from 'element-plus';

const emit = defineEmits(['search', 'reset']);

// 表单数据
const formData = ref({
  productStatic: 'all'
});

// 处理查询
const handleSearch = () => {
  emit('search', formData.value);
};

// 处理重置
const handleReset = () => {
  formData.value = {
    productStatic: 'all'
  };
  emit('reset');
};
</script>

<style scoped>
.gas-monitor-video-search {
  width: 100%;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-select .el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

:deep(.el-select .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #0277FD inset !important;
}

.search-btn {
  width: 60px;
  height: 32px;
  background: #0277FD;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 0;
  margin-right: 8px;
}

.reset-btn {
  width: 60px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  padding: 0;
}
</style>