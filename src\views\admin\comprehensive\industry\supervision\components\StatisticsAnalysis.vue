<template>
  <div class="statistics-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-form">
        <div class="form-item">
          <span class="label">所属区域:</span>
          <el-select v-model="filterForm.town" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" /> 
            <el-option v-for="item in areaOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">项目类型:</span>
          <el-select v-model="filterForm.projectType" class="form-input" placeholder="类型">
            <el-option label="全部" value="" />
            <el-option v-for="item in projectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleFilter">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 图表和表格区域 -->
    <div class="content-section">
      <!-- 左侧：项目进度统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>项目进度统计</h3>
          <div class="chart-info">
            <span class="total-count">全部项目：{{ progressStatistics.totalCount || 0 }}</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="progressChartRef" class="chart-container"></div>
          <div class="chart-legend-right">
            <div v-for="item in progressData" :key="item.code" class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: getProgressColor(item.code) }"></span>
              <span class="legend-text">{{ item.name }}</span>
              <span class="legend-count">{{ item.count }}个</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：所属行业统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>所属行业统计</h3>
        </div>
        <div class="chart-content">
          <div ref="businessChartRef" class="chart-container"></div>
          <div class="chart-legend-right">
            <div v-for="item in businessData" :key="item.code" class="legend-item">
              <span class="legend-color" :style="{ backgroundColor: getBusinessColor(item.code) }"></span>
              <span class="legend-text">{{ item.name }}</span>
              <span class="legend-count">{{ item.count }}个</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二行图表 -->
    <div class="content-section">
      <!-- 左侧：项目规模统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>项目规模统计</h3>
          <div class="chart-info">
            <span class="unit-label">单位：万元</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="scaleChartRef" class="chart-container full-width"></div>
        </div>
      </div>

      <!-- 右侧：在建项目统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>在建项目统计</h3>
          <div class="chart-info">
            <span class="unit-label">单位：个</span>
          </div>
        </div>
        <div class="chart-content">
          <div ref="constructionChartRef" class="chart-container full-width"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getProjectProgressStatistics,
  getProjectRelatedBusinessStatistics,
  getProjectScaleStatistics,
  getProjectConstructionStatistics
} from '@/api/comprehensive'
import { PROJECT_TYPE_OPTIONS } from '@/constants/comprehensive'
import { AREA_OPTIONS } from '@/constants/gas'

// 响应式数据
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  town: '', // 默认沙窝镇
  projectType: ''
})

// 项目类型选项
const projectTypeOptions = PROJECT_TYPE_OPTIONS

// 区域选项
const areaOptions = ref([])

// 初始化区域选项
const initAreaOptions = () => {
  // 从AREA_OPTIONS中提取东明县的children
  const dongmingCounty = AREA_OPTIONS.find(area => area.code === '371728')
  if (dongmingCounty && dongmingCounty.children) {
    areaOptions.value = dongmingCounty.children.map(child => ({
      label: child.name,
      value: child.code
    }))
  }
}

// 统计数据
const progressStatistics = reactive({
  totalCount: 0
})

const progressData = ref([])
const businessData = ref([])
const scaleData = ref([])
const constructionData = ref([])

// 图表引用
const progressChartRef = ref(null)
const businessChartRef = ref(null)
const scaleChartRef = ref(null)
const constructionChartRef = ref(null)

let progressChart = null
let businessChart = null
let scaleChart = null
let constructionChart = null

// 获取进度状态颜色
const getProgressColor = (code) => {
  const colorMap = {
    '7000401': '#f56c6c', // 未开始 - 红色
    '7000402': '#409eff', // 进行中 - 蓝色
    '7000403': '#67c23a'  // 已完成 - 绿色
  }
  return colorMap[code] || '#909399'
}

// 获取行业颜色
const getBusinessColor = (code) => {
  const colorMap = {
    '7000501': '#409eff', // 燃气 - 蓝色
    '7000502': '#67c23a', // 排水 - 绿色
    '7000503': '#e6a23c', // 供热 - 橙色
    '7000504': '#f56c6c'  // 桥梁 - 红色
  }
  return colorMap[code] || '#909399'
}

// 加载项目进度统计
const loadProgressStatistics = async () => {
  try {
    const params = {
      projectType: filterForm.projectType || 0,
      town: filterForm.town || ''
    }
    
    const response = await getProjectProgressStatistics(params)
    if (response.code === 200 && response.data) {
      progressData.value = Array.isArray(response.data) ? response.data : []
      progressStatistics.totalCount = progressData.value.reduce((total, item) => total + (item.count || 0), 0)
      await nextTick()
      renderProgressChart()
    }
  } catch (error) {
    console.error('加载项目进度统计失败:', error)
    ElMessage.error('加载项目进度统计失败')
  }
}

// 加载行业统计
const loadBusinessStatistics = async () => {
  try {
    const params = {
      projectType: filterForm.projectType || 0,
      town: filterForm.town || ''
    }
    
    const response = await getProjectRelatedBusinessStatistics(params)
    if (response.code === 200 && response.data) {
      businessData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderBusinessChart()
    }
  } catch (error) {
    console.error('加载行业统计失败:', error)
    ElMessage.error('加载行业统计失败')
  }
}

// 加载规模统计
const loadScaleStatistics = async () => {
  try {
    const params = {
      projectType: filterForm.projectType || 0,
      town: filterForm.town || ''
    }
    
    const response = await getProjectScaleStatistics(params)
    if (response.code === 200 && response.data) {
      scaleData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderScaleChart()
    }
  } catch (error) {
    console.error('加载规模统计失败:', error)
    ElMessage.error('加载规模统计失败')
  }
}

// 加载在建项目统计
const loadConstructionStatistics = async () => {
  try {
    const params = {
      projectType: filterForm.projectType || 0,
      town: filterForm.town || ''
    }
    
    const response = await getProjectConstructionStatistics(params)
    if (response.code === 200 && response.data) {
      constructionData.value = Array.isArray(response.data) ? response.data : []
      await nextTick()
      renderConstructionChart()
    }
  } catch (error) {
    console.error('加载在建项目统计失败:', error)
    ElMessage.error('加载在建项目统计失败')
  }
}

// 渲染进度统计图表
const renderProgressChart = () => {
  if (!progressChart) {
    progressChart = echarts.init(progressChartRef.value)
  }

  const data = progressData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getProgressColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '项目数量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  progressChart.setOption(option)
}

// 渲染行业统计图表
const renderBusinessChart = () => {
  if (!businessChart) {
    businessChart = echarts.init(businessChartRef.value)
  }

  const data = businessData.value.map(item => ({
    name: item.name,
    value: item.count || 0,
    itemStyle: {
      color: getBusinessColor(item.code)
    }
  }))

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '项目数量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  }

  businessChart.setOption(option)
}

// 渲染规模统计图表
const renderScaleChart = () => {
  if (!scaleChart) {
    scaleChart = echarts.init(scaleChartRef.value)
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: scaleData.value.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '投资额（万元）'
    },
    series: [
      {
        data: scaleData.value.map(item => ({
          value: item.value || 0,
          itemStyle: {
            color: '#409eff'
          }
        })),
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  }

  scaleChart.setOption(option)
}

// 渲染在建项目统计图表
const renderConstructionChart = () => {
  if (!constructionChart) {
    constructionChart = echarts.init(constructionChartRef.value)
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: constructionData.value.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '项目数量（个）'
    },
    series: [
      {
        data: constructionData.value.map(item => ({
          value: item.count || 0,
          itemStyle: {
            color: '#67c23a'
          }
        })),
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  }

  constructionChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  if (progressChartRef.value) {
    progressChart = echarts.init(progressChartRef.value)
  }
  
  if (businessChartRef.value) {
    businessChart = echarts.init(businessChartRef.value)
  }
  
  if (scaleChartRef.value) {
    scaleChart = echarts.init(scaleChartRef.value)
  }
  
  if (constructionChartRef.value) {
    constructionChart = echarts.init(constructionChartRef.value)
  }

  // 监听窗口大小变化
  const resizeHandler = () => {
    progressChart?.resize()
    businessChart?.resize()
    scaleChart?.resize()
    constructionChart?.resize()
  }
  
  window.addEventListener('resize', resizeHandler)
}

// 处理筛选
const handleFilter = async () => {
  await loadAllData()
}

// 处理重置
const handleReset = async () => {
  filterForm.town = '' // 默认沙窝镇
  filterForm.projectType = ''
  await loadAllData()
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadProgressStatistics(),
      loadBusinessStatistics(),
      loadScaleStatistics(),
      loadConstructionStatistics()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 初始化区域选项
  initAreaOptions()

  // 初始化图表
  await initCharts()

  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (progressChart) {
    progressChart.dispose()
    progressChart = null
  }
  if (businessChart) {
    businessChart.dispose()
    businessChart = null
  }
  if (scaleChart) {
    scaleChart.dispose()
    scaleChart = null
  }
  if (constructionChart) {
    constructionChart.dispose()
    constructionChart = null
  }
  
  window.removeEventListener('resize', () => {
    progressChart?.resize()
    businessChart?.resize()
    scaleChart?.resize()
    constructionChart?.resize()
  })
})
</script>

<style scoped>
.statistics-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height:calc(100vh - 180px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 20px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}

.form-item {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 160px;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 内容区域 */
.content-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-count, .unit-label {
  font-size: 14px;
  color: #909399;
}

.chart-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.chart-container {
  height: 350px;
  flex: 1;
  min-width: 300px;
}

.chart-container.full-width {
  width: 100%;
  min-width: auto;
}

.chart-legend-right {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 14px;
  color: #606266;
  min-width: 60px;
}

.legend-count {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-section {
    flex-direction: column;
  }

  .chart-content {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-container {
    height: 300px;
    min-width: auto;
  }

  .chart-legend-right {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .statistics-analysis-container {
    padding: 15px;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item {
    justify-content: space-between;
  }

  .form-input {
    width: 200px;
  }

  .chart-card {
    padding: 20px;
  }

  .chart-container {
    height: 280px;
  }
}

/* 表格优化 */
:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 32px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 