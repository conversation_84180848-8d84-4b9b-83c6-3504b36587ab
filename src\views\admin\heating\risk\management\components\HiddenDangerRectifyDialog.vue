<template>
  <el-dialog
    v-model="dialogVisible"
    title="隐患整改"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="rectify-dialog"
  >
    <div class="rectify-content">
      <div class="table-header">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增</el-button>
      </div>

      <!-- 整改列表 -->
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle">
        <el-table-column prop="processStatusName" label="整改状态" min-width="100" />
        <el-table-column prop="handleUserName" label="整改责任人" min-width="120" />
        <el-table-column prop="dealTime" label="整改时间" min-width="160" />
        <el-table-column prop="description" label="整改描述" min-width="200" />
        <el-table-column label="操作" fixed="right" min-width="150">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click="handleView(row)">查看</el-button>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑整改方案弹窗 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="formDialogTitle"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        :disabled="formMode === 'view'"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="整改状态" prop="handleStatus" required>
              <el-select v-model="formData.handleStatus" placeholder="请选择" style="width: 100%" @change="handleStatusChange">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改责任人" prop="handleUserName" required>
              <el-input v-model="formData.handleUserName" placeholder="请输入整改责任人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改时间" prop="dealTime" required>
              <el-date-picker
                v-model="formData.dealTime"
                type="datetime"
                placeholder="请选择整改时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改描述" prop="description" required>
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请输入整改描述"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改图片" prop="picUrls">
              <el-upload
                class="upload-demo"
                :auto-upload="false"
                :on-change="handleFileChange"
                :file-list="fileList"
                list-type="picture-card"
                :limit="9"
                :disabled="formMode === 'view'"
                multiple
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传jpg、jpeg、png、gif、bmp、JPG、JPEG、GIF、svg、SVG
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" v-if="formMode !== 'view'">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getHeatingHiddenDangerHandleList,
  addHeatingHiddenDangerHandle,
  deleteHeatingHiddenDangerHandle
} from '@/api/heating';
import { uploadFile } from '@/api/upload';
import { HEATING_RECTIFY_STATUS_OPTIONS } from '@/constants/heating';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dangerId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible']);

// 表单引用
const formRef = ref(null);
const fileList = ref([]);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表格数据
const tableData = ref([]);

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表单弹窗相关
const formDialogVisible = ref(false);
const formMode = ref('add'); // 'add', 'edit', 'view'
const statusOptions = ref(HEATING_RECTIFY_STATUS_OPTIONS);

// 表单弹窗标题
const formDialogTitle = computed(() => {
  const titles = {
    add: '新增整改方案',
    edit: '编辑整改方案',
    view: '整改方案详情'
  };
  return titles[formMode.value] || '整改方案';
});

// 表单数据
const formData = reactive({
  id: '',
  dangerId: '',
  dealTime: '',
  description: '',
  handleStatus: '',
  handleStatusName: '',
  handleUserName: '',
  picUrls: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  handleStatus: [{ required: true, message: '请选择整改状态', trigger: 'change' }],
  handleUserName: [{ required: true, message: '请输入整改责任人', trigger: 'blur' }],
  dealTime: [{ required: true, message: '请选择整改时间', trigger: 'change' }],
  description: [{ required: true, message: '请输入整改描述', trigger: 'blur' }]
};

// 监听props变化
watch(() => props.dangerId, (newVal) => {
  if (newVal && props.visible) {
    fetchHandleList();
  }
}, { immediate: true });

watch(() => props.visible, (newVal) => {
  if (newVal && props.dangerId) {
    fetchHandleList();
  }
});

// 获取整改列表
const fetchHandleList = async () => {
  if (!props.dangerId) return;
  
  try {
    const res = await getHeatingHiddenDangerHandleList(props.dangerId);
    if (res && res.code === 200) {
      tableData.value = res.data || [];
    }
  } catch (error) {
    console.error('获取整改列表失败:', error);
    ElMessage.error('获取整改列表失败');
  }
};

// 处理整改状态变化
const handleStatusChange = (value) => {
  const selected = statusOptions.value.find(item => item.value === value);
  if (selected) {
    formData.handleStatusName = selected.label;
  }
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'dangerId') {
      formData[key] = props.dangerId;
    } else {
      formData[key] = '';
    }
  });
  fileList.value = [];
};

// 处理新增
const handleAdd = () => {
  formMode.value = 'add';
  resetForm();
  formData.dangerId = props.dangerId;
  formDialogVisible.value = true;
};

// 处理查看
const handleView = (row) => {
  formMode.value = 'view';
  Object.keys(formData).forEach(key => {
    if (row[key] !== undefined) {
      formData[key] = row[key];
    }
  });
  // 处理图片显示
  if (row.picUrls) {
    fileList.value = row.picUrls.split(',').map((url, index) => ({
      name: `image_${index}`,
      url: url,
      uid: Date.now() + index
    }));
  }
  formDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row) => {
  formMode.value = 'edit';
  Object.keys(formData).forEach(key => {
    if (row[key] !== undefined) {
      formData[key] = row[key];
    }
  });
  // 处理图片显示
  if (row.picUrls) {
    fileList.value = row.picUrls.split(',').map((url, index) => ({
      name: `image_${index}`,
      url: url,
      uid: Date.now() + index
    }));
  }
  formDialogVisible.value = true;
};

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('此操作将永久删除该整改记录, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await deleteHeatingHiddenDangerHandle(row.id);
    if (res && res.code === 200) {
      ElMessage.success('删除成功');
      fetchHandleList();
    } else {
      ElMessage.error(res?.msg || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!');
    return;
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.picUrls ? formData.picUrls.split(',') : [];
      urls.push(response.data.url);
      formData.picUrls = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    const res = await addHeatingHiddenDangerHandle(submitData);

    if (res && res.code === 200) {
      ElMessage.success(formMode.value === 'add' ? '新增成功' : '更新成功');
      formDialogVisible.value = false;
      fetchHandleList();
    } else {
      ElMessage.error(res?.msg || (formMode.value === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  tableData.value = [];
};
</script>

<style scoped>
.rectify-dialog {
  font-family: PingFangSC, PingFang SC;
}

.rectify-content {
  min-height: 400px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 