<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="project-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目编号" prop="projectCode">
            <el-input v-model="formData.projectCode" placeholder="请输入项目编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="formData.projectName" placeholder="请输入项目名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目类型" prop="projectType">
            <el-select v-model="formData.projectType" placeholder="请选择" class="w-full" @change="handleProjectTypeChange">
              <el-option v-for="item in projectTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主管单位" prop="managementUnitName">
            <el-input v-model="formData.managementUnitName" placeholder="请输入主管单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划年度" prop="projectYear">
            <el-date-picker
              v-model="formData.projectYear"
              type="year"
              placeholder="请选择计划年度"
              class="w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划开工时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="date"
              placeholder="请选择计划开工时间"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="项目内容" prop="projectContent">
            <el-input v-model="formData.projectContent" type="textarea" :rows="3" placeholder="请输入项目内容" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据来源" prop="source">
            <el-select v-model="formData.source" placeholder="请选择" class="w-full">
              <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目建设周期" prop="projectPeriod">
            <el-input v-model="formData.projectPeriod" placeholder="请输入项目建设周期（月）" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="项目总投资" prop="projectInvest">
            <div class="flex items-center">
              <el-input-number v-model="formData.projectInvest" :min="0" :precision="2" class="w-full-unit" />
              <span class="unit-label">万元</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程进展状态" prop="projectStatus">
            <el-select v-model="formData.projectStatus" placeholder="请选择" class="w-full" @change="handleProjectStatusChange">
              <el-option v-for="item in projectStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联行业" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否重点项目" prop="isMain">
            <el-radio-group v-model="formData.isMain">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  saveProject,
  updateProject
} from '@/api/comprehensive'
import {
  PROJECT_TYPE_OPTIONS,
  PROJECT_STATUS_OPTIONS,
  RELATED_BUSINESS_OPTIONS,
  PROJECT_SOURCE_OPTIONS
} from '@/constants/comprehensive'
import { AREA_OPTIONS } from '@/constants/gas'
import { collectShow } from "@/hooks/gishooks"
import bus from '@/utils/mitt'

// 使用从常量文件导入的选项
const projectTypeOptions = PROJECT_TYPE_OPTIONS
const projectStatusOptions = PROJECT_STATUS_OPTIONS
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS
const sourceOptions = PROJECT_SOURCE_OPTIONS

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增项目',
    edit: '编辑项目',
    view: '项目详情'
  }
  return titles[props.mode] || '项目信息'
})

// 下拉选项数据
const areaOptions = ref(AREA_OPTIONS)

// 表单数据
const formData = reactive({
  id: '',
  projectCode: '',
  projectName: '',
  projectType: '',
  projectTypeName: '',
  managementUnitName: '',
  projectYear: '',
  startTime: '',
  projectContent: '',
  source: '',
  projectPeriod: '',
  projectInvest: 0,
  projectStatus: '',
  projectStatusName: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  isMain: false,
  address: '',
  longitude: '',
  latitude: '',
  remark: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
})

// 表单验证规则
const formRules = {
  projectCode: [{ required: true, message: '请输入项目编号', trigger: 'blur' }],
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  projectType: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
  managementUnitName: [{ required: true, message: '请输入主管单位', trigger: 'blur' }],
  projectYear: [{ required: true, message: '请选择计划年度', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择计划开工时间', trigger: 'change' }],
  projectContent: [{ required: true, message: '请输入项目内容', trigger: 'blur' }],
  source: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
  projectStatus: [{ required: true, message: '请选择工程进展状态', trigger: 'change' }],
  relatedBusiness: [{ required: true, message: '请选择关联行业', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'projectInvest') {
      formData[key] = 0
    } else if (key === 'isMain') {
      formData[key] = false
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理项目类型变化
const handleProjectTypeChange = (value) => {
  const selected = projectTypeOptions.find(item => item.value === value)
  if (selected) {
    formData.projectTypeName = selected.label
  }
}

// 处理项目状态变化
const handleProjectStatusChange = (value) => {
  const selected = projectStatusOptions.find(item => item.value === value)
  if (selected) {
    formData.projectStatusName = selected.label
  }
}

// 处理关联行业变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.find(item => item.value === value)
  if (selected) {
    formData.relatedBusinessName = selected.label
  }
}

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1]
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town)
    if (selectedArea) {
      formData.townName = selectedArea.name
    }
  }
}

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code)
      if (found) return found
    }
  }
  return null
}

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true
  bus.off("getCollectLocation", handleCollectLocation)
  bus.on("getCollectLocation", handleCollectLocation)
}

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude
    formData.latitude = params.latitude
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveProject(submitData)
    } else if (props.mode === 'edit') {
      res = await updateProject(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation)
})

// 组件挂载时获取数据
onMounted(() => {
  resetForm()
})
</script>

<style scoped>
.project-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full-unit {
  width: calc(100% - 40px) !important;
}

.unit-label {
  display: inline-block;
  white-space: nowrap;
  width: 35px;
  margin-left: 5px;
}
</style> 