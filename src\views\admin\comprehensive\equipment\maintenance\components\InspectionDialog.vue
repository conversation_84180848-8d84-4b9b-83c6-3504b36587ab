<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="inspection-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="formData.taskName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属专项" prop="relatedBusiness">
            <el-select v-model="formData.relatedBusiness" placeholder="请选择" class="w-full" @change="handleRelatedBusinessChange">
              <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="任务周期" prop="startTime">
            <div class="flex items-center">
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="mr-2"
                style="width: 200px;"
              />
              <span class="mx-2">至</span>
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 200px;"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="巡检频次" prop="frequency">
            <el-select v-model="formData.frequency" placeholder="请选择" class="w-full" @change="handleFrequencyChange">
              <el-option v-for="item in frequencyOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务状态" prop="taskStatus">
            <el-select v-model="formData.taskStatus" placeholder="请选择" class="w-full" @change="handleTaskStatusChange">
              <el-option v-for="item in taskStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="巡检人员" prop="taskUserId">
            <el-select v-model="formData.taskUserId" placeholder="请选择" class="w-full" @change="handleUserChange">
              <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="巡检内容" prop="taskContent">
            <el-input v-model="formData.taskContent" type="textarea" :rows="3" placeholder="请输入巡检内容" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="设备巡查" prop="deviceList">
            <el-select 
              v-model="selectedDeviceIds" 
              multiple 
              placeholder="请选择设备" 
              class="w-full"
              @change="handleDeviceChange"
            >
              <el-option 
                v-for="item in deviceOptions" 
                :key="item.id" 
                :label="item.deviceName" 
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 设备列表 -->
      <el-row :gutter="20" v-if="formData.deviceList && formData.deviceList.length > 0">
        <el-col :span="24">
          <el-form-item label="设备列表">
            <div class="device-list-container">
              <el-table :data="formData.deviceList" style="width: 100%" border>
                <el-table-column prop="deviceName" label="设备名称" min-width="120" />
                <el-table-column prop="deviceTypeName" label="设备类型" min-width="100" />
                <el-table-column prop="address" label="位置" min-width="150" />
                <el-table-column label="操作" fixed="right" min-width="100">
                  <template #default="{ row, $index }">
                    <el-button type="danger" link @click="removeDevice($index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  saveInspectionTask,
  updateInspectionTask,
  getInspectionDeviceList
} from '@/api/comprehensive';
import { getAllUsers } from '@/api/system';
import { 
  INSPECTION_FREQUENCY_OPTIONS, 
  INSPECTION_TASK_STATUS_OPTIONS,
  RELATED_BUSINESS_OPTIONS 
} from '@/constants/comprehensive';

// 使用从常量文件导入的选项
const frequencyOptions = INSPECTION_FREQUENCY_OPTIONS;
const taskStatusOptions = INSPECTION_TASK_STATUS_OPTIONS;
const relatedBusinessOptions = RELATED_BUSINESS_OPTIONS;

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增巡检任务',
    edit: '编辑巡检任务',
    view: '巡检任务详情'
  };
  return titles[props.mode] || '巡检任务';
});

// 下拉选项数据
const userOptions = ref([]);
const deviceOptions = ref([]);
const selectedDeviceIds = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  taskName: '',
  taskCode: '',
  taskContent: '',
  relatedBusiness: '',
  relatedBusinessName: '',
  frequency: '',
  frequencyName: '',
  taskUserId: '',
  taskUserName: '',
  taskStatus: '',
  taskStatusName: '',
  startTime: '',
  endTime: '',
  remark: '',
  deviceList: []
});

// 表单验证规则
const formRules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  relatedBusiness: [{ required: true, message: '请选择所属专项', trigger: 'change' }],
  frequency: [{ required: true, message: '请选择巡检频次', trigger: 'change' }],
  taskUserId: [{ required: true, message: '请选择巡检人员', trigger: 'change' }],
  taskStatus: [{ required: true, message: '请选择任务状态', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  taskContent: [{ required: true, message: '请输入巡检内容', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'deviceList') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });
  selectedDeviceIds.value = [];
};

// 更新各字段的名称，基于选中的值
const updateNamesByValues = () => {
  // 所属专项
  const selectedBusiness = relatedBusinessOptions.find(item => item.value === formData.relatedBusiness);
  if (selectedBusiness) {
    formData.relatedBusinessName = selectedBusiness.label;
  }

  // 巡检频次
  const selectedFrequency = frequencyOptions.find(item => item.value === formData.frequency);
  if (selectedFrequency) {
    formData.frequencyName = selectedFrequency.label;
  }

  // 巡检人员
  const selectedUser = userOptions.value.find(item => item.id === formData.taskUserId);
  if (selectedUser) {
    formData.taskUserName = selectedUser.name;
  }

  // 任务状态
  const selectedStatus = taskStatusOptions.find(item => item.value === formData.taskStatus);
  if (selectedStatus) {
    formData.taskStatusName = selectedStatus.label;
  }
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 设置选中的设备ID
    if (newVal.deviceList && newVal.deviceList.length > 0) {
      selectedDeviceIds.value = newVal.deviceList.map(device => device.id);
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理所属专项变化
const handleRelatedBusinessChange = (value) => {
  const selected = relatedBusinessOptions.find(item => item.value === value);
  if (selected) {
    formData.relatedBusinessName = selected.label;
  }
};

// 处理巡检频次变化
const handleFrequencyChange = (value) => {
  const selected = frequencyOptions.find(item => item.value === value);
  if (selected) {
    formData.frequencyName = selected.label;
  }
};

// 处理巡检人员变化
const handleUserChange = (value) => {
  const selected = userOptions.value.find(item => item.id === value);
  if (selected) {
    formData.taskUserName = selected.name;
  }
};

// 处理任务状态变化
const handleTaskStatusChange = (value) => {
  const selected = taskStatusOptions.find(item => item.value === value);
  if (selected) {
    formData.taskStatusName = selected.label;
  }
};

// 处理设备选择变化
const handleDeviceChange = (selectedIds) => {
  const selectedDevices = deviceOptions.value.filter(device => selectedIds.includes(device.id));
  formData.deviceList = selectedDevices.map(device => ({
    id: device.id,
    deviceId: device.deviceId,
    deviceName: device.deviceName,
    deviceTypeName: device.deviceTypeName,
    address: device.address,
    latitude: device.latitude,
    longitude: device.longitude,
    remark: device.remark,
    taskId: formData.id || ''
  }));
};

// 删除设备
const removeDevice = (index) => {
  formData.deviceList.splice(index, 1);
  // 更新选中的设备ID
  selectedDeviceIds.value = formData.deviceList.map(device => device.id);
};

// 获取用户列表
const fetchUsers = async () => {
  try {
    const res = await getAllUsers();
    if (res && res.data) {
      userOptions.value = res.data.map(item => ({
        id: item.id,
        name: item.name || item.username
      }));
    }
  } catch (error) {
    console.error('获取用户列表失败', error);
  }
};

// 获取设备列表
const fetchDevices = async () => {
  try {
    const res = await getInspectionDeviceList();
    if (res && res.data) {
      deviceOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取设备列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    updateNamesByValues();

    const submitData = { ...formData };

    let res;
    if (props.mode === 'add') {
      res = await saveInspectionTask(submitData);
    } else if (props.mode === 'edit') {
      res = await updateInspectionTask(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers();
  fetchDevices();
});
</script>

<style scoped>
.inspection-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.device-list-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}
</style> 