<template>
  <div class="emergency-supplies-container">
    <!-- 搜索区域 -->
    <div class="emergency-supplies-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属仓库:</span>
          <el-select v-model="formData.storeId" class="form-input" placeholder="请选择所属仓库">
            <el-option label="全部" value="" />
            <el-option v-for="item in storeOptions" :key="item.id" :label="item.storeName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.keyWord" class="form-input" placeholder="输入关键词" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>
    
    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="suppliesName" label="物资名称" min-width="160" />
        <el-table-column prop="suppliesModel" label="规格型号" min-width="120" />
        <el-table-column prop="suppliesTypeName" label="物资类型" min-width="120" />
        <el-table-column prop="suppliesNumber" label="数量" min-width="80" />
        <el-table-column prop="suppliesUnit" label="单位" min-width="80" />
        <el-table-column prop="storeName" label="所属仓库" min-width="120" />
        <el-table-column prop="gbSuppliesName" label="国标物资名称" min-width="140" />
        <el-table-column prop="gbSuppliesCode" label="国标物资代码" min-width="140" />
        <el-table-column prop="gbSubLevelTypeName" label="国标二级分类" min-width="140" />
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button type="primary" link @click.stop="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <EmergencySuppliesDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage } from 'element-plus';
import { 
  getEmergencySuppliesPage, 
  deleteEmergencySupplies, 
  getEmergencySuppliesDetail,
  getEmergencyStoreList
} from '@/api/comprehensive';
import { EMERGENCY_SUPPLIES_TYPE_OPTIONS } from '@/constants/comprehensive';
import { misPosition } from '@/hooks/gishooks';
import EmergencySuppliesDialog from './EmergencySuppliesDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const suppliesTypeOptions = ref(EMERGENCY_SUPPLIES_TYPE_OPTIONS);
const storeOptions = ref([]);

// 表单数据
const formData = ref({
  storeId: '',
  keyWord: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('add'); // 'add' | 'edit' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchSuppliesData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    storeId: '',
    keyWord: ''
  };
  currentPage.value = 1;
  fetchSuppliesData();
};

// 获取应急物资分页数据
const fetchSuppliesData = async () => {
  try {
    const params = {
      storeId: formData.value.storeId,
      keyWord: formData.value.keyWord
    };
    
    const res = await getEmergencySuppliesPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取应急物资数据失败:', error);
    ElMessage.error('获取应急物资数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取仓库列表
const fetchStoreList = async () => {
  try {
    const res = await getEmergencyStoreList();
    if (res && res.data) {
      storeOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取仓库列表失败:', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchSuppliesData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchSuppliesData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add';
  dialogData.value = {};
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = async (row) => {
  try {
    const res = await getEmergencySuppliesDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'edit';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取应急物资详情失败');
    }
  } catch (error) {
    console.error('获取应急物资详情失败:', error);
    ElMessage.error('获取应急物资详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getEmergencySuppliesDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取应急物资详情失败');
    }
  } catch (error) {
    console.error('获取应急物资详情失败:', error);
    ElMessage.error('获取应急物资详情失败');
  }
};

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该应急物资信息吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteEmergencySupplies(row.id);
      if (res && res.code === 200) {
        ElMessage.success('删除成功');
        fetchSuppliesData();
      } else {
        ElMessage.error(res?.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除应急物资失败:', error);
      ElMessage.error('删除应急物资失败');
    }
  }).catch(() => {
    // 取消删除
  });
};

// 处理定位
const handleLocation = (row) => {
  if (
    row.latitude &&
    row.latitude != '' &&
    row.longitude &&
    row.longitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchSuppliesData();
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStoreList(),
      fetchSuppliesData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.emergency-supplies-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.emergency-supplies-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style> 