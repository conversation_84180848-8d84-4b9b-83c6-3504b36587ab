# 桥梁布点方案管理功能

## 功能概述
桥梁布点方案管理提供了完整的增删改查功能，包括：
- 左右布局：左侧桥梁列表，右侧方案列表
- 方案的新增、编辑、删除、查看详情
- 文件上传和下载功能
- 搜索和分页功能

## 主要文件
- `point.vue` - 主页面组件
- `components/PointSchemeDialog.vue` - 弹窗组件

## 实现的功能

### 1. 左侧桥梁列表
- 显示所有桥梁信息
- 支持桥梁名称搜索（前端过滤）
- 默认选中第一个桥梁
- 点击切换桥梁，右侧显示对应方案

### 2. 右侧方案列表
- 分页显示选中桥梁的方案列表
- 支持方案名称搜索
- 表格显示：序号、方案名称、附件、更新时间、备注、操作

### 3. 新增/编辑功能
- 表单字段：方案名称、所属桥梁、所属部件、文件上传、备注
- 表单验证：必填项检查
- 文件上传：支持.doc/.docx/.pdf/.dwg格式，最多5个文件，单文件最大30MB
- 文件管理：上传后以表格形式显示，支持删除和下载

### 4. 删除功能
- 确认提示
- 删除成功后刷新列表

### 5. 详情功能
- 只读模式显示所有信息
- 文件列表只能查看和下载

### 6. 下载功能
- 单个文件下载：点击文件名或详情中的下载按钮
- 批量下载：点击操作列的下载按钮

## API接口

已在 `src/api/bridge.js` 中添加以下接口：

```javascript
// 桥梁布点方案管理 API
getPointSchemePage(pageNum, pageSize, params)  // 分页查询
getPointSchemeDetail(id)                       // 详情查询
savePointScheme(data)                          // 新增
updatePointScheme(data)                        // 更新
deletePointScheme(id)                          // 删除

// 常量
COMPONENT_TYPE_OPTIONS                         // 所属部件选项
```

## 数据字段

```javascript
{
  "id": "",           // 主键
  "schemeName": "",   // 方案名称
  "bridgeId": "",     // 桥梁ID
  "bridgeName": "",   // 桥梁名称
  "componentType": "", // 所属部件类型
  "fileUrl": "",      // 文件URL（JSON字符串存储多个文件）
  "remark": "",       // 备注
  "updateTime": ""    // 更新时间
}
```

## 样式特点
- 响应式布局：适配不同屏幕尺寸
- 与现有系统风格保持一致
- 左右布局在移动端自动切换为上下布局
- 表格行交替显示不同背景色

## 错误处理
- API调用异常处理
- 文件上传失败处理
- 表单验证错误提示
- 用户友好的错误消息

## 用户体验优化
- 加载状态提示
- 操作成功/失败消息
- 确认对话框
- 文件上传进度显示
- 批量下载防浏览器阻止（间隔200ms） 