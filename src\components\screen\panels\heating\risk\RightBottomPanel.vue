<template>
  <PanelBox title="隐患整改分析">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="timeRange" :options="timeOptions" @change="handleTimeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="risk-analysis-table">
        <div class="risk-header">
          <div class="header-item">隐患等级</div>
          <div class="header-item">隐患总数</div>
          <div class="header-item">已整改</div>
        </div>
        <div class="risk-row" v-for="(item, index) in riskData" :key="index">
          <div class="cell-level">
            <div class="indicator-dot-container" :style="{ borderColor: item.color + '40' }">
              <div class="indicator-dot" :style="{ backgroundColor: item.color }"></div>
            </div>
            <span>{{ item.level }}</span>
          </div>
          <div class="cell-count">{{ item.count }}</div>
          <div class="cell-progress">
            <div class="progress-bar-bg">
              <div class="progress-bar-fill" :style="{ width: item.percentage + '%' }"></div>
            </div>
            <span class="percentage-text">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getHiddenDangerRectifyAnalysis } from '@/api/heating'

// 时间选择
const timeRange = ref('week')
const timeOptions = [
  { label: '近一周', value: 'week' },
  { label: '近一月', value: 'month' },
  { label: '近一年', value: 'year' }
]

// 时间范围映射到 dayIndex
const getDayIndex = (timeRange) => {
  switch (timeRange) {
    case 'week':
      return 7
    case 'month':
      return 30
    case 'year':
      return 365
    default:
      return 7
  }
}

// 预定义隐患等级配置（确保始终显示完整结构）
const dangerLevelConfig = [
  {
    level: '重大隐患',
    dangerLevel: 7002701,
    color: '#FF0000'
  },
  {
    level: '较大隐患',
    dangerLevel: 7002702,
    color: '#FF9900'
  },
  {
    level: '一般隐患',
    dangerLevel: 7002703,
    color: '#FFD200'
  }
]

const handleTimeChange = () => {
  console.log('时间范围变更为:', timeRange.value)
  fetchRectifyAnalysisData(timeRange.value)
}

// 初始化风险数据（基于预定义配置，确保始终显示完整结构）
const riskData = ref(dangerLevelConfig.map(config => ({
  level: config.level,
  count: 0,
  percentage: 0,
  color: config.color
})))

// 从接口获取隐患整改分析数据
const fetchRectifyAnalysisData = async (timeRange) => {
  try {
    const dayIndex = getDayIndex(timeRange)
    const response = await getHiddenDangerRectifyAnalysis(dayIndex)

    if (response.code === 200 && response.data && Array.isArray(response.data)) {
      // 重置数据为初始状态
      riskData.value = dangerLevelConfig.map(config => ({
        level: config.level,
        count: 0,
        percentage: 0,
        color: config.color
      }))

      // 根据接口返回数据更新对应的风险等级
      response.data.forEach(item => {
        const configIndex = dangerLevelConfig.findIndex(config => config.dangerLevel === item.dangerLevel)
        if (configIndex !== -1) {
          riskData.value[configIndex] = {
            level: dangerLevelConfig[configIndex].level,
            count: item.totalCount || 0,
            percentage: Math.round(item.rectifiedRate || 0),
            color: dangerLevelConfig[configIndex].color
          }
        }
      })
    } else {
      // 接口返回异常时，重置为初始状态
      riskData.value = dangerLevelConfig.map(config => ({
        level: config.level,
        count: 0,
        percentage: 0,
        color: config.color
      }))
    }
  } catch (error) {
    console.error('获取隐患整改分析数据失败:', error)
    // 发生错误时，重置为初始状态
    riskData.value = dangerLevelConfig.map(config => ({
      level: config.level,
      count: 0,
      percentage: 0,
      color: config.color
    }))
  }
}

onMounted(() => {
  fetchRectifyAnalysisData(timeRange.value) // 初始化数据
})

</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.com-select {
  margin-right: 20px;
}

.risk-analysis-table {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* 行间距 */
}

.risk-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: rgba(0, 163, 255, 0.15);
  border-radius: 4px 4px 0px 0px;
}

.header-item {
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  flex: 1;
  text-align: center;
}

.header-item:first-child {
  text-align: left;
}

.header-item:last-child {
  text-align: right;
}

.risk-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 15px;
  /* 调整内边距以适应内容 */
  background: rgba(0, 163, 255, 0.15);
  border-radius: 4px 4px 0px 0px;
}

.cell-level {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: PingFangSC, 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
}

.indicator-dot-container {
  width: 13px;
  height: 13px;
  border: 1px solid transparent;
  /* 透明以便动态设置颜色 */
  /* border-radius: 50%; */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  /* border-radius: 50%; */
  flex-shrink: 0;
}

.cell-count {
  flex: 1;
  font-family: D-DIN, 'D-DIN';
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
}

.cell-progress {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  /* 确保进度条和百分比靠右 */
  gap: 10px;
}

.progress-bar-bg {
  width: 117px;
  height: 8px;
  background: #0B3363;
  /* border-radius: 4px; 给背景也加个圆角 */
  overflow: hidden;
  /* 确保内部条的圆角可见 */
}

.progress-bar-fill {
  height: 8px;
  background: linear-gradient(270deg, #00D3FF 0%, #00A8FF 100%);
  /* border-radius: 1px; 进度条圆角 */
}

.percentage-text {
  font-family: D-DIN, 'D-DIN';
  font-weight: bold;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 15px;
}

/* 响应式布局适配 */
@media screen and (min-height: 910px) and (max-height: 940px) {
  .panel-content {
    padding: 10px;
    gap: 8px;
  }

  .risk-header {
    padding: 6px 10px;
  }

  .header-item,
  .cell-level span,
  .percentage-text {
    font-size: 13px;
  }

  .cell-count {
    font-size: 22px;
  }

  .risk-row {
    padding: 4px 10px;
    gap: 8px;
  }

  .indicator-dot-container {
    width: 12px;
    height: 12px;
  }

  .indicator-dot {
    width: 5px;
    height: 5px;
  }

  .progress-bar-bg {
    width: 100px;
    /* 适当缩小 */
  }
}
</style>