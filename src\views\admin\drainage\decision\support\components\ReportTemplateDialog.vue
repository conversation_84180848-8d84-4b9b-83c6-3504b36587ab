<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="report-template-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="模板名称" prop="templateName">
            <el-input v-model="formData.templateName" placeholder="请输入模板名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板类型" prop="templateType">
            <el-select v-model="formData.templateType" placeholder="请选择模板类型" class="w-full" @change="handleTemplateTypeChange">
              <el-option v-for="item in templateTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="模板文件" prop="reportTemplateUrl">
            <div class="file-upload-container">
              <!-- 文件上传按钮 -->
              <el-upload
                class="file-upload"
                :auto-upload="false"
                :on-change="handleFileChange"
                :file-list="fileList"
                :limit="1"
                :disabled="mode === 'view'"
                accept=".doc,.docx,.pdf,.xls,.xlsx"
              >
                <el-button type="primary" :disabled="mode === 'view'">
                  <el-icon><Upload /></el-icon>
                  选择文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 doc、docx、pdf、xls、xlsx 格式，文件大小不超过 20MB
                  </div>
                </template>
              </el-upload>
              
              <!-- 文件列表显示 -->
              <div v-if="fileList.length > 0" class="file-list">
                <div v-for="(file, index) in fileList" :key="index" class="file-item">
                  <el-icon class="file-icon"><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <el-button 
                    v-if="mode !== 'view'" 
                    type="danger" 
                    link 
                    @click="removeFile(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Document, Delete } from '@element-plus/icons-vue'
import { uploadFile } from '@/api/upload'
import { saveAssessReportTemplate, updateAssessReportTemplate } from '@/api/drainage'
import { REPORT_TYPE_MAP } from '@/constants/drainage'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增模板',
    edit: '编辑模板',
    view: '模板详情'
  }
  return titles[props.mode] || '模板信息'
})

// 模板类型选项
const templateTypeOptions = ref(
  Object.entries(REPORT_TYPE_MAP).map(([value, label]) => ({
    value: parseInt(value),
    label
  }))
)

// 文件列表
const fileList = ref([])

// 表单数据
const formData = reactive({
  id: '',
  templateName: '',
  templateType: '',
  templateTypeName: '',
  reportTemplateUrl: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  reportTemplateUrl: [{ required: true, message: '请上传模板文件', trigger: 'change' }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  fileList.value = []
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    
    // 处理文件显示
    if (newVal.reportTemplateUrl) {
      const urls = newVal.reportTemplateUrl.split(',').filter(url => url.trim())
      fileList.value = urls.map((url, index) => ({
        name: `template_file_${index + 1}${getFileExtension(url)}`,
        url: url,
        uid: Date.now() + index,
        status: 'success'
      }))
    }
  } else if (props.mode === 'add') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const fileName = url.split('/').pop() || ''
  const dotIndex = fileName.lastIndexOf('.')
  return dotIndex !== -1 ? fileName.substring(dotIndex) : ''
}

// 处理模板类型变化
const handleTemplateTypeChange = (value) => {
  const selected = templateTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.templateTypeName = selected.label
  }
}

// 文件上传处理
const handleFileChange = async (file, fileListParam) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传文件大小不能超过 20MB!')
    return
  }

  // 检查文件类型
  const allowedTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
  const isValidType = allowedTypes.includes(file.raw.type) || 
                     /\.(doc|docx|pdf|xls|xlsx)$/i.test(file.name)
  
  if (!isValidType) {
    ElMessage.error('只能上传 doc、docx、pdf、xls、xlsx 格式的文件!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      // 更新文件列表
      fileList.value = [{
        name: file.name,
        url: response.data.url,
        uid: file.uid,
        status: 'success'
      }]
      
      // 更新formData中的reportTemplateUrl
      updateFileUrl()
      
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1)
  updateFileUrl()
}

// 更新文件URL
const updateFileUrl = () => {
  nextTick(() => {
    const urls = fileList.value
      .filter(file => file.status === 'success' && file.url)
      .map(file => file.url)
    formData.reportTemplateUrl = urls.join(',')
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 更新模板类型名称
    const selectedType = templateTypeOptions.value.find(item => item.value === formData.templateType)
    if (selectedType) {
      formData.templateTypeName = selectedType.label
    }

    const submitData = { ...formData }

    let res
    if (props.mode === 'add') {
      res = await saveAssessReportTemplate(submitData)
    } else if (props.mode === 'edit') {
      res = await updateAssessReportTemplate(submitData)
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'))
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log('报告模板对话框组件已挂载')
})
</script>

<style scoped>
.report-template-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.file-upload-container {
  width: 100%;
}

.file-upload {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

.file-list {
  margin-top: 16px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.file-icon {
  color: #409eff;
  margin-right: 8px;
  font-size: 16px;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #606266;
  word-break: break-all;
}

:deep(.el-button--danger.is-link) {
  color: #f56c6c;
  padding: 0;
  margin-left: 8px;
}
</style> 