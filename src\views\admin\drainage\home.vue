<template>
  <div class="drainage-home">
    <!-- 顶部卡片统计 -->
    <div class="top-section">
      <!-- 左侧管网信息 -->
      <div class="network-cards">
        <!-- 排水管网 -->
        <div class="network-card" style="background: #F1F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/guanwang.png" alt="排水管网">
          </div>
          <div class="content">
            <div class="title">排水管网</div>
            <div class="data">
              <span class="value">{{ networkData.drainPipeLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
        </div>

        <!-- 雨水管网 -->
        <div class="network-card" style="background: #FFF3F1;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/yushui.png" alt="雨水管网">
          </div>
          <div class="content">
            <div class="title">雨水管网</div>
            <div class="data">
              <span class="value">{{ networkData.rainPipeLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
        </div>

        <!-- 污水管网 -->
        <div class="network-card" style="background: #FFF8F0;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/wushuiwang.png" alt="污水管网">
          </div>
          <div class="content">
            <div class="title">污水管网</div>
            <div class="data">
              <span class="value">{{ networkData.sewagePipeLength }}</span>
              <span class="unit">km</span>
            </div>
          </div>
        </div>

        <!-- 泵站 -->
        <div class="network-card" style="background: #F1F5FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/bengzhan.png" alt="泵站">
          </div>
          <div class="content">
            <div class="title">泵站</div>
            <div class="data">
              <span class="value">{{ networkData.stationCount }}</span>
              <span class="unit">座</span>
            </div>
          </div>
        </div>

        <!-- 排水口 -->
        <div class="network-card" style="background: #E7FCFF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/paishuikou.png" alt="排水口">
          </div>
          <div class="content">
            <div class="title">排水口</div>
            <div class="data">
              <span class="value">{{ networkData.outletCount }}</span>
              <span class="unit">处</span>
            </div>
          </div>
        </div>

        <!-- 污水厂 -->
        <div class="network-card" style="background: #F0F8FF;">
          <div class="icon-box">
            <img src="@/assets/images/mis/drainage/wushuichang.png" alt="污水厂">
          </div>
          <div class="content">
            <div class="title">污水厂</div>
            <div class="data">
              <span class="value">{{ networkData.factoryCount }}</span>
              <span class="unit">座</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧报警信息 -->
      <div class="alarm-info">
        <div class="alarm-row">
          <div class="alarm-title">今日报警</div>
          <div class="alarm-value alarm-today">{{ alarmData.todayCount }}</div>
        </div>
        <div class="alarm-row">
          <div class="alarm-title">本月报警</div>
          <div class="alarm-value alarm-month">{{ alarmData.monthCount }}</div>
        </div>
      </div>
    </div>

    <!-- 第二行：待处理报警和巡检记录 -->
    <div class="second-row">
      <!-- 左侧：待处理报警区域 -->
      <div class="pending-alarm-section">
        <div class="section-header">
          <div class="section-title">待处理报警</div>
        </div>

        <!-- 报警分级统计 -->
        <div class="alarm-levels">
          <div class="level-card level-one">
            <div class="level-name">一级报警</div>
            <div class="level-value">{{ pendingAlarmData.level1count }}</div>
          </div>
          <div class="level-card level-two">
            <div class="level-name">二级报警</div>
            <div class="level-value">{{ pendingAlarmData.level2count }}</div>
          </div>
          <div class="level-card level-three">
            <div class="level-name">三级报警</div>
            <div class="level-value">{{ pendingAlarmData.level3count }}</div>
          </div>
        </div>

        <!-- 报警列表 -->
        <div class="alarm-list">
          <div class="alarm-item" v-for="alarm in pendingAlarmData.alarmList" :key="alarm.alarmId">
            <div class="alarm-info-detail">
              <div class="alarm-title">{{ alarm.deviceName }} ({{ alarm.alarmCode }})</div>
              <div class="alarm-location-time">
                <div class="alarm-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ alarm.address }}</span>
                </div>
                <div class="alarm-time">
                  <el-icon>
                    <Clock />
                  </el-icon>
                  <span>{{ alarm.alarmTime }}</span>
                </div>
              </div>
            </div>
            <div class="alarm-level-tag" 
                 :class="{
                   'level-1-tag': alarm.alarmLevel === '3003601',
                   'level-2-tag': alarm.alarmLevel === '3003602',
                   'level-3-tag': alarm.alarmLevel === '3003603'
                 }">
              {{ alarm.alarmLevelName }}报警
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：巡检记录 -->
      <div class="inspection-table-section">
        <div class="section-header">
          <div class="section-title">近30日巡检记录</div>
        </div>

        <div class="inspection-table-container">
          <el-table :data="inspectionTableData" style="width: 100%"
            :header-cell-style="{ background: '#EEF5FF', color: '#0E1D33', fontWeight: '600' }"
            :row-class-name="tableRowClassName" highlight-current-row>
            <el-table-column prop="index" label="序号" width="70" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="180"></el-table-column>
            <el-table-column prop="location" label="位置" min-width="300"></el-table-column>
            <el-table-column prop="count" label="次数" width="100" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 底部统计和风险面板 -->
    <div class="bottom-section">
      <!-- 左侧统计图表 -->
      <div class="statistics-charts">
        <div class="section-header">
          <div class="section-title">报警统计</div>
          <div class="action">
            <el-radio-group v-model="timeRange" size="small">
              <el-radio-button label="7">近7日</el-radio-button>
              <el-radio-button label="30">近30日</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="statistics-data">
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatsData.alarmCount }}</div>
            <div class="stat-label">全部报警</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatsData.handleCount }}</div>
            <div class="stat-label">已处理</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ alarmStatsData.handleRate }}%</div>
            <div class="stat-label">处理完成率</div>
          </div>
        </div>

        <div ref="trendChartRef" class="trend-chart-container"></div>
      </div>

      <!-- 右侧管网风险面板 -->
      <div class="risk-container">
        <div class="section-header">
          <div class="section-title">管网风险分布</div>
          <div class="action">
            <el-radio-group v-model="riskType" size="small">
              <el-radio-button label="pipeline">管线</el-radio-button>
              <el-radio-button label="sewage">污水厂</el-radio-button>
              <el-radio-button label="pump">泵站</el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div class="risk-content">
          <!-- 风险分布图表 -->
          <div class="risk-chart">
            <div class="risk-distribution">
              <div v-for="item in currentRiskData" :key="item.code" 
                   class="risk-item" :class="item.className">
                <div class="risk-label">{{ item.displayName }}</div>
                <div class="risk-bar" :style="{ width: currentRiskData.length > 0 ? Math.min(100, (item.value / Math.max(...currentRiskData.map(d => d.value), 1)) * 100) + '%' : '0%' }">
                  {{ item.value }}{{ item.unit }}
                </div>
              </div>
              <!-- 当没有数据时显示默认内容 -->
              <div v-if="currentRiskData.length === 0" class="no-data">
                <div class="risk-item risk-normal">
                  <div class="risk-label">暂无数据</div>
                  <div class="risk-bar" style="width: 0%;">--</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onBeforeUnmount, computed, watch } from 'vue'
import { Location, Clock } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import request from '@/utils/request'

// 窗口尺寸状态
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 根据窗口大小计算是否应该截断长文本
const shouldTruncate = computed(() => {
  return windowSize.value.width < 1440
})

// 处理窗口大小变化
const handleResize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  if (trendChart) {
    trendChart.resize()
  }
}

// 管网统计数据
const networkData = ref({
  drainPipeLength: 456,
  rainPipeLength: 20,
  sewagePipeLength: 20,
  factoryCount: 10,
  stationCount: 23,
  outletCount: 101
})

// 报警统计数据
const alarmData = ref({
  todayCount: 15,
  monthCount: 25
})

// 待处理报警数据
const pendingAlarmData = ref({
  level1count: 100,
  level2count: 80,
  level3count: 80,
  alarmList: []
})

// 巡检记录表格数据
const inspectionTableData = ref([])

// 报警趋势统计数据
const alarmStatsData = ref({
  alarmCount: 67,
  handleCount: 35,
  handleRate: 63,
  alarmTrendStatistics: []
})

// 风险分布数据
const riskData = ref({
  pipeline: [],
  sewage: [],
  pump: []
})

// 时间范围选择
const timeRange = ref('7')

// 风险类型选择
const riskType = ref('pipeline')

// 计算当前选中的风险数据
const currentRiskData = computed(() => {
  const data = riskData.value[riskType.value] || []
  const riskTypeMap = {
    '3002401': { name: '高风险', class: 'risk-high' },
    '3002402': { name: '中风险', class: 'risk-medium' },
    '3002403': { name: '一般风险', class: 'risk-low' },
    '3002404': { name: '低风险', class: 'risk-normal' }
  }
  
  return data.map(item => ({
    ...item,
    displayName: riskTypeMap[item.code]?.name || item.name,
    className: riskTypeMap[item.code]?.class || 'risk-normal',
    value: item.length || item.count || 0,
    unit: item.length ? 'km' : '座'
  }))
})

// 图表引用
const trendChartRef = ref(null)
let trendChart = null

// API调用函数
const fetchNetworkOverview = async () => {
  try {
    const response = await request.get('/drain/homePage/overview')
    if (response.code === 200) {
      networkData.value = response.data
    }
  } catch (error) {
    console.error('获取管网概览数据失败:', error)
  }
}

const fetchAlarmCount = async () => {
  try {
    const response = await request.get('/drain/homePage/alarm/count')
    if (response.code === 200) {
      alarmData.value = response.data
    }
  } catch (error) {
    console.error('获取报警统计数据失败:', error)
  }
}

const fetchPendingAlarms = async () => {
  try {
    const response = await request.get('/drain/homePage/alarm/unhandleStatistics', {
      params: { pageNum: 1, pageSize: 10 }
    })
    if (response.code === 200) {
      pendingAlarmData.value = {
        level1count: response.data.level1count,
        level2count: response.data.level2count,
        level3count: response.data.level3count,
        alarmList: response.data.alarmInfoPage.records
      }
    }
  } catch (error) {
    console.error('获取待处理报警数据失败:', error)
  }
}

const fetchAlarmRank = async () => {
  try {
    const response = await request.get('/drain/homePage/alarm/rank', {
      params: { pageNum: 1, pageSize: 10 }
    })
    if (response.code === 200) {
      inspectionTableData.value = response.data.records.map((item, index) => ({
        index: index + 1,
        deviceName: item.deviceName,
        location: item.address,
        count: item.alarmCount
      }))
    }
  } catch (error) {
    console.error('获取报警排名数据失败:', error)
  }
}

const fetchAlarmStatistics = async (dayIndex = 7) => {
  try {
    const response = await request.get('/drain/homePage/alarm/statistics', {
      params: { dayIndex }
    })
    if (response.code === 200) {
      alarmStatsData.value = response.data
      // 更新图表
      initTrendChart()
    }
  } catch (error) {
    console.error('获取报警统计数据失败:', error)
  }
}

const fetchRiskStatistics = async () => {
  try {
    // 获取管线风险统计
    const pipelineResponse = await request.get('/drain/homePage/pipeline/statistics')
    if (pipelineResponse.code === 200) {
      riskData.value.pipeline = pipelineResponse.data.riskLevelStatistics
    }

    // 获取污水厂风险统计
    const factoryResponse = await request.get('/drain/homePage/factory/statistics')
    if (factoryResponse.code === 200) {
      riskData.value.sewage = factoryResponse.data.riskLevelStatistics
    }

    // 获取泵站风险统计
    const stationResponse = await request.get('/drain/homePage/station/statistics')
    if (stationResponse.code === 200) {
      riskData.value.pump = stationResponse.data.riskLevelStatistics
    }
  } catch (error) {
    console.error('获取风险统计数据失败:', error)
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (trendChartRef.value) {
    if (trendChart) {
      trendChart.dispose()
    }

    trendChart = echarts.init(trendChartRef.value)

    // 使用真实数据或默认数据
    const trendData = alarmStatsData.value.alarmTrendStatistics || []
    const dates = trendData.length > 0 ? trendData.map(item => item.date) : ['8/1', '8/2', '8/3', '8/4', '8/5', '8/6', '8/7']
    const counts = trendData.length > 0 ? trendData.map(item => item.count) : [25, 40, 20, 35, 60, 45, 30]

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '报警数',
          type: 'line',
          smooth: true,
          data: counts,
          markPoint: {
            data: [
              { type: 'max', name: '最大值' },
              { type: 'min', name: '最小值' }
            ]
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.8)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    trendChart.setOption(option)
  }
}

// 初始化所有数据
const initAllData = async () => {
  try {
    await Promise.all([
      fetchNetworkOverview(),
      fetchAlarmCount(),
      fetchPendingAlarms(),
      fetchAlarmRank(),
      fetchAlarmStatistics(Number(timeRange.value)),
      fetchRiskStatistics()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

// 表格行的类名 - 用于实现斑马纹效果
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 1 ? 'striped-row' : ''
}

// 监听时间范围变化
watch(timeRange, (newVal) => {
  fetchAlarmStatistics(Number(newVal))
})

// 监听风险类型变化
watch(riskType, () => {
  // 风险数据已经在初始化时全部获取，这里不需要重新获取
  // 只需要通过计算属性来显示对应的数据
})

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // 初始化所有数据
  initAllData()
})

onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', handleResize)

  // 释放图表实例
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
})
</script>

<style scoped>
.drainage-home {
  padding: 1px;
  height: 99%;
  overflow: auto;
}

/* 顶部卡片统计样式 */
.top-section {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.network-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 1556px;
  height: 140px;
  background: #FFFFFF;
  padding: 15px;
  border-radius: 4px;
}

.network-card {
  display: flex;
  width: 240px;
  height: 110px;
  border-radius: 4px;
  padding: 12px;
  box-sizing: border-box;
}

.icon-box {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.icon-box img {
  width: 56px;
  height: 56px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  margin-bottom: 8px;
}

.data {
  display: flex;
  align-items: baseline;
}

.value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #000000;
}

.unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

/* 右侧报警信息样式 */
.alarm-info {
  width: 300px;
  height: 140px;
  background: linear-gradient(180deg, #FFE9E9 0%, #FFF7F7 100%);
  border-radius: 4px;
  border: 1px solid #EFF0F2;
  display: flex;
  gap: 78px;
  padding: 39px 47px;
}

.alarm-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12px;
}

.alarm-row:last-child {
  margin-bottom: 0;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 4px;
  white-space: nowrap;
}

.alarm-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 32px;
}

.alarm-today {
  color: #FF1414;
}

.alarm-month {
  color: #333333;
}

/* 第二行布局样式 */
.second-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

/* 左侧：待处理报警区域样式 */
.pending-alarm-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 右侧：巡检记录区域样式 */
.inspection-table-section {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

/* 区域标题样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #222222;
}

/* 报警分级卡片样式 */
.alarm-levels {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.level-card {
  width: 278px;
  height: 75px;
  border-radius: 4px;
  border: 1px solid #F3F3F3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  box-sizing: border-box;
}

.level-one {
  background: linear-gradient(315deg, #FFFAFA 0%, #FF6565 100%);
}

.level-two {
  background: linear-gradient(135deg, #FFA149 0%, #FFFDFB 100%);
}

.level-three {
  background: linear-gradient(135deg, #8FBAFF 0%, #F3F8FF 100%);
}

.level-name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.level-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
}

/* 报警列表样式 */
.alarm-list {
  max-height: 180px;
  overflow-y: auto;
  padding-right: 8px;
}

.alarm-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #FFFFFF;
  margin-bottom: 12px;
  border-radius: 4px;
  border-left: 4px solid rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.alarm-info-detail {
  display: flex;
  flex-direction: column;
}

.alarm-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #0E1D33;
  margin-bottom: 8px;
}

.alarm-location-time {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: 8px;
}

.alarm-location,
.alarm-time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #647688;
  display: flex;
  align-items: center;
}

.alarm-location :deep(svg),
.alarm-time :deep(svg) {
  margin-right: 4px;
  font-size: 16px;
  color: #909399;
}

.alarm-level-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.level-1-tag {
  background-color: #FFEFEF;
  color: #FF1414;
}

.level-2-tag {
  background-color: #FFF6EC;
  color: #FF7C00;
}

.level-3-tag {
  background-color: #EDF5FF;
  color: #2D7EFF;
}

/* 表格样式 */
.inspection-table-container {
  height: calc(100% - 40px);
  overflow-y: auto;
}

/* 表格斑马纹效果 */
:deep(.striped-row) {
  background-color: #F5F7FA;
}

/* 底部统计和风险面板 */
.bottom-section {
  display: flex;
  gap: 16px;
}

/* 左侧统计图表 */
.statistics-charts {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

.action {
  display: flex;
  align-items: center;
}

.statistics-data {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-family: D-DIN, D-DIN;
  font-weight: bold;
  font-size: 24px;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #909399;
}

.trend-chart-container {
  width: 100%;
  height: 220px;
}

/* 右侧风险面板 */
.risk-container {
  flex: 1;
  height: 378px;
  background: #FFFFFF;
  border: 1px solid #EFF0F2;
  padding: 20px;
  box-sizing: border-box;
  border-radius: 4px;
}

.risk-content {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.risk-distribution {
  width: 100%;
  padding: 20px 0;
}

.risk-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.risk-label {
  width: 80px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  margin-right: 16px;
}

.risk-bar {
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #FFFFFF;
  transition: width 0.5s ease;
}

.risk-high .risk-bar {
  background: linear-gradient(90deg, #FF4D4F 0%, #FF7875 100%);
}

.risk-medium .risk-bar {
  background: linear-gradient(90deg, #FF7A45 0%, #FF9C6E 100%);
}

.risk-low .risk-bar {
  background: linear-gradient(90deg, #FFC53D 0%, #FFD666 100%);
}

.risk-normal .risk-bar {
  background: linear-gradient(90deg, #73D13D 0%, #95DE64 100%);
}


@media (min-height: 900px) and (max-height: 940px) {
  .drainage-home {
    height: 76%;
    overflow: auto;
  }
}
</style>