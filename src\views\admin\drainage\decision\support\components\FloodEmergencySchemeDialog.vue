<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="flood-scheme-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="schemeName">
            <el-input v-model="formData.schemeName" placeholder="请输入方案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源单位" prop="sourceUnit">
            <el-select v-model="formData.sourceUnit" placeholder="请选择" class="w-full" @change="handleSourceUnitChange">
              <el-option v-for="item in enterpriseOptions" :key="item.enterpriseName" :label="item.enterpriseName" :value="item.enterpriseName" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="匹配报警类型" prop="alarmType">
            <el-select v-model="formData.alarmType" placeholder="请选择" class="w-full" @change="handleAlarmTypeChange">
              <el-option v-for="item in alarmTypeOptions" :key="item.alarmType" :label="item.alarmTypeName" :value="item.alarmType" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="匹配事件类型" prop="eventType">
            <el-select v-model="formData.eventType" placeholder="请选择" class="w-full" @change="handleEventTypeChange">
              <el-option v-for="item in eventTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="编制日期" prop="editDate">
            <el-date-picker
              v-model="formData.editDate"
              type="date"
              placeholder="年/月/日"
              style="width: 100%"
              format="YYYY/MM/DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="附件上传" prop="fileUrl">
            <div class="upload-container">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :file-list="fileList"
                :limit="1"
                :disabled="mode === 'view'"
                accept=".doc,.docx,.pdf"
                class="upload-attachment"
              >
                <el-button type="primary" :disabled="mode === 'view'">
                  <el-icon><Upload /></el-icon>
                  选择文件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持.doc、.docx、.pdf文件，大小不超过100M
                  </div>
                </template>
              </el-upload>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Upload } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import {
  saveFloodEmergencyScheme,
  updateFloodEmergencyScheme,
  getEnterpriseList,
  getAlarmType
} from '@/api/drainage';
import { uploadFile } from '@/api/upload';
import { EVENT_TYPE_OPTIONS } from '@/constants/drainage';
import moment from 'moment';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);
const uploadRef = ref(null);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增防汛应急辅助决策方案',
    edit: '编辑防汛应急辅助决策方案',
    view: '防汛应急辅助决策方案详情'
  };
  return titles[props.mode] || '防汛应急辅助决策方案';
});

// 下拉选项数据
const enterpriseOptions = ref([]);
const alarmTypeOptions = ref([]);
const eventTypeOptions = ref(EVENT_TYPE_OPTIONS);
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  schemeName: '',
  sourceUnit: '',
  sourceUnitName: '',
  alarmType: '',
  alarmTypeName: '',
  eventType: '',
  eventTypeName: '',
  editDate: '',
  fileUrl: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  schemeName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  sourceUnit: [{ required: true, message: '请选择来源单位', trigger: 'change' }],
  alarmType: [{ required: true, message: '请选择匹配报警类型', trigger: 'change' }],
  eventType: [{ required: true, message: '请选择匹配事件类型', trigger: 'change' }],
  editDate: [{ required: true, message: '请选择编制日期', trigger: 'change' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 处理文件显示
    if (newVal.fileUrl) {
      const fileName = newVal.fileUrl.split('/').pop() || '附件文件';
      fileList.value = [{
        name: fileName,
        url: newVal.fileUrl,
        uid: Date.now()
      }];
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理来源单位变化
const handleSourceUnitChange = (value) => {
  const selected = enterpriseOptions.value.find(item => item.enterpriseName === value);
  if (selected) {
    formData.sourceUnitName = selected.enterpriseName;
  }
};

// 处理报警类型变化
const handleAlarmTypeChange = (value) => {
  const selected = alarmTypeOptions.value.find(item => item.alarmType === value);
  if (selected) {
    formData.alarmTypeName = selected.alarmTypeName;
  }
};

// 处理事件类型变化
const handleEventTypeChange = (value) => {
  const selected = eventTypeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.eventTypeName = selected.label;
  }
};

// 文件上传变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage.error('上传文件大小不能超过 100MB!');
    return;
  }

  // 检查文件类型
  const allowedTypes = ['.doc', '.docx', '.pdf'];
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('只能上传.doc、.docx、.pdf格式的文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      formData.fileUrl = response.data.url;
      ElMessage.success('文件上传成功');
    } else {
      ElMessage.error('文件上传失败');
    }
  } catch (error) {
    console.error('文件上传失败:', error);
    ElMessage.error('文件上传失败');
  }
};

// 文件删除处理
const handleFileRemove = () => {
  formData.fileUrl = '';
};

// 获取企业列表
const fetchEnterprises = async () => {
  try {
    const res = await getEnterpriseList();
    if (res && res.data) {
      enterpriseOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取企业列表失败', error);
  }
};

// 获取报警类型列表
const fetchAlarmTypes = async () => {
  try {
    const res = await getAlarmType();
    if (res && res.data) {
      alarmTypeOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取报警类型列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    submitData.editDate = moment(submitData.editDate).format('YYYY-MM-DD HH:mm:ss');
    let res;
    if (props.mode === 'add') {
      res = await saveFloodEmergencyScheme(submitData);
    } else if (props.mode === 'edit') {
      res = await updateFloodEmergencyScheme(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEnterprises();
  fetchAlarmTypes();
});
</script>

<style scoped>
.flood-scheme-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-container {
  width: 100%;
}

.upload-attachment {
  width: 100%;
}

:deep(.el-upload__tip) {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 