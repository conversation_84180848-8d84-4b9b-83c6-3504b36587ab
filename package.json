{"name": "kongjiagou", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "file-saver": "^2.0.5", "handlebars": "^4.7.8", "highlight.js": "^11.11.1", "hls.js": "^1.6.2", "html2canvas": "^1.4.1", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "moment": "^2.30.1", "plotly.js-dist-min": "^3.0.1", "quill": "^1.3.7", "video.js": "^8.22.0", "vue": "^3.5.13", "vue-hls-player": "^1.0.6", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^5.2.1", "@vueuse/core": "^13.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "echarts": "^5.6.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.9.7", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.1", "postcss": "^8.5.3", "proj4": "^2.7.6", "sass": "^1.86.3", "tailwindcss": "^3.4.1", "terraformer-wkt-parser": "^1.2.1", "terser": "^5.39.0", "vite": "^6.2.0", "vue-router": "^4.5.0"}}