<template>
  <div class="maintain-record-container">
    <!-- 搜索区域 -->
    <div class="maintain-record-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属桥梁:</span>
          <el-select v-model="formData.bridgeId" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in bridgeOptions" :key="item.id" :label="item.bridgeName" :value="item.id" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">养护类型:</span>
          <el-select v-model="formData.planType" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in planTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">完成日期:</span>
          <el-select v-model="formData.status" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">养护单位:</span>
          <el-select v-model="formData.unitName" class="form-input" placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in unitOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.planName" class="form-input" placeholder="输入计划名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div> 
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" @row-click="handleRowClick" height="100%"
        empty-text="暂无数据">
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="planName" label="名称" min-width="120" />
        <el-table-column prop="bridgeName" label="桥梁名称" min-width="120" />
        <el-table-column prop="planTypeName" label="检测养护类型" min-width="120" />
        <el-table-column prop="planDateStart" label="计划日期" min-width="100">
          <template #default="{ row }">
            {{ formatDate(row.planDateStart) }}
          </template>
        </el-table-column>
        <el-table-column prop="completeTimeStart" label="实际完成日期" min-width="100">
          <template #default="{ row }">
            {{ formatDate(row.completeTimeStart) || '/' }}
          </template>
        </el-table-column>
        <el-table-column prop="statusName" label="检测养护状态" min-width="100" />
        <el-table-column prop="maintainer" label="检测养护人" min-width="100">
          <template #default="{ row }">
            {{ row.maintainer || '/' }}
          </template>
        </el-table-column>
        <el-table-column prop="unitName" label="检测养护单位" min-width="120">
          <template #default="{ row }">
            {{ row.unitName || '/' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">详情</el-button>
              <el-button 
                v-if="row.status !== 2" 
                type="primary" 
                link 
                @click.stop="handleReport(row)"
              >
                填报
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <MaintainRecordDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage } from 'element-plus';
import moment from 'moment';
import { 
  getMaintainPlanPage, 
  getMaintainPlanDetail,
  getBridgeBasicInfoList,
  getMaintenanceEnterpriseList
} from '@/api/bridge';
import {
  MAINTAIN_PLAN_TYPE_OPTIONS,
  MAINTAIN_PLAN_STATUS_OPTIONS
} from '@/constants/bridge';
import MaintainRecordDialog from './components/MaintainRecordDialog.vue';

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 下拉选项数据
const planTypeOptions = ref(MAINTAIN_PLAN_TYPE_OPTIONS);
const statusOptions = ref(MAINTAIN_PLAN_STATUS_OPTIONS);
const bridgeOptions = ref([]);
const unitOptions = ref([]);

// 表单数据
const formData = ref({
  bridgeId: '',
  planType: '',
  status: '',
  unitName: '',
  planName: ''
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref('report'); // 'report' | 'view'
const dialogData = ref({});

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
};

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 处理查询
const handleSearch = () => {
  currentPage.value = 1;
  fetchMaintainPlanData();
};

// 处理重置
const handleReset = () => {
  formData.value = {
    bridgeId: '',
    planType: '',
    status: '',
    unitName: '',
    planName: ''
  };
  currentPage.value = 1;
  fetchMaintainPlanData();
};

// 获取维护计划分页数据
const fetchMaintainPlanData = async () => {
  try {
    const params = {
      bridgeId: formData.value.bridgeId,
      planType: formData.value.planType,
      status: formData.value.status,
      unitName: formData.value.unitName,
      planName: formData.value.planName
    };
    
    const res = await getMaintainPlanPage(currentPage.value, pageSize.value, params);
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || [];
      total.value = res.data.total || 0;
    }
  } catch (error) {
    console.error('获取维护计划数据失败:', error);
    ElMessage.error('获取维护计划数据失败');
    tableData.value = [];
    total.value = 0;
  }
};

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList();
    if (res && res.code === 200) {
      bridgeOptions.value = res.data || [];
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error);
  }
};

// 获取养护单位列表
const fetchUnitList = async () => {
  try {
    const res = await getMaintenanceEnterpriseList({ enterpriseType: '5001002' });
    if (res && res.code === 200) {
      unitOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      })) || [];
    }
  } catch (error) {
    console.error('获取养护单位列表失败', error);
  }
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size;
  fetchMaintainPlanData();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchMaintainPlanData();
};

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row);
};

// 处理填报
const handleReport = async (row) => {
  try {
    const res = await getMaintainPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'report';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取计划详情失败');
    }
  } catch (error) {
    console.error('获取计划详情失败:', error);
    ElMessage.error('获取计划详情失败');
  }
};

// 处理详情
const handleDetail = async (row) => {
  try {
    const res = await getMaintainPlanDetail(row.id);
    if (res && res.code === 200) {
      dialogMode.value = 'view';
      dialogData.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error('获取计划详情失败');
    }
  } catch (error) {
    console.error('获取计划详情失败:', error);
    ElMessage.error('获取计划详情失败');
  }
};

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchMaintainPlanData();
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return moment(dateStr).format('YYYY-MM-DD');
};

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchBridgeList(),
      fetchUnitList(),
      fetchMaintainPlanData()
    ]);
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败');
  }
});
</script>

<style scoped>
.maintain-record-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.maintain-record-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>