<template>
  <div class="maintenance-container">
    <el-tabs v-model="activeTab" class="maintenance-tabs">
      <el-tab-pane label="管网改造辅助决策" name="pipeline">
        <PipelineReform />
      </el-tab-pane>
      <el-tab-pane label="易涝点改造辅助决策" name="point">
        <PointReform />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PipelineReform from './components/PipelineReform.vue'
import PointReform from './components/PointReform.vue'

// 当前激活的选项卡
const activeTab = ref('pipeline')

onMounted(() => {
  console.log('管网运维改造辅助决策组件已挂载')
})
</script>

<style scoped>
.maintenance-container {
  padding: 0;
}

.maintenance-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.mb-4 {
  margin-bottom: 1rem;
}

.bg-white {
  background-color: white;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style> 