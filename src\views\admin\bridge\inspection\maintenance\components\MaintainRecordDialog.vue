<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="maintain-record-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20" v-if="mode !== 'report'">
        <el-col :span="12">
          <el-form-item label="计划名称" prop="planName" required>
            <el-input v-model="formData.planName" placeholder="请输入计划名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属桥梁" prop="bridgeId" required>
            <el-select v-model="formData.bridgeId" placeholder="请选择" class="w-full" @change="handleBridgeChange">
              <el-option v-for="item in bridgeOptions" :key="item.id" :label="item.bridgeName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode !== 'report'">
        <el-col :span="12">
          <el-form-item label="养护类型" prop="planType" required>
            <el-select v-model="formData.planType" placeholder="请选择" class="w-full" @change="handlePlanTypeChange">
              <el-option v-for="item in planTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="养护单位" prop="unitName">
            <el-select v-model="formData.unitName" placeholder="请选择" class="w-full">
              <el-option v-for="item in unitOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode !== 'report'">
        <el-col :span="12">
          <el-form-item label="计划开始日期" prop="planDateStart" required>
            <el-date-picker
              v-model="formData.planDateStart"
              type="datetime"
              placeholder="请选择开始日期"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划结束日期" prop="planDateEnd" required>
            <el-date-picker
              v-model="formData.planDateEnd"
              type="datetime"
              placeholder="请选择结束日期"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'report'">
        <el-col :span="12">
          <el-form-item label="完成日期" prop="completeTime" required>
            <el-date-picker
              v-model="formData.completeTime"
              type="date"
              placeholder="请选择完成日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测养护人" prop="maintainer" required>
            <el-input v-model="formData.maintainer" placeholder="请输入检测养护人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'view'">
        <el-col :span="12">
          <el-form-item label="实际开始日期">
            <el-input :value="formatDateTime(formData.completeTimeStart)" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际结束日期">
            <el-input :value="formatDateTime(formData.completeTimeEnd)" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'view'">
        <el-col :span="12">
          <el-form-item label="养护人">
            <el-input v-model="formData.maintainer" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态">
            <el-input v-model="formData.statusName" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'report'">
        <el-col :span="24">
          <el-form-item label="养护结果" prop="resultDescription" required>
            <el-input
              v-model="formData.resultDescription"
              type="textarea"
              :rows="4"
              placeholder="请输入养护结果"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'view'">
        <el-col :span="24">
          <el-form-item label="结果描述">
            <el-input
              v-model="formData.resultDescription"
              type="textarea"
              :rows="4"
              readonly
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode !== 'report'">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="mode === 'view'">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" readonly />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检测养护照片">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :file-list="fileList"
              list-type="picture-card"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon v-if="mode !== 'view'"><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  大小20M以内，建议尺寸16：9
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode === 'report'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import {
  getMaintainPlanDetail,
  reportMaintainPlan,
  getBridgeBasicInfoList,
  getMaintenanceEnterpriseList
} from '@/api/bridge'
import { uploadFile } from '@/api/upload'
import {
  MAINTAIN_PLAN_TYPE_OPTIONS,
  MAINTAIN_PLAN_STATUS_OPTIONS
} from '@/constants/bridge'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'report', // 'report', 'view'
    validator: (value) => ['report', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 表单引用
const formRef = ref(null)
const fileList = ref([])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    report: '养护记录填报',
    view: '养护记录详情'
  }
  return titles[props.mode] || '养护记录'
})

// 下拉选项数据
const planTypeOptions = ref(MAINTAIN_PLAN_TYPE_OPTIONS)
const statusOptions = ref(MAINTAIN_PLAN_STATUS_OPTIONS)
const bridgeOptions = ref([])
const unitOptions = ref([])

// 表单数据
const formData = reactive({
  id: '',
  bridgeId: '',
  bridgeName: '',
  planName: '',
  planType: '',
  planTypeName: '',
  planDateStart: '',
  planDateEnd: '',
  completeTime: '',
  completeTimeStart: '',
  completeTimeEnd: '',
  maintainer: '',
  unitName: '',
  resultDescription: '',
  remark: '',
  maintenancePhotos: '',
  status: 1,
  statusName: ''
})

// 表单验证规则
const formRules = {
  planName: [{ 
    required: true, 
    message: '请输入计划名称', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (props.mode !== 'report' && !value) {
        callback(new Error('请输入计划名称'))
      } else {
        callback()
      }
    }
  }],
  bridgeId: [{ 
    required: true, 
    message: '请选择所属桥梁', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (props.mode !== 'report' && !value) {
        callback(new Error('请选择所属桥梁'))
      } else {
        callback()
      }
    }
  }],
  planType: [{ 
    required: true, 
    message: '请选择养护类型', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (props.mode !== 'report' && !value) {
        callback(new Error('请选择养护类型'))
      } else {
        callback()
      }
    }
  }],
  planDateStart: [{ 
    required: true, 
    message: '请选择计划开始日期', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (props.mode !== 'report' && !value) {
        callback(new Error('请选择计划开始日期'))
      } else {
        callback()
      }
    }
  }],
  planDateEnd: [{ 
    required: true, 
    message: '请选择计划结束日期', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (props.mode !== 'report' && !value) {
        callback(new Error('请选择计划结束日期'))
      } else {
        callback()
      }
    }
  }],
  completeTime: [{ 
    required: true, 
    message: '请选择完成日期', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (props.mode === 'report' && !value) {
        callback(new Error('请选择完成日期'))
      } else {
        callback()
      }
    }
  }],
  maintainer: [{ 
    required: true, 
    message: '请输入检测养护人', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (props.mode === 'report' && !value) {
        callback(new Error('请输入检测养护人'))
      } else {
        callback()
      }
    }
  }],
  resultDescription: [{ 
    required: true, 
    message: '请输入养护结果', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (props.mode === 'report' && !value) {
        callback(new Error('请输入养护结果'))
      } else {
        callback()
      }
    }
  }]
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (typeof formData[key] === 'number') {
      formData[key] = 0
    } else {
      formData[key] = ''
    }
  })
  formData.status = 1
  fileList.value = []
}

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key]
      }
    })
    // 处理图片显示
    if (newVal.maintenancePhotos) {
      fileList.value = newVal.maintenancePhotos.split(',').filter(url => url.trim()).map((url, index) => ({
        name: `image_${index}`,
        url: url,
        uid: Date.now() + index
      }))
    } else {
      fileList.value = []
    }
  } else if (props.mode === 'report') {
    resetForm()
  }
}, { immediate: true, deep: true })

// 处理桥梁选择变化
const handleBridgeChange = (value) => {
  const selected = bridgeOptions.value.find(item => item.id === value)
  if (selected) {
    formData.bridgeName = selected.bridgeName
  }
}

// 处理养护类型选择变化
const handlePlanTypeChange = (value) => {
  const selected = planTypeOptions.value.find(item => item.value === value)
  if (selected) {
    formData.planTypeName = selected.label
  }
}

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!')
    return
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw)
    if (response.status === 200) {
      const urls = formData.maintenancePhotos ? formData.maintenancePhotos.split(',').filter(url => url.trim()) : []
      urls.push(response.data.url)
      formData.maintenancePhotos = urls.join(',')
      ElMessage.success('上传成功')
    } else {
      ElMessage.error('上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败')
  }
}

// 文件移除处理
const handleFileRemove = (file, fileList) => {
  if (formData.maintenancePhotos) {
    const urls = formData.maintenancePhotos.split(',').filter(url => url.trim())
    const index = urls.findIndex(url => url === file.url)
    if (index > -1) {
      urls.splice(index, 1)
      formData.maintenancePhotos = urls.join(',')
    }
  }
}

// 获取桥梁列表
const fetchBridgeList = async () => {
  try {
    const res = await getBridgeBasicInfoList()
    if (res && res.code === 200) {
      bridgeOptions.value = res.data || []
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error)
  }
}

// 获取养护单位列表
const fetchUnitList = async () => {
  try {
    const res = await getMaintenanceEnterpriseList({ enterpriseType: '5001002' })
    if (res && res.code === 200) {
      unitOptions.value = res.data.map(item => ({
        label: item.enterpriseName,
        value: item.enterpriseName
      })) || []
    }
  } catch (error) {
    console.error('获取养护单位列表失败', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
  resetForm()
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  return moment(dateStr).format('YYYY-MM-DD HH:mm:ss')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 将时间转换为API要求的格式
    // let completeTimeObj = null
    // if (formData.completeTime) {
    //   const momentTime = moment(formData.completeTime)
    //   completeTimeObj = {
    //     date: momentTime.date(),
    //     day: momentTime.day(),
    //     hours: momentTime.hours(),
    //     minutes: momentTime.minutes(),
    //     month: momentTime.month(),
    //     nanos: 0,
    //     seconds: momentTime.seconds(),
    //     time: momentTime.valueOf(),
    //     timezoneOffset: momentTime.utcOffset(),
    //     year: momentTime.year() - 1900  // Java Date requires year since 1900
    //   }
    // }

    const submitData = {
      maintainPlanId: formData.id,
      completeTime: moment(formData.completeTime).format('YYYY-MM-DD HH:mm:ss'),
      maintainer: formData.maintainer,
      maintenancePhotos: formData.maintenancePhotos,
      resultDescription: formData.resultDescription
    }

    const res = await reportMaintainPlan(submitData)

    if (res && res.code === 200) {
      ElMessage.success('填报成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(res?.msg || '填报失败')
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBridgeList()
  fetchUnitList()
})
</script>

<style scoped>
.maintain-record-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item[required] .el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 