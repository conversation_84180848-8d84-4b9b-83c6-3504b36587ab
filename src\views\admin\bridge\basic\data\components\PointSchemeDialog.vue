<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="900px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="point-scheme-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方案名称" prop="schemeName">
            <el-input v-model="formData.schemeName" placeholder="请输入方案名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属桥梁" prop="bridgeId">
            <el-select v-model="formData.bridgeId" placeholder="请选择桥梁" class="w-full" @change="handleBridgeChange">
              <el-option v-for="item in bridgeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="方案上传" prop="fileUrl" v-if="mode !== 'view'">
            <div class="upload-container">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :on-change="handleFileChange"
                :show-file-list="false"
                accept=".doc,.docx,.pdf,.dwg"
                class="upload-demo"
              >
                <el-button type="primary">
                  <el-icon><Upload /></el-icon>
                  选择文件
                </el-button>
              </el-upload>
              <div class="upload-tip">
                每实际作业最多上传5个文件，单文件大小不超过30M
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 文件列表 -->
      <el-row :gutter="20" v-if="fileList.length > 0">
        <el-col :span="24">
          <el-form-item label="文件列表">
            <el-table :data="fileList" style="width: 100%" border>
              <el-table-column prop="name" label="文件名" min-width="200" />
              <el-table-column prop="size" label="大小" width="100">
                <template #default="{ row }">
                  {{ formatFileSize(row.size) }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <span v-if="row.status === 'uploading'" class="text-blue-500">上传中</span>
                  <span v-else-if="row.status === 'success'" class="text-green-500">已上传</span>
                  <span v-else-if="row.status === 'error'" class="text-red-500">上传失败</span>
                  <span v-else class="text-gray-500">待上传</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row, $index }">
                  <div class="operation-btns">
                    <el-button type="primary" link @click="handleDownload(row)" v-if="row.url">下载</el-button>
                    <el-button type="danger" link @click="handleFileDelete($index)" :disabled="mode === 'view'">删除</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'" :loading="submitLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox, ElIcon } from 'element-plus';
import { Upload } from '@element-plus/icons-vue';
import {
  savePointScheme,
  updatePointScheme,
  getBridgeBasicInfoList
} from '@/api/bridge';
import { uploadFile } from '@/api/upload';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  selectedBridgeId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);
const uploadRef = ref(null);

// 加载状态
const submitLoading = ref(false);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增桥梁布点方案',
    edit: '编辑桥梁布点方案',
    view: '桥梁布点方案详情'
  };
  return titles[props.mode] || '桥梁布点方案';
});

// 下拉选项数据
const bridgeOptions = ref([]);

// 文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  id: '',
  schemeName: '',
  bridgeId: '',
  bridgeName: '',
  fileUrl: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  schemeName: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  bridgeId: [{ required: true, message: '请选择所属桥梁', trigger: 'change' }],
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    
    // 如果有文件URL，解析文件信息
    if (newVal.fileUrl) {
      try {
        const files = JSON.parse(newVal.fileUrl);
        fileList.value = files.map(file => ({
          ...file,
          status: 'success'
        }));
      } catch (error) {
        // 如果解析失败，可能是单个文件URL
        if (newVal.fileUrl.trim()) {
          fileList.value = [{
            name: '方案文件',
            url: newVal.fileUrl,
            status: 'success'
          }];
        }
      }
    }
  } else if (props.mode === 'add') {
    resetForm();
    // 新增模式下，默认选中传入的桥梁
    if (props.selectedBridgeId) {
      formData.bridgeId = props.selectedBridgeId;
      handleBridgeChange(props.selectedBridgeId);
    }
  }
}, { immediate: true, deep: true });

// 处理桥梁变化
const handleBridgeChange = (value) => {
  const selected = bridgeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.bridgeName = selected.label;
  }
};

// 处理文件选择
const handleFileChange = async (file) => {
  // 检查文件类型
  const allowedTypes = ['.doc', '.docx', '.pdf', '.dwg'];
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('只支持 .doc .docx .pdf .dwg 格式的文件');
    return;
  }

  // 检查文件大小 (30MB)
  if (file.size > 30 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过30MB');
    return;
  }

  // 检查文件数量
  if (fileList.value.length >= 5) {
    ElMessage.error('最多只能上传5个文件');
    return;
  }

  const fileItem = {
    name: file.name,
    size: file.size,
    file: file.raw,
    status: 'uploading'
  };

  fileList.value.push(fileItem);

  try {
    const res = await uploadFile(file.raw);
    if (res && res.code === 200) {
      fileItem.url = res.data.url;
      fileItem.status = 'success';
      ElMessage.success('文件上传成功');
    } else {
      fileItem.status = 'error';
      ElMessage.error(res?.msg || '文件上传失败');
    }
  } catch (error) {
    fileItem.status = 'error';
    ElMessage.error('文件上传失败');
    console.error('文件上传失败:', error);
  }
};

// 删除文件
const handleFileDelete = (index) => {
  ElMessageBox.confirm('确定要删除该文件吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    fileList.value.splice(index, 1);
    ElMessage.success('删除成功');
  }).catch(() => {
    // 取消删除
  });
};

// 下载文件
const handleDownload = (file) => {
  if (!file.url) {
    ElMessage.error('文件地址不存在');
    return;
  }
  
  // 创建隐藏的下载链接
  const link = document.createElement('a');
  link.href = file.url;
  link.download = file.name || '下载文件';
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(size) / Math.log(k));
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取桥梁列表
const fetchBridges = async () => {
  try {
    const res = await getBridgeBasicInfoList({});
    if (res && res.data) {
      bridgeOptions.value = res.data.map(item => ({
        label: item.bridgeName,
        value: item.id
      }));
    }
  } catch (error) {
    console.error('获取桥梁列表失败', error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 构建文件URL字符串
    const validFiles = fileList.value.filter(file => file.status === 'success' && file.url);
    formData.fileUrl = validFiles.length > 0 ? JSON.stringify(validFiles.map(file => ({
      name: file.name,
      url: file.url,
      size: file.size
    }))) : '';

    const submitData = { ...formData };
    
    submitLoading.value = true;
    let res;
    if (props.mode === 'add') {
      res = await savePointScheme(submitData);
    } else if (props.mode === 'edit') {
      res = await updatePointScheme(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  } finally {
    submitLoading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchBridges();
});
</script>

<style scoped>
.point-scheme-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item__label::before) {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.el-form-item:not([required]) .el-form-item__label::before) {
  content: '';
  margin-right: 0;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.w-full {
  width: 100%;
}

.upload-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

.text-blue-500 { color: #3b82f6; }
.text-green-500 { color: #10b981; }
.text-red-500 { color: #ef4444; }
.text-gray-500 { color: #6b7280; }
</style> 