# 检测养护记录管理功能

本功能实现了桥梁检测养护记录的增删改查管理，包括分页列表、条件查询、详情查看和记录填报功能。

## 功能特性

### 1. 列表页面功能
- ✅ 分页查询支持
- ✅ 多条件筛选（所属桥梁、养护类型、完成状态、养护单位、计划名称）
- ✅ 表格数据展示（序号、名称、桥梁名称、检测养护类型、计划日期、实际完成日期、检测养护状态、检测养护人、检测养护单位）
- ✅ 操作按钮（详情、填报）
- ✅ 状态控制（已完成状态不显示填报按钮）

### 2. 弹窗表单功能
- ✅ 支持两种模式：填报模式、详情查看模式
- ✅ 表单验证（必填字段、数据格式验证）
- ✅ 下拉选择（所属桥梁、养护类型、养护单位）
- ✅ 日期时间选择器
- ✅ 图片上传功能（支持多张图片，大小限制20MB）
- ✅ 文本域输入（结果描述、备注）

### 3. 数据接口
- ✅ 列表查询：`GET /bridge/usmMaintainPlan/page/{pageNum}/{pageSize}`
- ✅ 详情查询：`GET /bridge/usmMaintainPlan/{id}`
- ✅ 养护记录上报：`POST /bridge/usmMaintainPlan/reporting`
- ✅ 桥梁列表：`POST /bridge/usmBridgeBasicInfo/list`
- ✅ 养护单位：`POST /bridge/usmMaintenanceEnterprise/list`
- ✅ 文件上传：`POST /file/upload`

## 技术实现

### 1. 常量定义
在 `src/constants/bridge.js` 中添加了：
- `MAINTAIN_PLAN_TYPE_OPTIONS`: 监测养护类型选项
- `MAINTAIN_PLAN_STATUS_OPTIONS`: 监测养护状态选项
- 对应的映射关系

### 2. API接口
在 `src/api/bridge.js` 中添加了：
```javascript
// 养护记录上报
export function reportMaintainPlan(data) {
  return request({
    url: '/bridge/usmMaintainPlan/reporting',
    method: 'post',
    data
  })
}
```

### 3. 组件结构
```
src/views/admin/bridge/inspection/maintenance/
├── record.vue                          # 主页面
└── components/
    └── MaintainRecordDialog.vue        # 弹窗组件
```

### 4. 主要功能

#### 主页面（record.vue）
- 搜索表单：支持多条件筛选
- 数据表格：展示养护记录列表
- 分页组件：支持页码和每页条数选择
- 操作按钮：详情查看和记录填报

#### 弹窗组件（MaintainRecordDialog.vue）
- 表单验证：必填字段校验
- 文件上传：支持图片上传和预览
- 时间处理：符合后端API要求的时间格式转换
- 动态表单：根据模式显示不同字段

## 使用说明

### 1. 查看记录列表
- 进入检测养护记录管理页面
- 可使用搜索条件进行筛选
- 支持分页浏览

### 2. 查看记录详情
- 点击列表中的"详情"按钮
- 弹窗显示完整的记录信息
- 所有字段为只读状态

### 3. 填报养护记录
- 点击列表中的"填报"按钮（仅未完成状态显示）
- 填写必要信息：完成日期、养护人、结果描述
- 可上传养护照片
- 提交后更新记录状态

## 数据字段说明

### 实体字段
- `bridgeId`: 桥梁ID
- `bridgeName`: 桥梁名称
- `planName`: 计划名称
- `planType`: 养护类型代码
- `planTypeName`: 养护类型名称
- `planDateStart`: 计划开始日期
- `planDateEnd`: 计划结束日期
- `completeTime`: 完成时间
- `completeTimeStart`: 实际开始时间
- `completeTimeEnd`: 实际结束时间
- `maintainer`: 养护人
- `unitName`: 养护单位名称
- `resultDescription`: 结果描述
- `remark`: 备注
- `maintenancePhotos`: 养护照片URL（多个用逗号分隔）
- `status`: 状态（1-未完成，2-已完成）
- `statusName`: 状态名称

### 时间格式处理
- 显示格式：`YYYY-MM-DD HH:mm:ss`
- API提交格式：Java Date对象格式
- 使用moment.js进行时间转换

## 样式设计

参考现有系统风格：
- 布局样式参考 `building.vue`
- 弹窗样式参考 `BuildingDialog.vue`
- 使用Element Plus组件库
- 响应式布局设计
- 统一的颜色和字体规范

## 错误处理

- API调用异常处理
- 表单验证错误提示
- 文件上传错误处理
- 用户友好的错误信息展示

## 扩展性

代码结构清晰，易于扩展：
- 新增字段只需修改表单和常量
- 新增验证规则只需扩展 formRules
- 新增操作按钮只需在操作列添加
- 支持国际化扩展 