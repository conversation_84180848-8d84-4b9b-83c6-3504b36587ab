<template>
  <div class="min-h-screen flex flex-col" style="background-color: #EEF1F5;">
    <!-- 顶部导航栏 -->
    <header class="header-bg">
      <div class="px-4 flex items-center h-16" style="height: 65px;">
        <!-- 系统Logo -->
        <div class="flex-shrink-0 flex items-center mr-4">
          <img src="/logo.png" alt="Logo" class="h-8 w-8" />
        </div>

        <!-- 系统名称+专项名称 -->
        <h1 class="system-title mr-8">东明县城市生命线物联感知平台｜{{ specialtyName }}</h1>

        <!-- 一级菜单图标按钮 -->
        <div class="flex items-center flex-1">
          <el-popover placement="bottom" :width="360" trigger="click" popper-class="menu-popover">
            <template #reference>
              <div class="menu-icon-button flex items-center justify-center cursor-pointer">
                <img src="@/assets/images/mis/mis_menu.png" alt="Menu" class="w-5 h-5"
                  style="width: 20px; height: 20px;" />
              </div>
            </template>
            <!-- 气泡面板中的菜单 -->
            <div class="menu-panel flex flex-wrap justify-between">
              <div v-for="item in [
                { index: 'comprehensive', label: '综合专项' },
                { index: 'gas', label: '燃气专项' },
                { index: 'drainage', label: '排水专项' },
                { index: 'heating', label: '供热专项' },
                { index: 'bridge', label: '桥梁专项' },
                { index: 'system', label: '系统管理' }
              ]" :key="item.index" class="menu-item py-2 px-4 cursor-pointer mb-2"
                :class="{ 'active': activeFirstLevel === item.index }" style="width: 48%;"
                @click="handleFirstLevelSelect(item.index)">
                {{ item.label }}
              </div>
            </div>
          </el-popover>

          <!-- 二级菜单 -->
          <div class="secondary-menu flex items-center ml-4">
            <div v-for="item in secondaryMenuItems" :key="item.path"
              class="secondary-menu-item px-4 py-1 mx-1 cursor-pointer"
              :class="{ 'active': route.path.startsWith(item.path) }" @click="handleSecondaryMenuSelect(item)">
              {{ item.meta ? item.meta.title : '未命名菜单' }}
            </div>
          </div>
        </div>

        <!-- 大屏跳转按钮，系统管理时隐藏 -->
        <div v-if="activeFirstLevel !== 'system'" class="mr-4 flex items-center cursor-pointer" @click="goToScreen">
          <img :src="getSpecialtyIcon" alt="Icon" class="mr-2" style="width: 20px; height: 20px;" />
          <span class="text-white">{{ specialtyName.replace('专项', '') }}一张图</span>
        </div>

        <!-- 用户信息 -->
        <div class="flex items-center">
          <el-dropdown>
            <span class="flex items-center text-white hover:text-white cursor-pointer">
              <img src="@/assets/images/mis/avatar.png" alt="Avatar" class="mr-1 rounded-full"
                style="width: 20px; height: 20px;" />
              {{ userName }} <el-icon class="ml-1"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showPasswordDialog = true">修改密码</el-dropdown-item>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </header>

    <!-- 密码修改弹窗 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="400px">
      <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdatePassword">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <div class="flex-1 flex">
      <!-- 侧边栏 - 非首页时显示 -->
      <div v-if="!isHomePage" class="bg-white shadow-sm sidebar-container"
        :class="{ 'expanded': !isCollapse, 'collapsed': isCollapse }">
        <SideMenu :menu-list="sideMenuList" :is-collapse="isCollapse" />
      </div>

      <!-- 主内容区 -->
      <div class="flex-1 flex flex-col main-content">
        <!-- 面包屑导航和折叠按钮 -->
        <div class="p-3 flex items-center bread-crumb-container">
          <!-- 折叠按钮 - 非首页时显示 -->
          <div v-if="!isHomePage" class="mr-4 cursor-pointer" @click="toggleSideMenu">
            <el-icon :size="20">
              <component :is="isCollapse ? 'Expand' : 'Fold'" />
            </el-icon>
          </div>

          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="route.meta && route.meta.title">{{ route.meta.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 标签页导航 -->
        <TagsView />

        <!-- 页面内容区 -->
        <div class="flex-1 content-container">
          <div class="mis-gis-main">
            <div class="mis-gis-wrapper" :class="{'custom-height': menuTag}"  v-if="visibleMap">
              <MapGisMis />
            </div>
            <div class="mis-gis-Dialog">
              <MapContent />
            </div>
          </div>
          <router-view v-slot="{ Component }">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </router-view>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowDown, Fold, Expand } from '@element-plus/icons-vue'
import SideMenu from '@/components/admin/SideMenu.vue'
import TagsView from '@/components/admin/TagsView.vue'
import MapGisMis from '@/components/GisMap/components/MapGisMis/index.vue' // 地图组件
import MapContent from '@/components/GisMap/components/MapContent/index.vue' // 地图定位弹框
import { adminRoutes } from '@/router/admin.js'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import emitter from "@/utils/mitt.js";

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 密码修改相关
const showPasswordDialog = ref(false)
const passwordFormRef = ref(null)
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 侧边栏折叠状态
const isCollapse = ref(false)

// 当前激活的一级菜单
const activeFirstLevel = computed(() => {
  const path = route.path
  const firstLevel = path.split('/')[1]
  return firstLevel || 'comprehensive'
})

// 计算用户名称
const userName = computed(() => {
  return userStore.userInfo?.nickName || '系统管理员'
})

// 判断当前是否为首页路径（包括各专项的首页）
const isHomePage = computed(() => {
  const path = route.path
  const pathParts = path.split('/')
  return pathParts.length === 3 && pathParts[2] === 'home'
})

// 二级菜单项，显示在顶部导航栏
const secondaryMenuItems = computed(() => {
  // 根据当前一级菜单筛选所有二级菜单
  return adminRoutes[0].children
    .filter(item => {
      const itemFirstLevel = item.path.split('/')[1]
      return itemFirstLevel === activeFirstLevel.value
    })
    .map(item => ({
      path: item.path,
      redirect: item.redirect,
      meta: item.meta
    }))
})

// 专项名称映射
const specialtyMap = {
  comprehensive: '综合专项',
  gas: '燃气专项',
  drainage: '排水专项',
  heating: '供热专项',
  bridge: '桥梁专项',
  system: '系统管理'
}

// 当前专项名称
const specialtyName = computed(() => {
  return specialtyMap[activeFirstLevel.value] || '综合专项'
})

// 缓存的视图
const cachedViews = computed(() => {
  return route.matched
    .filter(item => item.meta && item.meta.keepAlive)
    .map(item => item.name)
})

// 侧边栏菜单列表，只显示3级和4级菜单
const sideMenuList = computed(() => {
  // 如果是首页，不显示侧边栏菜单
  if (isHomePage.value) {
    return []
  }

  // 获取当前路由路径的分段
  const pathSegments = route.path.split('/')

  // 如果路径长度小于3，说明还未进入到二级菜单，不显示侧边栏
  if (pathSegments.length < 3) {
    return []
  }

  // 当前激活的二级菜单路径
  const activeSecondaryPath = `/${pathSegments[1]}/${pathSegments[2]}`

  // 找到当前激活的二级菜单
  const activeSecondaryMenu = adminRoutes[0].children.find(item =>
    item.path === activeSecondaryPath || item.redirect === activeSecondaryPath
  )

  // 如果找到二级菜单且有子菜单(3级菜单)，则返回这些子菜单
  if (activeSecondaryMenu && activeSecondaryMenu.children) {
    return activeSecondaryMenu.children
  }

  return []
})

// 切换侧边栏折叠状态
const toggleSideMenu = () => {
  isCollapse.value = !isCollapse.value
}

// 确保在路由变化时默认展开菜单
const updateMenuExpansion = () => {
  // 获取当前二级菜单路径
  const pathSegments = route.path.split('/')
  if (pathSegments.length >= 3) {
    const secondaryPath = `/${pathSegments[1]}/${pathSegments[2]}`

    // 找到对应的二级菜单项
    const menuItem = secondaryMenuItems.value.find(item =>
      item.path === secondaryPath || item.redirect === secondaryPath
    )

    // 如果找到了二级菜单且有重定向，则跳转到其默认子菜单
    if (menuItem && menuItem.redirect && route.path === menuItem.path) {
      router.push(menuItem.redirect)
      return true
    }
  }
  return false
}

// 监听路由变化，自动展开菜单
watch(() => route.path, (newPath, oldPath) => {
  // 只有当路径变化到不同的二级菜单时才执行自动展开
  if (newPath.split('/')[2] !== oldPath.split('/')[2]) {
    setTimeout(() => {
      updateMenuExpansion()
    }, 100)
  }
})

// 处理一级菜单选择
const handleFirstLevelSelect = (index) => {
  // 系统管理时跳转到/system/management 路由，综合专项时跳转到/comprehensive/industry 路由，其他专项跳转到对应一级菜单的首页
  if (index === 'comprehensive') {
    router.push(`/comprehensive/industry`)
  } else if (index === 'system') {
    router.push(`/system/management`)
  } else {
    router.push(`/${index}/home`)
  }
}

// 处理二级菜单选择
const handleSecondaryMenuSelect = (item) => {
  // 如果有重定向路径，则直接跳转
  if (item.redirect) {
    router.push(item.redirect)
  } else {
    router.push(item.path)
  }
}

//地图页面路由
const mapRouters = [
  "/gas/risk/explosion/heatmap", // 燃气管网风险热力图
  "/gas/risk/explosion/station-heatmap", // 燃气管网风险站点热力图
  // "/gas/leak/monitor/video", // 视频监控
  "/gas/predict/warning/source", //可燃气体泄漏溯源分析
  "/gas/predict/warning/diffusion", //可燃气体扩散分析
  "/gas/predict/warning/damage", //可燃气体爆炸损伤范围分析
  // "/drainage/monitoringMis/alarm/video", // 排水监控视频
  // "/drainage/decisionMis/support/flood", // 排水防汛调度辅助决策
  "/drainage/predict/warning/network", // 排水管网模型预测预警
  "/drainage/predict/warning/flooding", // 排水内涝模型预测预警
  "/heating/riskMis/management/network-distribution", // 供热管网风险分布图
  "/heating/riskMis/management/station-distribution", // 供热站点风险分布图
  "/heating/riskMis/management/hidden-distribution", // 供热隐患分布图
  "/heating/riskMis/management/protection-distribution", // 供热防护分布图
  "/heating/monitoringMis/warning/source",  // 热源运行安全风险防控 临时-- todo
  "/heating/monitoringMis/warning/leak", // 供热管网泄漏研判分析
  "/heating/decisionMis/support/analysis", // 供热空间分析
  "/heating/decisionMis/support/display", // 供热态势标绘与展示
  // "/bridge/monitoring/realtime/video", // 桥梁视频监控
  "/comprehensive/riskMis/control/map", // 综合风险四色图
];
// 是否显示地图页面
const misMapShow = ref(false);
const menuTag = computed(() => {
    return (route.query && (
        route.query?.floodTab==='materials' ||
        route.query?.floodTab==='evaluation' ||
        route.query?.floodTab==='video' ||
        route.query?.floodTab==='stationSynchronization'
        )
    )
});
// 计算是否显示地图页面
const visibleMap = computed(() => {
  const { path } = route;
  // console.log('当前路由路径:route.query--->>>', route.query);
  return (route.query && (
      route.query?.floodTab==='materials' ||
      route.query?.floodTab==='evaluation' ||
      route.query?.floodTab==='stationSynchronization'
      )) || mapRouters.indexOf(path) > -1 || misMapShow.value;
});
watch(
  () => route.path,
  () => {
    misMapShow.value = false;
  }
);

// 在组件挂载时，也执行一次展开逻辑
onMounted(async () => {
  // 初始化时，如果路径是根路径，则跳转到默认页面
  if (route.path === '/') {
    await router.push('/comprehensive/home')
  } else {
    // 如果不是根路径，执行菜单展开逻辑
    updateMenuExpansion()
  }

  // 获取用户信息
  await userStore.getUserInfo()
  // 切换地图模式事件
  emitter.on("handleMisMapShow", (v) => {
    misMapShow.value = v;
  });
})

// 跳转到大屏
const goToScreen = () => {
  // 根据当前专项跳转到对应的大屏页面
  const screenRouteMap = {
    'gas': '/gas/overview',
    'comprehensive': '/comprehensive/overview',
    'drainage': '/drainage/overview',
    'heating': '/heating/overview',
    'bridge': '/bridge'
  }

  // 获取当前专项对应的路由路径，如果没有则默认跳转到综合大屏
  const routePath = screenRouteMap[activeFirstLevel.value] || screenRouteMap.comprehensive
  router.push(routePath)
}

// 退出登录
const logout = () => {
  localStorage.removeItem('Admin-Token')
  router.push('/login')
}

// 密码修改
const handleUpdatePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!userStore.userInfo?.userId) {
        ElMessage.error('用户信息不完整')
        return
      }

      const success = await userStore.updatePassword(
        userStore.userInfo.userId,
        passwordForm.value.newPassword
      )

      if (success) {
        ElMessage.success('密码修改成功')
        showPasswordDialog.value = false
        passwordForm.value = {
          newPassword: '',
          confirmPassword: ''
        }
      }
    }
  })
}

// 获取当前专项对应的图标
const getSpecialtyIcon = computed(() => {
  const iconMap = {
    'gas': new URL('@/assets/images/mis/gas.png', import.meta.url).href,
    'heating': new URL('@/assets/images/mis/reli.png', import.meta.url).href,
    'bridge': new URL('@/assets/images/mis/qiaoliang.png', import.meta.url).href,
    'drainage': new URL('@/assets/images/mis/paishui.png', import.meta.url).href,
    'comprehensive': new URL('@/assets/images/mis/comp.png', import.meta.url).href
  }

  return iconMap[activeFirstLevel.value] || iconMap.comprehensive
})
</script>

<style scoped>
/* 顶部背景 */
.header-bg {
  background-image: url('@/assets/images/mis/mis_bg.png');
  background-repeat: no-repeat;
  background-size: 102%;
  background-position: center;
  height: 65px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.menu-popover) {
  background: #1E62C0;
  border: none;
  padding: 0;
}

/* 系统标题 */
.system-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 20px;
  color: #FFFFFF;
  line-height: 28px;
  text-align: left;
  font-style: normal;
}

/* 自定义样式 */
.el-menu--horizontal {
  border-bottom: none;
  background-color: transparent;
}

.el-menu--horizontal .el-menu-item {
  color: rgba(255, 255, 255, 0.7);
  border-bottom-color: transparent;
}

.el-menu--horizontal .el-menu-item.is-active {
  color: #ffffff;
  border-bottom-color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
}

.el-menu--horizontal .el-menu-item:not(.is-active):hover {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.05);
}

/* 用户信息样式调整 */
.flex.items-center .el-dropdown span {
  color: #ffffff;
}

/* 过渡动画 */
.sidebar-transition {
  transition: width 0.3s;
}

/* 菜单图标按钮 */
.menu-icon-button {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s;
}

.menu-icon-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 气泡菜单样式 */
.menu-panel {
  padding: 12px;
}

.menu-panel .menu-item {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #222222;
  text-align: center;
  border-radius: 4px;
  transition: all 0.3s;
  background: rgba(2, 119, 253, 0.1);
  border: 1px solid rgba(2, 119, 253, 0.5);
}

.menu-panel .menu-item:hover {
  background: rgba(2, 119, 253, 0.2);
}

.menu-panel .menu-item.active {
  background: #0277FD;
  border: 1px solid rgba(2, 119, 253, 0.7);
  color: #FFFFFF;
  font-weight: 400;
}

/* 二级菜单样式 */
.secondary-menu {
  height: 100%;
}

.secondary-menu-item {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  transition: all 0.3s;
  height: 32px;
  display: flex;
  align-items: center;
  position: relative;
  padding-bottom: 4px;
}

.secondary-menu-item:hover {
  color: #ffffff;
}

.secondary-menu-item.active {
  color: #ffffff;
  position: relative;
}

.secondary-menu-item.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 85%;
  height: 4px;
  background: #FFB500;
}

.mis-gis-main {
  position: absolute;
  height: 83vh;
  margin-right: 15px;
  margin-bottom: 15px;
  /*overflow: hidden;*/

  .mis-gis-wrapper {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .mis-gis-Dialog {
    z-index: 9999;
  }

  .custom-height {
    position: relative;
    width: 100%;
    height: calc(100% - 75px); /* 减去标签页高度 */
    top: 75px;
  }
}

/* 自定义侧边栏宽度 */
.sidebar-container {
  transition: width 0.3s;
  background-color: #FFFFFF;
}

.sidebar-container.expanded {
  width: 240px;
  /* 修改为240px */
}

.sidebar-container.collapsed {
  width: 64px;
}

/* 主内容区样式 */
.main-content {
  background-color: #EEF1F5;
  /* margin-right: 16px; */
  margin-bottom: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 面包屑容器 */
.bread-crumb-container {
  background-color: #EEF1F5;
  border-bottom: 1px solid #E4E7ED;
}

/* 内容区容器 */
.content-container {
  padding: 16px;
  overflow-y: auto;
  background-color: #EEF1F5;
}

/* 修改TagsView样式，将在全局CSS文件中添加或在组件中直接修改 */
:deep(.tags-view-container .tags-view-item.active) {
  background-color: rgba(2, 119, 253, 0.15) !important;
  border-color: rgba(2, 119, 253, 0.3) !important;
  color: #0277FD !important;
}
</style>