<template>
  <div class="heating-event-analysis-container" v-loading="loading" element-loading-text="数据加载中...">
    <!-- 日期筛选区域 -->
    <div class="filter-section">
      <div class="date-filter">
        <label class="filter-label">日期：</label>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange"
          class="date-picker" />
        <el-button type="primary" :class="{ active: quickDateType === 'recent7' }" @click="setQuickDate('recent7')"
          class="quick-btn">
          近7日
        </el-button>
        <el-button type="primary" :class="{ active: quickDateType === 'recent30' }" @click="setQuickDate('recent30')"
          class="quick-btn">
          最近30天
        </el-button>
        <el-button type="success" @click="exportPage" class="export-btn" :loading="exportLoading">
          导出
        </el-button>
      </div>
    </div>

    <!-- 内容区域 - 用于截图 -->
    <div ref="captureRef" class="capture-content">
      <!-- 统计卡片区域 -->
      <div class="statistics-section">
        <!-- 事件数量统计 -->
        <div class="stats-row">
          <!-- 左侧圆形统计卡片 -->
          <div class="circle-stats">
            <div class="circle-card">
              <div class="circle-number">{{ eventStatistics.totalCount || 0 }}</div>
              <div class="circle-label">事件总数</div>
              <div class="circle-trend">
                <span class="trend-label">同比</span>
                <span :class="getTrendClass(eventStatistics.yoyAnalysisTrend)">
                  {{ getTrendIcon(eventStatistics.yoyAnalysisTrend) }} {{ eventStatistics.yoyAnalysis || '0%' }}
                </span>
              </div>
            </div>

            <div class="circle-card handled">
              <div class="circle-number">{{ eventStatistics.handledCount || 0 }}</div>
              <div class="circle-label">已处置</div>
            </div>

            <div class="circle-card unhandled">
              <div class="circle-number">{{ eventStatistics.unhandledCount || 0 }}</div>
              <div class="circle-label">未处置</div>
            </div>
          </div>

          <!-- 右侧事件等级统计 -->
          <div class="level-stats">
            <div 
              v-for="(item, index) in levelStatistics"
              :key="index"
              :class="['level-card', getLevelClass(item.eventLevel)]"
            >
              <div class="level-header">
                <h4>{{ item.eventLevelName || '未知等级' }}</h4>
              </div>
              <div class="level-content">
                <div class="level-number">{{ item.count || 0 }}</div>
                <div class="level-trend">
                  <span class="trend-label">同比</span>
                  <span :class="getTrendClass(item.yoyAnalysisTrend)">
                    {{ getTrendIcon(item.yoyAnalysisTrend) }} {{ item.yoyAnalysis || '0%' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <!-- 事件趋势图 -->
        <div class="chart-card trend-chart">
          <div class="chart-header">
            <h3>事件趋势分析</h3>
          </div>
          <div class="chart-content">
            <div ref="trendChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 事件分类统计图 -->
        <div class="chart-card type-chart">
          <div class="chart-header">
            <h3>事件分类</h3>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color total"></span>
                总数
              </span>
              <span class="legend-item">
                <span class="legend-color handled"></span>
                已处置
              </span>
              <span class="legend-item">
                <span class="legend-color rate-line"></span>
                处置率
              </span>
            </div>
          </div>
          <div class="chart-content">
            <div ref="typeChartRef" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="tables-section">
        <!-- 企业事件表格 -->
        <div class="table-card">
          <div class="table-header">
            <h3>企业事件</h3>
          </div>
          <div class="table-content">
            <el-table :data="enterpriseTableData" stripe class="enterprise-table" v-loading="enterpriseLoading">
              <el-table-column prop="index" label="排序" width="60" align="center">
                <template #default="{ $index }">
                  <span class="rank-number">{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="enterpriseName" label="企业名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="eventCount" label="事件总数" width="100" align="center" />
              <el-table-column prop="handledCount" label="已处置" width="100" align="center" />
              <el-table-column label="处置完成率" width="120" align="center">
                <template #default="{ row }">
                  <div class="completion-rate-cell">
                    <span class="completion-rate">{{ row.handledRate || '0%' }}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import moment from 'moment'
import html2canvas from 'html2canvas'
import {
  getHeatEventStatistics,
  getHeatEventTypeStatistics,
  getHeatEventTrendStatistics,
  getHeatEventEnterpriseStatistics
} from '@/api/heating'

// 供热事件态势数据分析组件
// TODO: 实现具体功能

// 响应式数据
const dateRange = ref([])
const quickDateType = ref('recent7')
const loading = ref(false)
const enterpriseLoading = ref(false)
const exportLoading = ref(false)

// 统计数据
const eventStatistics = reactive({
  totalCount: 0,
  handledCount: 0,
  unhandledCount: 0,
  yoyAnalysis: '0%',
  yoyAnalysisTrend: 'up'
})

const levelStatistics = ref([])
const enterpriseTableData = ref([])

// 图表引用
const trendChartRef = ref(null)
const typeChartRef = ref(null)
const captureRef = ref(null)
let trendChart = null
let typeChart = null

// 方法
const getTrendClass = (trend) => {
  if (trend === 'up') return 'trend-up'
  if (trend === 'down') return 'trend-down'
  return 'trend-stable'
}

const getTrendIcon = (trend) => {
  if (trend === 'up') return '↑'
  if (trend === 'down') return '↓'
  return ''
}

const getLevelClass = (eventLevel) => {
  // 根据事件等级返回对应的样式类
  if (eventLevel === '2003001' || eventLevel === 2003001) return 'level-major'
  if (eventLevel === '2003002' || eventLevel === 2003002) return 'level-important'
  if (eventLevel === '2003003' || eventLevel === 2003003) return 'level-large'
  if (eventLevel === '2003004' || eventLevel === 2003004) return 'level-general'
  return 'level-unknown'
}

const setQuickDate = (type) => {
  quickDateType.value = type
  const today = moment()

  if (type === 'recent7') {
    dateRange.value = [
      today.clone().subtract(6, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  } else if (type === 'recent30') {
    dateRange.value = [
      today.clone().subtract(29, 'days').startOf('day').format('YYYY-MM-DD'),
      today.clone().endOf('day').format('YYYY-MM-DD')
    ]
  }

  loadAllData()
}

const handleDateChange = () => {
  quickDateType.value = ''
  loadAllData()
}

const getDateParams = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    return {}
  }
  return {
    startDate: dateRange.value[0],
    endDate: dateRange.value[1]
  }
}

// 加载事件统计数据
const loadEventStatistics = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatEventStatistics(params)
    if (response.code === 200 && response.data) {
      Object.assign(eventStatistics, response.data)
      // 处理事件等级统计数据
      if (response.data.eventLevelStatistics && Array.isArray(response.data.eventLevelStatistics)) {
        levelStatistics.value = response.data.eventLevelStatistics
      }
    }
  } catch (error) {
    console.error('加载事件统计数据失败:', error)
    ElMessage.error('加载事件统计数据失败')
  }
}

// 加载趋势图表数据
const loadTrendChart = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatEventTrendStatistics(params)
    if (response.code === 200 && response.data) {
      renderTrendChart(response.data)
    }
  } catch (error) {
    console.error('加载趋势图表数据失败:', error)
    ElMessage.error('加载趋势图表数据失败')
  }
}

// 加载事件分类图表数据
const loadTypeChart = async () => {
  try {
    const params = getDateParams()
    const response = await getHeatEventTypeStatistics(params)
    if (response.code === 200 && response.data) {
      renderTypeChart(response.data)
    }
  } catch (error) {
    console.error('加载事件分类图表数据失败:', error)
    ElMessage.error('加载事件分类图表数据失败')
  }
}

// 加载企业表格数据
const loadEnterpriseTable = async () => {
  try {
    enterpriseLoading.value = true
    const params = getDateParams()
    const response = await getHeatEventEnterpriseStatistics(1, 10, params)
    if (response.code === 200 && response.data && response.data.records) {
      enterpriseTableData.value = response.data.records
    }
  } catch (error) {
    console.error('加载企业表格数据失败:', error)
    ElMessage.error('加载企业表格数据失败')
  } finally {
    enterpriseLoading.value = false
  }
}

// 渲染趋势图表
const renderTrendChart = (data) => {
  if (!trendChart) {
    trendChart = echarts.init(trendChartRef.value)
  }

  const dates = data.map(item => moment(item.date).format('MM/DD'))
  const counts = data.map(item => item.totalCount || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f5f5'
        }
      }
    },
    series: [
      {
        name: '事件总数',
        type: 'line',
        data: counts,
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  trendChart.setOption(option)
}

// 渲染事件分类图表
const renderTypeChart = (data) => {
  if (!typeChart) {
    typeChart = echarts.init(typeChartRef.value)
  }

  const categories = data.map(item => item.eventTypeName || '未知类型')
  const totalData = data.map(item => item.totalCount || 0)
  const handledData = data.map(item => item.handledCount || 0)
  const rateData = data.map(item => parseFloat(item.handledRate) || 0)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        lineStyle: {
          color: '#e6e6e6'
        }
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5'
          }
        }
      },
      {
        type: 'value',
        name: '处置率(%)',
        position: 'right',
        axisLine: {
          lineStyle: {
            color: '#e6e6e6'
          }
        },
        axisLabel: {
          color: '#666',
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '总数',
        type: 'bar',
        data: totalData,
        itemStyle: {
          color: '#5dade2'
        },
        barWidth: '20%'
      },
      {
        name: '已处置',
        type: 'bar',
        data: handledData,
        itemStyle: {
          color: '#26de81'
        },
        barWidth: '20%'
      },
      {
        name: '处置率',
        type: 'line',
        yAxisIndex: 1,
        data: rateData,
        lineStyle: {
          color: '#ff6b6b'
        },
        itemStyle: {
          color: '#ff6b6b'
        }
      }
    ]
  }

  typeChart.setOption(option)
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value)
  }
  if (typeChartRef.value) {
    typeChart = echarts.init(typeChartRef.value)
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    trendChart?.resize()
    typeChart?.resize()
  })
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadEventStatistics(),
      loadTrendChart(),
      loadTypeChart(),
      loadEnterpriseTable()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 设置默认日期为近7天
  setQuickDate('recent7')

  // 初始化图表
  await initCharts()

  // 加载数据
  await loadAllData()
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (trendChart) {
    trendChart.dispose()
    trendChart = null
  }
  if (typeChart) {
    typeChart.dispose()
    typeChart = null
  }
  window.removeEventListener('resize', () => {
    trendChart?.resize()
    typeChart?.resize()
  })
})

// 页面截图导出功能
const exportPage = async () => {
  if (!captureRef.value) {
    ElMessage.error('页面元素未准备就绪')
    return
  }

  exportLoading.value = true

  try {
    const canvas = await html2canvas(captureRef.value, {
      backgroundColor: '#f5f7fa',
      scale: 2, // 提高清晰度
      useCORS: true,
      allowTaint: false
    })

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `供热事件态势数据分析_${moment().format('YYYY-MM-DD_HH-mm-ss')}.png`
    link.href = canvas.toDataURL('image/png')
    link.click()

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}
</script>

<style scoped>
.heating-event-analysis-container {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 280px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 筛选区域 */
.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.date-filter {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-label {
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.date-picker {
  width: 300px;
}

.quick-btn {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.quick-btn.active {
  background: #409eff;
  color: white;
}

.export-btn {
  margin-left: auto;
}

/* 统计卡片区域 */
.statistics-section {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 20px;
}

/* 圆形统计卡片 */
.circle-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 0 0 200px;
}

.circle-card {
  background: white;
  border-radius: 50%;
  width: 180px;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  color: white;
}

.circle-card.handled {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.circle-card.unhandled {
  background: linear-gradient(135deg, #ff7875 0%, #ff9c6e 100%);
}

.circle-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.circle-label {
  font-size: 16px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.circle-trend {
  font-size: 12px;
  text-align: center;
  opacity: 0.8;
}

.trend-label {
  margin-right: 5px;
}

.trend-up {
  color: #ff4d4f;
}

.trend-down {
  color: #52c41a;
}

.trend-stable {
  color: #8c8c8c;
}

/* 事件等级统计卡片 */
.level-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  flex: 1;
  background: #e4e7ed;
}

.level-card {
  background: white;
  padding: 20px;
  color: white;
}

.level-card.level-major {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.level-card.level-important {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

.level-card.level-large {
  background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  color: #333;
}

.level-card.level-general {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.level-card.level-unknown {
  background: linear-gradient(135deg, #8c8c8c 0%, #bfbfbf 100%);
}

.level-header h4 {
  font-size: 14px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.level-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.level-trend {
  font-size: 12px;
  opacity: 0.8;
}

/* 图表区域 */
.charts-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.total {
  background: #5dade2;
}

.legend-color.handled {
  background: #26de81;
}

.legend-color.rate-line {
  background: #ff6b6b;
}

.chart-content .chart-container {
  height: 300px;
  min-height: 250px;
}

/* 表格区域 */
.tables-section {
  display: flex;
  gap: 20px;
}

.table-card {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 20px 20px 0 20px;
  flex-shrink: 0;
}

.table-header h3 {
  font-size: 18px;
  color: #303133;
  margin: 0;
}

.table-content {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.rank-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background: #f0f2f5;
  color: #606266;
  font-weight: bold;
}

.completion-rate-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.completion-rate {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  background: #52c41a;
  color: white;
  min-width: 30px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .charts-section {
    flex-direction: column;
  }

  .chart-content .chart-container {
    height: 280px;
  }
}

@media (max-width: 1200px) {
  .stats-row {
    flex-direction: column;
  }

  .circle-stats {
    flex-direction: row;
    justify-content: center;
    flex: none;
  }

  .level-stats {
    grid-template-columns: repeat(4, 1fr);
  }

  .chart-content .chart-container {
    height: 260px;
  }
}

@media (max-height: 1050px) {
  .heating-event-analysis-container {
    padding: 15px;
    background-color: #f5f7fa;
    min-height: calc(100vh - 300px);
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .chart-content .chart-container {
    height: 240px;
  }
}

@media (max-height: 800px) {
  .heating-event-analysis-container {
    padding: 10px;
  }

  .chart-content .chart-container {
    height: 220px;
  }

  .table-card {
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .stats-row {
    flex-direction: column;
  }

  .circle-stats {
    flex-direction: column;
    align-items: center;
  }

  .level-stats {
    grid-template-columns: 1fr;
  }

  .date-filter {
    flex-wrap: wrap;
  }

  .chart-legend {
    flex-direction: column;
    gap: 10px;
  }

  .chart-content .chart-container {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .heating-event-analysis-container {
    padding: 10px;
  }

  .date-picker {
    width: 100%;
  }

  .quick-btn {
    flex: 1;
  }

  .chart-content .chart-container {
    height: 180px;
  }

  .circle-card {
    width: 150px;
    height: 150px;
  }

  .circle-number {
    font-size: 24px;
  }
}

/* 表格优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 截图内容区域 */
.capture-content {
  background-color: #f5f7fa;
}
</style> 