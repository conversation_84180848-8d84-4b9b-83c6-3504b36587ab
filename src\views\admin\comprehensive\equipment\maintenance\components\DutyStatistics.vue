<template>
  <div class="duty-statistics-container">
    <!-- 查询条件区域 -->
    <div class="statistics-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">起止时间:</span>
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="time-picker"
            @change="handleTimeChange"
          />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 统计内容区域 -->
    <div class="statistics-content">
      <!-- 左侧：值班人员天数统计 -->
      <div class="statistics-left">
        <div class="statistics-card">
          <div class="card-header">
            <h3>值班人员天数统计</h3>
          </div>
          <div class="card-content">
            <div ref="personChartRef" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- 右侧：单位值班人数统计 -->
      <div class="statistics-right">
        <div class="statistics-card">
          <div class="card-header">
            <h3>单位值班人数统计</h3>
          </div>
          <div class="card-content">
            <div class="unit-table-container">
              <el-table 
                :data="unitStatisticsData" 
                style="width: 100%" 
                :header-cell-style="headerCellStyle"
                :row-class-name="tableRowClassName"
                empty-text="暂无数据"
                max-height="400"
              >
                <el-table-column label="序号" width="80" align="center">
                  <template #default="{ $index }">
                    {{ $index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="单位名称" min-width="120" />
                <el-table-column prop="count" label="值班人数" width="100" align="center" />
                <el-table-column prop="rate" label="占比" width="100" align="center">
                  <template #default="{ row }">
                    <span class="rate-text">{{ row.rate || '0%' }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { getDutyUnitStatistics, getDutyPersonStatistics } from '@/api/comprehensive'
import moment from 'moment'

// 时间范围
const timeRange = ref([])

// 图表实例
const personChartRef = ref(null)
let personChart = null

// 统计数据
const unitStatisticsData = ref([])
const personStatisticsData = ref([])

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 处理时间变化
const handleTimeChange = (value) => {
  if (value && value.length === 2) {
    fetchPersonStatistics()
  }
}

// 处理查询
const handleSearch = () => {
  fetchPersonStatistics()
}

// 处理重置
const handleReset = () => {
  timeRange.value = []
  // 重置为默认时间范围（最近30天）
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
  timeRange.value = [startTime, endTime]
  fetchPersonStatistics()
}

// 获取单位值班人数统计
const fetchUnitStatistics = async () => {
  try {
    const res = await getDutyUnitStatistics()
    if (res && res.code === 200) {
      unitStatisticsData.value = res.data || []
    }
  } catch (error) {
    console.error('获取单位统计数据失败:', error)
    ElMessage.error('获取单位统计数据失败')
  }
}

// 获取值班人员天数统计
const fetchPersonStatistics = async () => {
  try {
    let params = {}
    if (timeRange.value && timeRange.value.length === 2) {
      params = {
        startTime: timeRange.value[0],
        endTime: timeRange.value[1]
      }
    }
    
    const res = await getDutyPersonStatistics(params)
    if (res && res.code === 200) {
      personStatisticsData.value = res.data || []
      // 等待DOM更新完成后再渲染图表
      await nextTick()
      // 再次确保DOM完全渲染
      setTimeout(() => {
        renderPersonChart()
      }, 100)
    }
  } catch (error) {
    console.error('获取人员统计数据失败:', error)
    ElMessage.error('获取人员统计数据失败')
  }
}

// 渲染人员统计图表
const renderPersonChart = () => {
  if (!personChartRef.value || !personStatisticsData.value.length) return

  // 确保容器有尺寸
  const container = personChartRef.value
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    // 如果容器尺寸为0，延迟渲染
    setTimeout(() => {
      renderPersonChart()
    }, 100)
    return
  }

  // 销毁已存在的图表实例
  if (personChart) {
    personChart.dispose()
  }

  // 创建新的图表实例
  personChart = echarts.init(personChartRef.value)

  // 根据数据量动态调整柱子宽度和间距
  const dataLength = personStatisticsData.value.length
  let barWidth = '60%'
  let barMaxWidth = 60
  
  if (dataLength > 10) {
    barWidth = '80%'
    barMaxWidth = 40
  }
  if (dataLength > 15) {
    barWidth = '90%'
    barMaxWidth = 30
  }
  if (dataLength > 20) {
    barWidth = '95%'
    barMaxWidth = 25
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const data = params[0]
        return `${data.name}<br/>值班天数: ${data.value}天`
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: dataLength > 8 ? '20%' : '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: personStatisticsData.value.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: dataLength > 8 ? 45 : 30,
        fontSize: dataLength > 15 ? 10 : 12,
        color: '#666',
        margin: 8
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      name: '天',
      nameTextStyle: {
        color: '#666',
        fontSize: 12
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      splitLine: {
        lineStyle: {
          color: '#F5F7FA'
        }
      }
    },
    series: [
      {
        name: '值班天数',
        type: 'bar',
        data: personStatisticsData.value.map(item => item.count),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#79BBFF' }
          ]),
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#337ECC' },
              { offset: 1, color: '#6BB6FF' }
            ])
          }
        },
        barWidth: barWidth,
        barMaxWidth: barMaxWidth,
        barCategoryGap: '20%'
      }
    ]
  }

  personChart.setOption(option)

  // 确保图表正确渲染
  setTimeout(() => {
    if (personChart) {
      personChart.resize()
    }
  }, 50)

  // 监听窗口大小变化
  const handleResize = () => {
    if (personChart) {
      personChart.resize()
    }
  }
  window.addEventListener('resize', handleResize)
}

// 初始化默认时间范围
const initDefaultTimeRange = () => {
  const endTime = moment().format('YYYY-MM-DD HH:mm:ss')
  const startTime = moment().subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss')
  timeRange.value = [startTime, endTime]
}

// 组件挂载
onMounted(async () => {
  try {
    // 初始化默认时间范围
    initDefaultTimeRange()
    
    // 先获取单位统计数据
    await fetchUnitStatistics()
    
    // 等待DOM完全渲染后再获取人员统计数据
    await nextTick()
    setTimeout(async () => {
      await fetchPersonStatistics()
    }, 200)
  } catch (error) {
    console.error('初始化统计数据失败:', error)
    ElMessage.error('初始化统计数据失败')
  }
})

// 组件卸载
onUnmounted(() => {
  if (personChart) {
    personChart.dispose()
    personChart = null
  }
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.duty-statistics-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.statistics-search {
  width: 100%;
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.time-picker {
  width: 350px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 统计内容区域 */
.statistics-content {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
}

.statistics-left {
  flex: 1;
  min-width: 0;
}

.statistics-right {
  width: 600px;
  flex-shrink: 0;
}

/* 统计卡片样式 */
.statistics-card {
  height: 100%;
  background: white;
  border-radius: 8px;
  border: 1px solid #E4E7ED;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid #E4E7ED;
  background: #F8F9FA;
  border-radius: 8px 8px 0 0;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.card-content {
  flex: 1;
  padding: 20px;
  min-height: 0;
}

/* 图表容器 */
.chart-container {
  width: 100%;
  height: 400px;
  min-height: 300px;
}

/* 表格容器 */
.unit-table-container {
  height: 100%;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.rate-text {
  color: #409EFF;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .statistics-content {
    flex-direction: column;
  }
  
  .statistics-right {
    width: 100%;
  }
  
  .chart-container {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .duty-statistics-container {
    padding: 12px;
  }
  
  .search-form {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .form-item {
    margin-right: 0;
    width: 100%;
  }
  
  .time-picker {
    width: 100%;
  }
  
  .statistics-content {
    gap: 16px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .chart-container {
    height: 250px;
  }
}
</style> 