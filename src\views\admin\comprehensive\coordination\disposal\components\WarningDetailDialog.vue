<template>
  <el-dialog
    v-model="dialogVisible"
    title="查看"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="warning-detail-dialog"
  >
    <div class="warning-detail-content">
      <!-- 预警信息部分 -->
      <div class="detail-section">
        <h3 class="section-title">预警信息:</h3>
        <div class="detail-table">
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">所属行业:</span>
              <span class="detail-value">{{ detailData.relatedBusinessName || '燃气' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">预警类型:</span>
              <span class="detail-value">{{ detailData.warningTypeName || '燃气泄漏爆炸一级预警' }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">预警标题:</span>
              <span class="detail-value">{{ detailData.warningTitle || '燃气泄漏爆炸一级预警' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">预警级别:</span>
              <span class="detail-value">{{ detailData.warningLevelName || '一级预警' }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">预警来源:</span>
              <span class="detail-value">{{ detailData.warningSource || '设备报警' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">报警源:</span>
              <span class="detail-value">{{ detailData.warningCode || '320230927001' }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">报警源描述:</span>
              <span class="detail-value">{{ detailData.warningDesc || '2023-9-27 14:00 郑州市承天大道****附近 可燃气体监测 发生（二级报警）' }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">地理位置:</span>
              <span class="detail-value">{{ detailData.warningLocation || '郑州市承天大道****附近' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">地图定位:</span>
              <span class="detail-value">
                <el-button type="primary" link @click="handleViewLocation" v-if="detailData.longitude && detailData.latitude">查看</el-button>
                <span v-else>无</span>
              </span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">发布单位:</span>
              <span class="detail-value">{{ detailData.publishUnitName || '郑州市住建局' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">发布时间:</span>
              <span class="detail-value">{{ detailData.publishTime || '2023-9-27 10:10' }}</span>
            </div>
          </div>
          <div class="detail-row">
            <div class="detail-item">
              <span class="detail-label">相关附件:</span>
              <span class="detail-value">
                <div v-if="fileList.length > 0" class="file-list">
                  <div v-for="(file, index) in fileList" :key="index" class="file-item">
                    <el-icon class="file-icon">
                      <Document v-if="isDocumentFile(file.name)" />
                      <Picture v-else-if="isImageFile(file.name)" />
                      <Files v-else />
                    </el-icon>
                    <span class="file-name">{{ file.name }}</span>
                    <el-button type="primary" link size="small" @click="handleFileDownload(file)">下载</el-button>
                  </div>
                </div>
                <span v-else>无</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 处置单位部分 -->
      <div class="detail-section">
        <h3 class="section-title">处置单位:</h3>
        <div class="detail-table">
          <div class="detail-row">
            <div class="detail-item full-width">
              <span class="detail-value">{{ formatDealUnits(detailData.dealUnitAndUserList) || '无' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 推送方式部分 -->
      <div class="detail-section">
        <h3 class="section-title">推送方式:</h3>
        <div class="detail-table">
          <div class="detail-row">
            <div class="detail-item full-width">
              <span class="detail-value">{{ formatPushMethods(detailData) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 处置过程部分 -->
      <div class="detail-section">
        <h3 class="section-title">处置过程</h3>
        <div class="timeline-container">
          <el-timeline v-if="timelineData.length > 0">
            <el-timeline-item
              v-for="(item, index) in timelineData"
              :key="index"
              :timestamp="item.dealTime"
              placement="top"
              :type="getTimelineType(item.dealStatus)"
              :icon="getTimelineIcon(item.dealStatus)"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ item.dealStatusName }}</div>
                <div class="timeline-desc" v-if="item.description">{{ item.description }}</div>
                <div class="timeline-handler" v-if="item.handleUserName">
                  <span>处理人：{{ item.handleUserName }}</span>
                  <span v-if="item.handleUnitName">（{{ item.handleUnitName }}）</span>
                </div>
                <div class="timeline-remark" v-if="item.remark">备注：{{ item.remark }}</div>
                <div class="timeline-status" v-if="item.handleStatusName">
                  <span>处置状态：{{ item.handleStatusName }}</span>
                </div>
                <!-- 处置图片 -->
                <div class="timeline-images" v-if="item.beforePicUrls || item.afterPicUrls">
                  <div v-if="item.beforePicUrls" class="image-group">
                    <div class="image-label">处置前图片：</div>
                    <div class="image-list">
                      <img
                        v-for="(img, imgIndex) in parseImageUrls(item.beforePicUrls)"
                        :key="imgIndex"
                        :src="img"
                        class="timeline-image"
                        @click="handleImagePreview(img)"
                      />
                    </div>
                  </div>
                  <div v-if="item.afterPicUrls" class="image-group">
                    <div class="image-label">处置后图片：</div>
                    <div class="image-list">
                      <img
                        v-for="(img, imgIndex) in parseImageUrls(item.afterPicUrls)"
                        :key="imgIndex"
                        :src="img"
                        class="timeline-image"
                        @click="handleImagePreview(img)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="no-timeline">
            <el-empty description="暂无处置记录" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>

    <!-- 图片预览 -->
    <el-dialog v-model="imagePreviewVisible" :show-close="false" :width="800" class="image-preview-dialog">
      <img :src="currentPreviewImage" style="width: 100%; max-height: 80vh; object-fit: contain;" />
      <template #footer>
        <el-button @click="handleImagePreviewClose">关闭</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Picture, Files } from '@element-plus/icons-vue'
import { getWarningDealList, getWarningDealStatusList } from '@/api/comprehensive'
import { misPosition } from '@/hooks/gishooks'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 详情数据
const detailData = ref({})
const fileList = ref([])
const timelineData = ref([])

// 图片预览
const imagePreviewVisible = ref(false)
const currentPreviewImage = ref('')

// 监听props.data变化
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    detailData.value = newVal
    
    // 处理文件列表
    if (newVal.fileUrls) {
      const urls = newVal.fileUrls.split(',').filter(url => url.trim())
      fileList.value = urls.map((url, index) => ({
        name: `attachment_${index + 1}${getFileExtension(url)}`,
        url: url.trim()
      }))
    } else {
      fileList.value = []
    }
    
    // 获取处置流程时间线
    if (newVal.id) {
      fetchTimelineData(newVal.id)
    }
  }
}, { immediate: true, deep: true })

// 获取文件扩展名
const getFileExtension = (url) => {
  const match = url.match(/\.[^.]*$/)
  return match ? match[0] : ''
}

// 判断是否为文档文件
const isDocumentFile = (fileName) => {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.rar', '.zip']
  return docExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

// 格式化处置单位
const formatDealUnits = (dealUnitAndUserList) => {
  if (!dealUnitAndUserList || dealUnitAndUserList.length === 0) {
    return ''
  }
  
  const grouped = {}
  dealUnitAndUserList.forEach(item => {
    if (!grouped[item.dealUnitName]) {
      grouped[item.dealUnitName] = []
    }
    if (item.dealUserName) {
      grouped[item.dealUnitName].push(item.dealUserName)
    }
  })
  
  return Object.entries(grouped)
    .filter(([unit, users]) => unit && users.length > 0)
    .map(([unit, users]) => `${unit}-${users.join('、')}`)
    .join('，') || ''
}

// 格式化推送方式
const formatPushMethods = (data) => {
  const methods = []
  if (data.notifySms) methods.push('短信')
  if (data.notifyApp) methods.push('APP')
  return methods.length > 0 ? methods.join('，') : '无'
}

// 获取时间线类型
const getTimelineType = (status) => {
  const typeMap = {
    7001601: 'primary', // 发布预警
    7001602: 'warning', // 处置预警
    7001603: 'success', // 处置完成
    7001604: 'info'     // 解除预警
  }
  return typeMap[status] || 'primary'
}

// 获取时间线图标
const getTimelineIcon = (status) => {
  const iconMap = {
    7001601: 'Warning',    // 发布预警
    7001602: 'Tools',      // 处置预警
    7001603: 'Check',      // 处置完成
    7001604: 'CircleCheck' // 解除预警
  }
  return iconMap[status] || 'InfoFilled'
}

// 解析图片URL
const parseImageUrls = (urlString) => {
  if (!urlString) return []
  return urlString.split(',').map(url => url.trim()).filter(url => url)
}

// 处理查看位置
const handleViewLocation = () => {
  if (detailData.value.longitude && detailData.value.latitude) {
    misPosition.value = {
      longitude: detailData.value.longitude,
      latitude: detailData.value.latitude
    }
    ElMessage.success('位置已在地图上显示')
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 处理文件下载
const handleFileDownload = (file) => {
  if (file.url) {
    const link = document.createElement('a')
    link.href = file.url
    link.download = file.name
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 处理图片预览
const handleImagePreview = (imageUrl) => {
  currentPreviewImage.value = imageUrl
  imagePreviewVisible.value = true
}

// 关闭图片预览
const handleImagePreviewClose = () => {
  imagePreviewVisible.value = false
  currentPreviewImage.value = ''
}

// 获取处置流程时间线数据
const fetchTimelineData = async (warningId) => {
  try {
    // 先获取处置列表
    const dealRes = await getWarningDealList({ warningId })
    if (dealRes && dealRes.code === 200 && dealRes.data && dealRes.data.length > 0) {
      // 获取第一个处置记录的详细状态
      const dealId = dealRes.data[0].id
      const statusRes = await getWarningDealStatusList({ dealId })
      if (statusRes && statusRes.code === 200) {
        timelineData.value = statusRes.data || []
      } else {
        timelineData.value = []
      }
    } else {
      timelineData.value = []
    }
  } catch (error) {
    console.error('获取处置流程失败:', error)
    timelineData.value = []
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  detailData.value = {}
  fileList.value = []
  timelineData.value = []
}
</script>

<style scoped>
.warning-detail-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

.warning-detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding-bottom: 8px;
  border-bottom: 2px solid #0277FD;
}

.detail-table {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  gap: 24px;
}

.detail-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.detail-item.full-width {
  flex: none;
  width: 100%;
}

.detail-label {
  font-weight: 500;
  color: #666;
  white-space: nowrap;
  min-width: 80px;
}

.detail-value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.file-icon {
  font-size: 16px;
  color: #606266;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.timeline-container {
  margin-top: 16px;
}

.timeline-content {
  padding: 12px 0;
}

.timeline-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.timeline-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.timeline-handler {
  color: #0277FD;
  font-size: 14px;
  margin-bottom: 8px;
}

.timeline-remark {
  color: #999;
  font-size: 14px;
  margin-bottom: 8px;
}

.timeline-status {
  color: #f56c6c;
  font-size: 14px;
  margin-bottom: 8px;
}

.timeline-images {
  margin-top: 12px;
}

.image-group {
  margin-bottom: 12px;
}

.image-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.timeline-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: transform 0.2s;
}

.timeline-image:hover {
  transform: scale(1.05);
}

.no-timeline {
  text-align: center;
  padding: 40px 0;
}

:deep(.el-timeline-item__timestamp) {
  color: #666;
  font-size: 14px;
}

:deep(.el-timeline-item__wrapper) {
  position: relative;
  padding-left: 28px;
  top: -3px;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
}

:deep(.el-timeline-item__node) {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  left: -6px;
  top: 0;
}

:deep(.el-timeline-item__node--primary) {
  background-color: #0277FD;
  color: #fff;
}

:deep(.el-timeline-item__node--success) {
  background-color: #67c23a;
  color: #fff;
}

:deep(.el-timeline-item__node--warning) {
  background-color: #e6a23c;
  color: #fff;
}

:deep(.el-timeline-item__node--info) {
  background-color: #909399;
  color: #fff;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .detail-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-label {
    min-width: auto;
    margin-bottom: 4px;
  }
  
  .image-list {
    justify-content: flex-start;
  }
  
  .timeline-image {
    width: 60px;
    height: 60px;
  }
}
</style> 