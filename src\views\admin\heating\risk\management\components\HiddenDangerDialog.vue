<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="hidden-danger-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      :disabled="mode === 'view'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患来源" prop="dangerSourceType" required>
            <el-select v-model="formData.dangerSourceType" placeholder="请选择" style="width: 100%" @change="handleSourceChange">
              <el-option
                v-for="item in sourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="隐患描述" prop="dangerDesc" required>
            <el-input v-model="formData.dangerDesc" placeholder="请输入隐患描述" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患类型" prop="dangerType" required>
            <el-select v-model="formData.dangerType" placeholder="请选择" style="width: 100%" @change="handleTypeChange">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="隐患等级" prop="dangerLevel" required>
            <el-select v-model="formData.dangerLevel" placeholder="请选择" style="width: 100%" @change="handleLevelChange">
              <el-option
                v-for="item in levelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="隐患对象" prop="dangerTarget" required>
            <el-select v-model="formData.dangerTarget" placeholder="请选择" style="width: 100%" @change="handleTargetChange">
              <el-option
                v-for="item in targetOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="整改期限" prop="rectificationDeadline" required>
            <el-date-picker
              v-model="formData.rectificationDeadline"
              type="datetime"
              placeholder="请选择整改期限"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="位置">
            <div class="flex items-center">
              <el-cascader
                v-model="formData.town"
                :options="areaOptions"
                :props="{
                  value: 'code',
                  label: 'name',
                  children: 'children'
                }"
                placeholder="请选择所属区域"
                class="mr-2 w-full"
                @change="handleAreaChange"
              />
              <el-input v-model="formData.address" placeholder="输入详细地址" />
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="定位">
            <div class="flex items-center">
              <el-input v-model="formData.longitude" placeholder="经度" class="mr-2 w-32" />
              <el-input v-model="formData.latitude" placeholder="纬度" class="w-32" />
              <el-button type="primary" class="ml-2" icon="Location" circle @click="openMapPicker"></el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上报时间" prop="reportTime" required>
            <el-date-picker
              v-model="formData.reportTime"
              type="datetime"
              placeholder="请选择上报时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上报人" prop="reportUser" required>
            <el-input v-model="formData.reportUser" placeholder="请输入上报人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="权属单位" prop="ownershipUnitName" required>
            <el-input v-model="formData.ownershipUnitName" placeholder="请输入权属单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="责任人" prop="responsibleUserName" required>
            <el-input v-model="formData.responsibleUserName" placeholder="请输入责任人" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="隐患图片" prop="picUrls">
            <el-upload
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleFileChange"
              :file-list="fileList"
              list-type="picture-card"
              :limit="9"
              :disabled="mode === 'view'"
              multiple
            >
              <el-icon><Plus /></el-icon>
              <template #tip>
                <div class="el-upload__tip">
                  支持上传jpg、jpeg、png、gif、bmp、JPG、JPEG、GIF、svg、SVG
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" v-if="mode !== 'view'">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { saveHeatingHiddenDanger, updateHeatingHiddenDanger } from '@/api/heating';
import { uploadFile } from '@/api/upload';
import { collectShow } from "@/hooks/gishooks";
import bus from '@/utils/mitt';
import {
  HEATING_HIDDEN_DANGER_SOURCE_OPTIONS,
  HEATING_HIDDEN_DANGER_TYPE_OPTIONS,
  HEATING_HIDDEN_DANGER_LEVEL_OPTIONS,
  HEATING_HIDDEN_DANGER_OBJECT_OPTIONS
} from '@/constants/heating';
import { AREA_OPTIONS } from '@/constants/gas';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add', // 'add', 'edit', 'view'
    validator: (value) => ['add', 'edit', 'view'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref(null);
const fileList = ref([]);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => {
  const titles = {
    add: '新增隐患信息',
    edit: '编辑隐患信息',
    view: '隐患信息详情'
  };
  return titles[props.mode] || '隐患信息';
});

// 下拉选项数据
const sourceOptions = ref(HEATING_HIDDEN_DANGER_SOURCE_OPTIONS);
const typeOptions = ref(HEATING_HIDDEN_DANGER_TYPE_OPTIONS);
const levelOptions = ref(HEATING_HIDDEN_DANGER_LEVEL_OPTIONS);
const targetOptions = ref(HEATING_HIDDEN_DANGER_OBJECT_OPTIONS);
const areaOptions = ref(AREA_OPTIONS);

// 表单数据
const formData = reactive({
  id: '',
  dangerCode: '',
  dangerDesc: '',
  dangerLevel: '',
  dangerLevelName: '',
  dangerObjectId: '',
  dangerObjectName: '',
  dangerSourceName: '',
  dangerSourceType: '',
  dangerStatus: 2002701, // 默认待整改
  dangerStatusName: '待整改',
  dangerTarget: '',
  dangerTargetName: '',
  dangerType: '',
  dangerTypeName: '',
  keyWord: '',
  latitude: '',
  longitude: '',
  ownershipUnit: '',
  ownershipUnitName: '',
  picUrls: '',
  rectificationDeadline: '',
  reportTime: '',
  reportUser: '',
  responsibleUserId: '',
  responsibleUserName: '',
  address: '',
  city: '',
  county: '',
  countyName: '',
  town: '',
  townName: ''
});

// 表单验证规则
const formRules = {
  dangerSourceType: [{ required: true, message: '请选择隐患来源', trigger: 'change' }],
  dangerDesc: [{ required: true, message: '请输入隐患描述', trigger: 'blur' }],
  dangerType: [{ required: true, message: '请选择隐患类型', trigger: 'change' }],
  dangerLevel: [{ required: true, message: '请选择隐患等级', trigger: 'change' }],
  dangerTarget: [{ required: true, message: '请选择隐患对象', trigger: 'change' }],
  rectificationDeadline: [{ required: true, message: '请选择整改期限', trigger: 'change' }],
  reportTime: [{ required: true, message: '请选择上报时间', trigger: 'change' }],
  reportUser: [{ required: true, message: '请输入上报人', trigger: 'blur' }],
  ownershipUnitName: [{ required: true, message: '请输入权属单位', trigger: 'blur' }],
  responsibleUserName: [{ required: true, message: '请输入责任人', trigger: 'blur' }]
};

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'dangerStatus') {
      formData[key] = 2002701;
    } else if (key === 'dangerStatusName') {
      formData[key] = '待整改';
    } else if (typeof formData[key] === 'number') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  fileList.value = [];
};

// 监听props.data变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.keys(formData).forEach(key => {
      if (newVal[key] !== undefined) {
        formData[key] = newVal[key];
      }
    });
    // 处理图片显示
    if (newVal.picUrls) {
      fileList.value = newVal.picUrls.split(',').map((url, index) => ({
        name: `image_${index}`,
        url: url,
        uid: Date.now() + index
      }));
    }
  } else if (props.mode === 'add') {
    resetForm();
  }
}, { immediate: true, deep: true });

// 处理隐患来源变化
const handleSourceChange = (value) => {
  const selected = sourceOptions.value.find(item => item.value === value);
  if (selected) {
    formData.dangerSourceName = selected.label;
  }
};

// 处理隐患类型变化
const handleTypeChange = (value) => {
  const selected = typeOptions.value.find(item => item.value === value);
  if (selected) {
    formData.dangerTypeName = selected.label;
  }
};

// 处理隐患等级变化
const handleLevelChange = (value) => {
  const selected = levelOptions.value.find(item => item.value === value);
  if (selected) {
    formData.dangerLevelName = selected.label;
  }
};

// 处理隐患对象变化
const handleTargetChange = (value) => {
  const selected = targetOptions.value.find(item => item.value === value);
  if (selected) {
    formData.dangerTargetName = selected.label;
  }
};

// 处理区域选择变化
const handleAreaChange = (value) => {
  if (value && value.length > 0) {
    formData.town = value[value.length - 1];
    // 更新区域名称
    const selectedArea = findAreaByCode(areaOptions.value, formData.town);
    if (selectedArea) {
      formData.townName = selectedArea.name;
    }
  }
};

// 根据区域代码查找区域信息
const findAreaByCode = (areas, code) => {
  for (const area of areas) {
    if (area.code === code) {
      return area;
    }
    if (area.children) {
      const found = findAreaByCode(area.children, code);
      if (found) return found;
    }
  }
  return null;
};

// 打开地图选点
const openMapPicker = () => {
  collectShow.value = true;
  bus.off("getCollectLocation", handleCollectLocation);
  bus.on("getCollectLocation", handleCollectLocation);
};

// 地图选点事件处理函数
const handleCollectLocation = (params) => {
  nextTick(() => {
    formData.longitude = params.longitude;
    formData.latitude = params.latitude;
  });
};

// 文件选择变化处理
const handleFileChange = async (file, fileList) => {
  // 检查文件大小
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    ElMessage.error('上传图片大小不能超过 20MB!');
    return;
  }

  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }

  try {
    // 上传文件
    const response = await uploadFile(file.raw);
    if (response.status === 200) {
      const urls = formData.picUrls ? formData.picUrls.split(',') : [];
      urls.push(response.data.url);
      formData.picUrls = urls.join(',');
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    const submitData = { ...formData };
    
    let res;
    if (props.mode === 'add') {
      res = await saveHeatingHiddenDanger(submitData);
    } else if (props.mode === 'edit') {
      res = await updateHeatingHiddenDanger(submitData);
    }

    if (res && res.code === 200) {
      ElMessage.success(props.mode === 'add' ? '新增成功' : '更新成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(res?.msg || (props.mode === 'add' ? '新增失败' : '更新失败'));
    }
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// 组件卸载时清理事件监听
onUnmounted(() => {
  bus.off("getCollectLocation", handleCollectLocation);
});
</script>

<style scoped>
.hidden-danger-dialog {
  font-family: PingFangSC, PingFang SC;
}

:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper),
:deep(.el-select__input) {
  border-radius: 2px;
  height: 36px;
}

:deep(.el-button) {
  border-radius: 2px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background-color: #0277FD;
  border-color: #0277FD;
}

:deep(.el-button--primary:hover) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.w-32 {
  width: 140px;
}

.w-full {
  width: 100%;
}

.upload-demo .el-upload__tip {
  margin-top: 7px;
  color: #999;
  font-size: 12px;
}
</style> 