# 态势数据分级管理功能说明

## 功能概述

态势数据分级管理功能提供供热系统的综合态势分析，包含三个主要分析模块：

1. **供热报警态势数据分析** - 已完成开发
2. **供热事件态势数据分析** - 待开发
3. **供热隐患态势数据分析** - 待开发

## 技术实现

### 主要文件结构
```
src/views/admin/heating/decision/support/
├── level.vue                        # 主页面 - 三个tab的容器
└── components/
    ├── HeatingAlarmAnalysis.vue     # 供热报警态势数据分析（已完成）
    ├── HeatingEventAnalysis.vue     # 供热事件态势数据分析（空页面）
    └── HeatingHazardAnalysis.vue    # 供热隐患态势数据分析（空页面）
```

### API接口

已在 `src/api/heating.js` 中添加以下接口：

1. `getHeatAlarmStatistics` - 获取供热报警统计数据
2. `getHeatAlarmDisposalSituation` - 获取报警处置、误报统计
3. `getHeatAlarmTrendStatistics` - 获取报警趋势分析统计
4. `getHeatAlarmLevelStatistics` - 获取报警等级统计
5. `getHeatAlarmHighFrequencyDevices` - 获取高发报警设备列表
6. `getHeatAlarmEnterpriseStatistics` - 获取企业报警信息统计

### 已完成功能（供热报警态势数据分析）

#### 主要功能特性：
- 📊 报警数量统计（全部、待确认、待处置、处置中、已处置）
- 📈 处置完成率和误报率环形图显示
- ⏱️ 平均处置时长展示
- 📉 报警趋势图表（按时间维度）
- 📊 报警等级统计图表（柱状图+折线图）
- 📋 高发报警设备Top10列表
- 🏢 企业报警统计表格

#### 交互功能：
- 📅 日期筛选（自定义日期范围）
- 🔘 快捷日期选择（近7日、最近30天）
- 📱 响应式布局设计
- 🔄 数据实时刷新

#### 技术特点：
- 使用ECharts图表库进行数据可视化
- 完全响应式设计，支持多种屏幕尺寸
- 优雅的加载状态和错误处理
- 现代化的UI设计，与系统整体风格一致

## 数据格式

### 报警统计数据格式
```javascript
{
  "totalAlarms": 6,
  "pendingConfirm": 5,
  "pendingConfirmRate": "83.33%",
  "pendingHandle": 0,
  "pendingHandleRate": "0%",
  "handling": 1,
  "handlingRate": "16.67%",
  "handled": 0,
  "handledRate": "0%"
}
```

### 趋势数据格式
```javascript
{
  "statistics": [
    {
      "date": "2025-05-23",
      "totalCount": 6,
      "level1Count": 0,
      "level2Count": 5,
      "level3Count": 1
    }
  ]
}
```

## 后续开发计划

1. **供热事件态势数据分析**
   - 实现事件统计分析
   - 事件处置效率分析
   - 事件影响范围评估

2. **供热隐患态势数据分析**
   - 隐患等级分布统计
   - 隐患处理时效分析
   - 风险评估和预警

## 使用说明

1. 进入系统后，导航到 `智慧决策 -> 辅助决策支持 -> 态势数据分级管理`
2. 默认显示供热报警态势数据分析页面
3. 可通过顶部Tab切换不同的分析模块
4. 使用日期筛选器选择分析时间范围
5. 查看各种统计图表和数据表格

## 技术依赖

- Vue 3 Composition API
- Element Plus UI组件库
- ECharts 图表库
- Moment.js 时间处理库
- 响应式CSS布局 