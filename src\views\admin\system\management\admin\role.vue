<template>
  <div class="role-management-container">
    <!-- 搜索区域 -->
    <div class="role-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">角色名称:</span>
          <el-input v-model="formData.name" class="form-input" placeholder="输入角色名称" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="table-header">
      <div class="button-group">
        <el-button type="primary" class="operation-btn" @click="handleAdd">新增角色</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="角色名称" min-width="120" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="{ row }">
            <el-switch 
              v-model="row.state" 
              active-value="0"
              inactive-value="1"
              active-text="正常"
              inactive-text="停用"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="240">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleDetail(row)">查看</el-button>
              <el-button type="primary" link @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click.stop="handlePermissionSetting(row)">权限设置</el-button>
              <el-button 
                type="primary" 
                link 
                @click.stop="handleDelete(row)"
                :disabled="row.state === '0'"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <RoleDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :data="dialogData"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessageBox, ElMessage, ElSwitch } from 'element-plus'
import moment from 'moment'
import { 
  getRolePage, 
  deleteRole,
  updateRole
} from '@/api/system'
import { STATUS_TEXT_MAP } from '@/constants/system'
import { useUserStore } from '@/stores/user'
import RoleDialog from './components/RoleDialog.vue'

// 获取用户store
const userStore = useUserStore()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 表单数据
const formData = ref({
  name: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit' | 'view' | 'permission'
const dialogData = ref({})

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchRoleData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    name: ''
  }
  currentPage.value = 1
  fetchRoleData()
}

// 获取角色分页数据
const fetchRoleData = async () => {
  try {
    const params = {
      name: formData.value.name
    }
    
    const res = await getRolePage(currentPage.value, pageSize.value, params)
    
    if (res && res.status === 200) {
      tableData.value = res.data.rows || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取角色数据失败:', error)
    ElMessage.error('获取角色数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchRoleData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchRoleData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理新增
const handleAdd = () => {
  dialogMode.value = 'add'
  dialogData.value = {}
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row) => {
  dialogMode.value = 'edit'
  dialogData.value = { ...row }
  dialogVisible.value = true
}

// 处理详情
const handleDetail = (row) => {
  dialogMode.value = 'view'
  dialogData.value = { ...row }
  dialogVisible.value = true
}

// 处理权限设置
const handlePermissionSetting = (row) => {
  dialogMode.value = 'permission'
  dialogData.value = { ...row }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row) => {
  if (row.state === '0') {
    ElMessage.warning('正常状态的角色不可删除，请先停用')
    return
  }

  ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteRole(row.id)
      if (res && res.status === 200) {
        ElMessage.success('删除成功')
        fetchRoleData()
      } else {
        ElMessage.error(res?.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败'+res?.msg)
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理状态变化
const handleStatusChange = async (row) => {
  try {
    const statusText = row.state === '0' ? '启用' : '停用'
    await ElMessageBox.confirm(`确定要${statusText}该角色吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await updateRole({
      ...row,
      createTime: row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : undefined,
      updateTime: row.updateTime ? moment(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : undefined
    })

    if (res && res.status === 200) {
      ElMessage.success(`${statusText}成功`)
      fetchRoleData()
    } else {
      // 回滚状态
      row.state = row.state === '0' ? '1' : '0'
      ElMessage.error(res?.msg || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      // 回滚状态
      row.state = row.state === '0' ? '1' : '0'
      console.error('更新角色状态失败:', error)
      ElMessage.error('更新角色状态失败')
    } else {
      // 用户取消，回滚状态
      row.state = row.state === '0' ? '1' : '0'
    }
  }
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchRoleData()
}

// 在组件挂载后获取数据
onMounted(async () => {
  // 确保用户信息存在
  if (!userStore.userInfo) {
    await userStore.getUserInfo()
  }
  
  fetchRoleData()
})
</script>

<style scoped>
.role-management-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.role-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.operation-btn {
  height: 32px;
  padding: 0 16px;
  background: #0277FD;
  border-radius: 2px;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* Switch 开关样式 */
:deep(.el-switch) {
  --el-switch-on-color: #13ce66;
  --el-switch-off-color: #ff4949;
}

:deep(.el-switch__label) {
  font-size: 12px;
}

:deep(.el-switch__label.is-active) {
  color: #13ce66;
}

:deep(.el-switch__label:not(.is-active)) {
  color: #ff4949;
}
</style> 