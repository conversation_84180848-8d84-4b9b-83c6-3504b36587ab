<template>
  <div class="inspection-order-container">
    <!-- 搜索区域 -->
    <div class="inspection-order-search">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属专项:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">巡检状态:</span>
          <el-select v-model="formData.recordStatus" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in recordStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.taskUserName" class="form-input" placeholder="输入巡检人员" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="taskCode" label="任务编码" min-width="120">
          <template #default="{ row }">
            {{ row.taskCode }}
          </template>
        </el-table-column>
        <el-table-column prop="taskName" label="任务名称" min-width="120">
          <template #default="{ row }">
            {{ row.taskName }}
          </template>
        </el-table-column>
        <el-table-column prop="relatedBusinessName" label="所属专项" min-width="100">
          <template #default="{ row }">
            {{ row.relatedBusinessName }}
          </template>
        </el-table-column>
        <el-table-column prop="inspectionTime" label="计划检查时间" min-width="120">
          <template #default="{ row }">
            {{ row.inspectionTime }}
          </template>
        </el-table-column>
        <el-table-column prop="completeTime" label="巡检完成时间" min-width="120">
          <template #default="{ row }">
            {{ row.completeTime }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceCount" label="巡检设备数" min-width="100">
          <template #default="{ row }">
              {{ row.deviceCount }}
          </template>
        </el-table-column>
        <el-table-column prop="taskUserName" label="巡检人员" min-width="100">
          <template #default="{ row }">
            {{ row.taskUserName }}
          </template>
        </el-table-column>
        <el-table-column prop="recordStatusName" label="任务状态" min-width="100">
          <template #default="{ row }">
            <span :class="getStatusClass(row.recordStatus)">
              {{ row.recordStatusName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <el-button type="primary" link @click.stop="handleView(row)">详情</el-button>
              <el-button 
                type="primary" 
                link 
                @click.stop="handleReport(row)"
                v-if="canReport(row)"
              >
                填报
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 对话框区域 -->
    <InspectionRecordDialog
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :record-id="selectedRecordId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getInspectionRecordPage } from '@/api/comprehensive'
import { 
  RELATED_BUSINESS_OPTIONS, 
  INSPECTION_RECORD_STATUS_OPTIONS 
} from '@/constants/comprehensive'
import InspectionRecordDialog from './components/InspectionRecordDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const recordStatusOptions = ref(INSPECTION_RECORD_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  relatedBusiness: '',
  recordStatus: '',
  taskUserName: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('view') // 'view' | 'report'
const selectedRecordId = ref('')

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 7003901: // 未开始
      return 'status-not-started'
    case 7003902: // 进行中
      return 'status-in-progress'
    case 7003903: // 已完成
      return 'status-completed'
    default:
      return 'status-not-started'
  }
}

// 判断是否可以填报
const canReport = (row) => {
  // 未开始或进行中状态可以填报
  return row.recordStatus === 7003901 || row.recordStatus === 7003902
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchInspectionRecordData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    recordStatus: '',
    taskUserName: ''
  }
  currentPage.value = 1
  fetchInspectionRecordData()
}

// 获取巡检工单分页数据
const fetchInspectionRecordData = async () => {
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      recordStatus: formData.value.recordStatus,
      taskUserName: formData.value.taskUserName
    }
    
    const res = await getInspectionRecordPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取巡检工单数据失败:', error)
    ElMessage.error('获取巡检工单数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchInspectionRecordData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchInspectionRecordData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理查看
const handleView = (row) => {
  dialogMode.value = 'view'
  selectedRecordId.value = row.id
  dialogVisible.value = true
}

// 处理填报
const handleReport = (row) => {
  dialogMode.value = 'report'
  selectedRecordId.value = row.id
  dialogVisible.value = true
}

// 处理对话框成功提交
const handleDialogSuccess = () => {
  fetchInspectionRecordData()
}

// 在组件挂载后获取数据
onMounted(() => {
  fetchInspectionRecordData()
})
</script>

<style scoped>
.inspection-order-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 搜索区域样式 */
.inspection-order-search {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 状态样式 */
.status-not-started {
  color: #909399;
}

.status-in-progress {
  color: #E6A23C;
}

.status-completed {
  color: #67C23A;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}
</style>