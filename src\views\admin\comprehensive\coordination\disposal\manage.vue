<template>
  <div class="disposal-manage-container">
    <!-- 数据统计区域 -->
    <div class="statistics-section">
      <div class="statistics-cards">
        <!-- 预警状态统计 -->
        <div class="stat-card total-card" @click="handleStatusFilter('')">
          <div class="stat-number">{{ statusStatistics.totalCount || 0 }}</div>
          <div class="stat-label">全部 ({{ currentStatusFilter === '' ? '10' : currentPage * pageSize }})</div>
        </div>
        <div class="stat-card pending-card" @click="handleStatusFilter(7002301)">
          <div class="stat-number">{{ statusStatistics.pendingHandle || 0 }}</div>
          <div class="stat-label">待处置 ({{ currentStatusFilter === 7002301 ? '2' : statusStatistics.pendingHandle || 0 }})</div>
          <div class="stat-rate">占比: {{ statusStatistics.pendingHandleRate || '0%' }}</div>
        </div>
        <div class="stat-card handling-card" @click="handleStatusFilter(7002302)">
          <div class="stat-number">{{ statusStatistics.handling || 0 }}</div>
          <div class="stat-label">处置中 ({{ currentStatusFilter === 7002302 ? '2' : statusStatistics.handling || 0 }})</div>
          <div class="stat-rate">占比: {{ statusStatistics.handlingRate || '0%' }}</div>
        </div>
        <div class="stat-card handled-card" @click="handleStatusFilter(7002303)">
          <div class="stat-number">{{ statusStatistics.handled || 0 }}</div>
          <div class="stat-label">已处置 ({{ currentStatusFilter === 7002303 ? '2' : statusStatistics.handled || 0 }})</div>
          <div class="stat-rate">占比: {{ statusStatistics.handledRate || '0%' }}</div>
        </div>
        <div class="stat-card released-card" @click="handleStatusFilter(7002304)">
          <div class="stat-number">{{ statusStatistics.released || 0 }}</div>
          <div class="stat-label">已解除 ({{ currentStatusFilter === 7002304 ? '2' : statusStatistics.released || 0 }})</div>
          <div class="stat-rate">占比: {{ statusStatistics.releasedRate || '0%' }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <span class="label">所属行业:</span>
          <el-select v-model="formData.relatedBusiness" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in relatedBusinessOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预警类型:</span>
          <el-select v-model="formData.warningType" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <span class="label">预警时间:</span>
          <el-date-picker
            v-model="formData.warningTime"
            type="daterange"
            class="form-input"
            placeholder="请选择时间范围"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </div>
        <div class="form-item">
          <span class="label">预警状态:</span>
          <el-select v-model="formData.warningStatus" class="form-input" placeholder="请选择">
            <el-option label="全部" value="" />
            <el-option v-for="item in warningStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="form-item">
          <el-input v-model="formData.warningTitle" class="form-input" placeholder="输入预警标题" />
        </div>
        <div class="form-item">
          <el-button type="primary" class="search-btn" @click="handleSearch">查询</el-button>
          <el-button class="reset-btn" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        :header-cell-style="headerCellStyle"
        :row-class-name="tableRowClassName" 
        @row-click="handleRowClick" 
        height="100%"
        empty-text="暂无数据"
      >
        <el-table-column label="序号" min-width="60">
          <template #default="{ $index }">
            {{ (currentPage - 1) * pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="relatedBusinessName" label="所属行业" min-width="80" />
        <el-table-column prop="warningCode" label="预警编号" min-width="120" />
        <el-table-column prop="warningTitle" label="预警标题" min-width="150" />
        <el-table-column prop="warningTypeName" label="预警类型" min-width="120" />
        <el-table-column prop="warningDesc" label="预警描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="warningTime" label="预警时间" min-width="120" />
        <el-table-column prop="publishUnitName" label="发布单位" min-width="120" />
        <el-table-column label="预警级别" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="getWarningLevelType(row.warningLevel)"
              size="small"
            >
              {{ row.warningLevelName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="预警状态" min-width="100">
          <template #default="{ row }">
            <el-tag
              :type="getWarningStatusType(row.warningStatus)"
              size="small"
            >
              {{ row.warningStatusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
          <template #default="{ row }">
            <div class="operation-btns">
              <!-- 待处置和处置中时显示处置按钮 -->
              <el-button 
                type="primary" 
                link 
                @click.stop="handleDetail(row)"
                v-if="row.warningStatus === 7002301 || row.warningStatus === 7002302"
              >
                处置
              </el-button>
              <!-- 详情按钮 -->
              <el-button type="primary" link @click.stop="handleViewDetail(row)">详情</el-button>
              <!-- 定位按钮 -->
              <el-button type="primary" link @click.stop="handleLocation(row)">定位</el-button>
              <!-- 已处置时显示解除按钮 -->
              <el-button 
                type="warning" 
                link 
                @click.stop="handleRelease(row)"
                v-if="row.warningStatus === 7002303"
              >
                解除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 处置列表对话框 -->
    <el-dialog
      v-model="processListVisible"
      title="处置列表"
      width="1200px"
      :close-on-click-modal="false"
      class="process-list-dialog"
    >
      <div class="process-list-container">
        <!-- 第一层处置列表 -->
        <div class="first-level-section">
          <div class="section-header">
            <h3>处置列表</h3>
            <div class="warning-info">
              <span class="warning-label">预警编号:</span>
              <span class="warning-value">{{ selectedWarning?.warningCode }}</span>
            </div>
          </div>
          
          <el-table :data="firstLevelData" style="width: 100%" class="first-level-table">
            <el-table-column label="序号" width="60">
              <template #default="{ $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="warningCode" label="预警编号" min-width="120" />
            <el-table-column prop="warningTime" label="预警时间" min-width="120" />
            <el-table-column prop="dealUnitName" label="处置单位" min-width="120" />
            <el-table-column prop="dealUserName" label="处置人员" min-width="100" />
            <el-table-column label="处置状态" min-width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getWarningStatusType(row.warningStatus)"
                  size="small"
                >
                  {{ row.warningStatusName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dealTime" label="处置时间" min-width="120" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleViewDeal(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 第二层处置详情 -->
        <div class="second-level-section" v-if="showSecondLevel">
          <div class="section-header">
            <div class="back-btn" @click="handleBackToFirst">
              <el-icon><ArrowLeft /></el-icon>
              <span>返回</span>
            </div>
            <h3>处置列表</h3>
            <div class="warning-info">
              <span class="red-dot"></span>
              <span class="highlight-text">点击处置列表的详情跳转至此</span>
            </div>
          </div>

          <div class="second-level-content">
            <div class="table-actions">
              <el-button type="primary" @click="handleAddDealStatus">新增处置</el-button>
            </div>
            
            <el-table :data="secondLevelData" style="width: 100%" class="second-level-table">
              <el-table-column label="序号" width="60">
                <template #default="{ $index }">
                  {{ $index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="dealTime" label="处置时间" min-width="120" />
              <el-table-column label="处置状态" min-width="100">
                <template #default="{ row }">
                  <el-tag
                    :type="getDealStatusType(row.dealStatus)"
                    size="small"
                  >
                    {{ row.dealStatusName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="处置描述" min-width="200" show-overflow-tooltip />
              <el-table-column label="处置图片" min-width="120">
                <template #default="{ row }">
                  <div class="image-preview">
                    <el-icon v-for="i in 2" :key="i" class="image-placeholder">
                      <Picture />
                    </el-icon>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="{ row }">
                  <div class="operation-btns">
                    <el-button type="primary" link @click="handleEditDealStatus(row)">编辑</el-button>
                    <el-button type="primary" link @click="handleViewDealStatus(row)">详情</el-button>
                    <el-button type="danger" link @click="handleDeleteDealStatus(row)">删除</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 处置状态对话框 -->
    <DealStatusDialog
      v-model:visible="dealStatusDialogVisible"
      :mode="dealStatusDialogMode"
      :data="dealStatusDialogData"
      :warning-id="selectedWarning?.id"
      :deal-id="selectedDeal?.id"
      @success="handleDealStatusDialogSuccess"
    />

    <!-- 预警详情对话框 -->
    <WarningDetailDialog
      v-model:visible="warningDetailVisible"
      :data="selectedWarning"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElPagination, ElMessage, ElTag, ElMessageBox, ElIcon } from 'element-plus'
import { ArrowLeft, Picture } from '@element-plus/icons-vue'
import {
  getWarningInfoPage,
  getWarningStatusStatistics,
  getWarningDealList,
  getWarningDealStatusList,
  deleteWarningDealStatus,
  getWarningDealStatusDetail,
  updateWarningInfo,
  WARNING_LEVEL_OPTIONS,
  WARNING_STATUS_OPTIONS,
  WARNING_TYPE_OPTIONS,
  RELATED_BUSINESS_OPTIONS
} from '@/api/comprehensive'
import { misPosition } from '@/hooks/gishooks'
import DealStatusDialog from './components/DealStatusDialog.vue'
import WarningDetailDialog from './components/WarningDetailDialog.vue'

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableData = ref([])

// 统计数据
const statusStatistics = ref({})
const currentStatusFilter = ref('') // 当前状态筛选

// 下拉选项数据
const relatedBusinessOptions = ref(RELATED_BUSINESS_OPTIONS)
const warningTypeOptions = ref(WARNING_TYPE_OPTIONS)
const warningStatusOptions = ref(WARNING_STATUS_OPTIONS)

// 表单数据
const formData = ref({
  relatedBusiness: '',
  warningType: '',
  warningTime: [],
  warningStatus: '', // 与currentStatusFilter联合使用
  warningTitle: ''
})

// 处置列表对话框相关
const processListVisible = ref(false)
const selectedWarning = ref({})
const firstLevelData = ref([])
const secondLevelData = ref([])
const showSecondLevel = ref(false)
const selectedDeal = ref({})

// 处置状态对话框相关
const dealStatusDialogVisible = ref(false)
const dealStatusDialogMode = ref('add') // 'add' | 'edit' | 'view'
const dealStatusDialogData = ref({})

// 预警详情对话框相关
const warningDetailVisible = ref(false)

// 表格样式
const headerCellStyle = {
  background: '#F5F7FA',
  color: '#606266',
  height: '40px',
  fontWeight: 'bold'
}

// 表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row'
}

// 获取预警级别标签类型
const getWarningLevelType = (level) => {
  const typeMap = {
    7002201: 'danger',  // 一级预警 - 红色
    7002202: 'warning', // 二级预警 - 橙色
    7002203: 'success'  // 三级预警 - 绿色
  }
  return typeMap[level] || ''
}

// 获取预警状态标签类型
const getWarningStatusType = (status) => {
  const typeMap = {
    7002301: 'danger',  // 待处置
    7002302: 'warning', // 处置中
    7002303: 'success', // 已处置
    7002304: 'info'     // 已解除
  }
  return typeMap[status] || ''
}

// 获取处置状态标签类型
const getDealStatusType = (status) => {
  const typeMap = {
    7001601: 'info',    // 发布预警
    7001602: 'warning', // 现场处置
    7001603: 'success', // 处置完成
    7001604: 'success'  // 预警解除
  }
  return typeMap[status] || ''
}

// 处理状态筛选
const handleStatusFilter = (status) => {
  currentStatusFilter.value = status
  formData.value.warningStatus = status
  currentPage.value = 1
  fetchWarningData()
}

// 处理查询
const handleSearch = () => {
  currentPage.value = 1
  fetchWarningData()
}

// 处理重置
const handleReset = () => {
  formData.value = {
    relatedBusiness: '',
    warningType: '',
    warningTime: [],
    warningStatus: currentStatusFilter.value, // 保持状态筛选
    warningTitle: ''
  }
  currentPage.value = 1
  fetchWarningData()
}

// 获取预警分页数据
const fetchWarningData = async () => {
  try {
    const params = {
      relatedBusiness: formData.value.relatedBusiness,
      warningType: formData.value.warningType,
      warningStatus: formData.value.warningStatus || currentStatusFilter.value || "", // 默认为""查询所有
      warningTitle: formData.value.warningTitle
    }
    
    // 处理时间范围
    if (formData.value.warningTime && formData.value.warningTime.length === 2) {
      params.startTime = formData.value.warningTime[0] + ' 00:00:00'
      params.endTime = formData.value.warningTime[1] + ' 23:59:59'
    }
    
    const res = await getWarningInfoPage(currentPage.value, pageSize.value, params)
    
    if (res && res.code === 200) {
      tableData.value = res.data.records || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取预警数据失败:', error)
    ElMessage.error('获取预警数据失败')
    tableData.value = []
    total.value = 0
  }
}

// 获取预警状态统计
const fetchStatusStatistics = async () => {
  try {
    const res = await getWarningStatusStatistics()
    if (res && res.code === 200) {
      statusStatistics.value = res.data || {}
    }
  } catch (error) {
    console.error('获取预警状态统计失败:', error)
  }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchWarningData()
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchWarningData()
}

// 处理行点击
const handleRowClick = (row) => {
  console.log('行点击', row)
}

// 处理详情
const handleDetail = async (row) => {
  selectedWarning.value = row
  processListVisible.value = true
  showSecondLevel.value = false
  await fetchFirstLevelData(row.id)
}

// 处理编辑
const handleEdit = (row) => {
  ElMessage.info('编辑功能请在预警管理页面操作')
}

// 处理定位
const handleLocation = (row) => {
  if (
    row.longitude &&
    row.longitude != '' &&
    row.latitude &&
    row.latitude != ''
  ) {
    misPosition.value = {
      longitude: row.longitude,
      latitude: row.latitude
    }
  } else {
    ElMessage.warning('没有经纬度，无法定位！')
  }
}

// 获取第一层处置数据
const fetchFirstLevelData = async (warningId) => {
  try {
    const res = await getWarningDealList({ warningId })
    if (res && res.code === 200) {
      firstLevelData.value = res.data || []
    }
  } catch (error) {
    console.error('获取处置列表失败:', error)
    ElMessage.error('获取处置列表失败')
  }
}

// 处理查看处置详情
const handleViewDeal = async (row) => {
  selectedDeal.value = row
  showSecondLevel.value = true
  await fetchSecondLevelData(row.id)
}

// 获取第二层处置状态数据
const fetchSecondLevelData = async (dealId) => {
  try {
    const res = await getWarningDealStatusList({ dealId })
    if (res && res.code === 200) {
      secondLevelData.value = res.data || []
    }
  } catch (error) {
    console.error('获取处置状态列表失败:', error)
    ElMessage.error('获取处置状态列表失败')
  }
}

// 返回第一层
const handleBackToFirst = () => {
  showSecondLevel.value = false
  selectedDeal.value = {}
}

// 处理新增处置状态
const handleAddDealStatus = () => {
  dealStatusDialogMode.value = 'add'
  dealStatusDialogData.value = {}
  dealStatusDialogVisible.value = true
}

// 处理编辑处置状态
const handleEditDealStatus = async (row) => {
  try {
    const res = await getWarningDealStatusDetail(row.id)
    if (res && res.code === 200) {
      dealStatusDialogMode.value = 'edit'
      dealStatusDialogData.value = res.data
      dealStatusDialogVisible.value = true
    } else {
      ElMessage.error('获取处置详情失败')
    }
  } catch (error) {
    console.error('获取处置详情失败:', error)
    ElMessage.error('获取处置详情失败')
  }
}

// 处理查看处置状态
const handleViewDealStatus = async (row) => {
  try {
    const res = await getWarningDealStatusDetail(row.id)
    if (res && res.code === 200) {
      dealStatusDialogMode.value = 'view'
      dealStatusDialogData.value = res.data
      dealStatusDialogVisible.value = true
    } else {
      ElMessage.error('获取处置详情失败')
    }
  } catch (error) {
    console.error('获取处置详情失败:', error)
    ElMessage.error('获取处置详情失败')
  }
}

// 处理删除处置状态
const handleDeleteDealStatus = (row) => {
  ElMessageBox.confirm('是否确认解除预警？', '解除预警', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const res = await deleteWarningDealStatus(row.id)
      if (res && res.code === 200) {
        ElMessage.success('删除成功')
        await fetchSecondLevelData(selectedDeal.value.id)
      } else {
        ElMessage.error(res?.message || '删除失败')
      }
    } catch (error) {
      console.error('删除处置状态失败:', error)
      ElMessage.error('删除处置状态失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 处理处置状态对话框成功提交
const handleDealStatusDialogSuccess = () => {
  if (selectedDeal.value.id) {
    fetchSecondLevelData(selectedDeal.value.id)
  }
}

// 处理预警详情
const handleViewDetail = (row) => {
  selectedWarning.value = row
  warningDetailVisible.value = true
}

// 处理预警解除
const handleRelease = (row) => {
  ElMessageBox.confirm('是否确认解除预警？', '解除预警', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const updateData = {
        ...row,
        warningStatus: 7002304 // 已解除
      }
      const res = await updateWarningInfo(updateData)
      if (res && res.code === 200) {
        ElMessage.success('解除成功')
        await fetchWarningData()
        await fetchStatusStatistics()
      } else {
        ElMessage.error(res?.message || '解除失败')
      }
    } catch (error) {
      console.error('解除预警失败:', error)
      ElMessage.error('解除预警失败')
    }
  }).catch(() => {
    // 取消解除
  })
}

// 在组件挂载后获取数据
onMounted(async () => {
  try {
    await Promise.all([
      fetchStatusStatistics(),
      fetchWarningData()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败')
  }
})
</script>

<style scoped>
.disposal-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: white;
}

/* 统计区域样式 */
.statistics-section {
  width: 100%;
  margin-bottom: 16px;
}

.statistics-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-card {
  flex: 1;
  min-width: 120px;
  height: 80px;
  padding: 16px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  margin-bottom: 2px;
}

.stat-rate {
  font-size: 12px;
  opacity: 0.9;
}

.total-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.pending-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.handling-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.handled-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.released-card {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 搜索区域样式 */
.search-section {
  width: 100%;
  margin-bottom: 16px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.form-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 16px;
}

.label {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #282828;
  margin-right: 8px;
  white-space: nowrap;
}

.form-input {
  width: 180px;
  height: 32px;
}

:deep(.el-input__wrapper) {
  background: #FFFFFF;
  border-radius: 2px;
  border: 1px solid #CED3DA;
  height: 32px;
  box-shadow: none !important;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #0066FF inset !important;
}

.search-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  margin-right: 8px;
}

.reset-btn {
  height: 32px;
  padding: 0 16px;
  border-radius: 2px;
  border: 1px solid #DCDFE6;
}

/* 表格样式 */
.table-container {
  flex: 1;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table .even-row) {
  background-color: #FAFAFA;
}

:deep(.el-table .odd-row) {
  background-color: #FFFFFF;
}

.operation-btns {
  display: flex;
  gap: 8px;
}

/* 分页样式 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 处置列表对话框样式 */
.process-list-dialog {
  font-family: PingFangSC, PingFang SC;
}

.process-list-container {
  height: 600px;
  overflow: hidden;
}

.first-level-section,
.second-level-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.warning-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.warning-label {
  color: #666;
}

.warning-value {
  color: #333;
  font-weight: 500;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #0277FD;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-btn:hover {
  background-color: #f0f0f0;
}

.red-dot {
  width: 8px;
  height: 8px;
  background-color: #ff4d4f;
  border-radius: 50%;
}

.highlight-text {
  color: #ff4d4f;
  font-size: 14px;
}

.second-level-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-actions {
  margin-bottom: 16px;
}

.first-level-table,
.second-level-table {
  flex: 1;
}

.image-preview {
  display: flex;
  gap: 4px;
}

.image-placeholder {
  width: 24px;
  height: 24px;
  color: #999;
  cursor: pointer;
}

.image-placeholder:hover {
  color: #0277FD;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .statistics-cards {
    flex-direction: column;
  }
  
  .stat-card {
    min-width: 100%;
  }
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item {
    margin-right: 0;
    justify-content: space-between;
  }
  
  .form-input {
    width: 200px;
  }
}
</style>