<template>
  <div class="planning-container">
    <el-tabs v-model="activeTab" class="planning-tabs">
      <el-tab-pane label="规划项目" name="planning">
        <PlanningProjects />
      </el-tab-pane>
      <el-tab-pane label="年度重点建设项目" name="keyProjects">
        <KeyProjects />
      </el-tab-pane>
      <el-tab-pane label="统计分析" name="statistics">
        <StatisticsAnalysis />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PlanningProjects from './components/PlanningProjects.vue'
import KeyProjects from './components/KeyProjects.vue'
import StatisticsAnalysis from './components/StatisticsAnalysis.vue'

// 当前激活的选项卡
const activeTab = ref('planning')

onMounted(() => {
  console.log('规划建设组件已挂载')
})
</script>

<style scoped>
.planning-container {
  padding: 0;
}

.planning-tabs {
  padding: 0;
}

:deep(.el-tabs__header) {
  margin: 0;
  padding: 0 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-tabs__nav-wrap) {
  padding: 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 0;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.mb-4 {
  margin-bottom: 1rem;
}

.bg-white {
  background-color: white;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
</style>