<template>
  <PanelBox title="风险统计">
    <template #extra>
      <div class="com-select">
        <CommonSelect v-model="selectedPipeType" :options="pipeTypeOptions" @change="handlePipeTypeChange" />
      </div>
    </template>
    <div class="panel-content">
      <div class="content-wrapper">
        <div class="risk-chart">
          <div class="chart-container" ref="chartRef"></div>
          <div class="center-text">
            <div class="risk-total">{{ totalRisk }}</div>
            <div class="unit">{{ unit }}</div>
          </div>
        </div>
        <div class="risk-list">
          <div class="risk-item" v-for="(item, index) in riskItems" :key="index">
            <div class="risk-indicator" :style="{ background: item.color }"></div>
            <div class="risk-name">{{ item.name }}</div>
            <div class="risk-value">{{ item.value }} <span class="unit-text">{{ unit }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </PanelBox>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import * as echarts from 'echarts'
import PanelBox from '@/components/screen/PanelBox.vue'
import CommonSelect from '@/components/screen/CommonSelect.vue'
import { getPipelineRiskStatistics, getFactoryRiskStatistics, getStationRiskStatistics } from '@/api/heating'

// 管线类型选项
const pipeTypeOptions = [
  { value: 'pipeline', label: '管线' },
  { value: 'factory', label: '热源厂' },
  { value: 'station', label: '换热站' }
]

// 默认选择管线
const selectedPipeType = ref('pipeline')

// 定义数据源
const chartRef = ref(null)
let chartInstance = null

// 风险数据 - 使用响应式ref
const riskData = ref({
  pipeline: [
    { name: '重大风险', value: 0, color: '#DE6970', code: 2002801},
    { name: '较大风险', value: 0, color: '#FE9150', code: 2002802 },
    { name: '一般风险', value: 0, color: '#D8F115', code: 2002803 },
    { name: '低风险', value: 0, color: '#00E1B9', code: 2002804 }
  ],
  factory: [
    { name: '重大风险', value: 0, color: '#DE6970', code: 3002401 },
    { name: '较大风险', value: 0, color: '#FE9150', code: 3002402 },
    { name: '一般风险', value: 0, color: '#D8F115', code: 3002403 },
    { name: '低风险', value: 0, color: '#00E1B9', code: 3002404 }
  ],
  station: [
    { name: '重大风险', value: 0, color: '#DE6970', code: 3002401 },
    { name: '较大风险', value: 0, color: '#FE9150', code: 3002402 },
    { name: '一般风险', value: 0, color: '#D8F115', code: 3002403 },
    { name: '低风险', value: 0, color: '#00E1B9', code: 3002404 }
  ]
})

// 计算当前展示数据
const riskItems = computed(() => {
  console.log('重新计算riskItems，当前类型:', selectedPipeType.value)
  console.log('当前riskData:', JSON.parse(JSON.stringify(riskData.value[selectedPipeType.value])))
  return riskData.value[selectedPipeType.value] || []
})

// 计算总风险值
const totalRisk = computed(() => {
  return riskItems.value.reduce((sum, item) => {
    const value = typeof item.value === 'string' ? parseFloat(item.value) : item.value
    return sum + (isNaN(value) ? 0 : value)
  }, 0).toFixed(2)
})

// 计算单位
const unit = computed(() => {
  return selectedPipeType.value === 'pipeline' ? 'KM' : '个'
})

// 处理管线类型变化
const handlePipeTypeChange = () => {
  if (chartInstance) {
    updateChart()
  }
}

// 初始化图表
const initChart = () => {
  console.log('开始初始化图表...')
  if (!chartRef.value) {
    console.warn('图表DOM引用不存在，无法初始化图表')
    return
  }

  try {
    // 如果已经存在图表实例，先销毁它
    if (chartInstance) {
      console.log('销毁已存在的图表实例')
      chartInstance.dispose()
    }
    
    console.log('创建新的图表实例')
    chartInstance = echarts.init(chartRef.value)
    
    // 确保图表容器可见
    console.log('图表容器尺寸:', chartRef.value.clientWidth, chartRef.value.clientHeight)
    
    // 延迟更新图表，确保数据已经准备就绪
    console.log('初始化后延迟更新图表')
    setTimeout(() => {
      updateChart()
    }, 100)

    // 添加窗口大小变化监听器
    const resizeHandler = () => {
      if (chartInstance) {
        console.log('窗口大小变化，调整图表大小')
        chartInstance.resize()
      }
    }
    
    // 移除可能已存在的监听器，避免重复
    window.removeEventListener('resize', resizeHandler)
    window.addEventListener('resize', resizeHandler)
    
    console.log('图表初始化完成')
  } catch (error) {
    console.error('图表初始化失败:', error)
  }
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新图表')
    return
  }

  console.log('开始更新图表...')
  const data = riskItems.value
  console.log('当前风险数据:', data)
  
  const colorList = data.map(item => item.color)
  const valueList = data.map(item => {
    const value = typeof item.value === 'string' ? parseFloat(item.value) : item.value
    return isNaN(value) ? 0 : value
  })

  // 检查是否所有值都为0
  const allZero = valueList.every(value => value === 0)
  console.log('所有值是否为0:', allZero)
  console.log('处理后的数值列表:', valueList)

  // 即使所有值为0，也要显示饼图的轮廓
  const option = {
    backgroundColor: 'transparent',
    series: [{
      type: 'pie',
      radius: ['75%', '85%'],
      center: ['50%', '50%'],
      startAngle: 0,
      itemStyle: {
        borderRadius: 0,
        borderColor: 'transparent',
        borderWidth: 0
      },
      label: {
        show: false
      },
      silent: true,
      data: valueList.map((value, index) => ({
        value: value || 0.001, // 确保即使值为0也能显示饼图轮廓
        name: data[index].name,
        itemStyle: {
          color: colorList[index]
        }
      }))
    }]
  }

  console.log('设置图表选项:', option)
  chartInstance.setOption(option)
  console.log('图表更新完成')
}

// 从后端获取数据的方法
const fetchData = async (pipeType) => {
  console.log(`开始获取${pipeType}风险数据...`)
  try {
    let response = null

    // 根据选择的类型调用相应的API
    if (pipeType === 'pipeline') {
      response = await getPipelineRiskStatistics()
    } else if (pipeType === 'factory') {
      response = await getFactoryRiskStatistics()
    } else if (pipeType === 'station') {
      response = await getStationRiskStatistics()
    }

    console.log(`${pipeType}风险数据API响应:`, response)

    if (response && response.code === 200 && response.data) {
      // 记录当前的riskData状态
      console.log('更新前的riskData:', JSON.parse(JSON.stringify(riskData.value[pipeType])))

      // 处理返回的数据
      const apiRiskData = response.data.riskLevelStatistics || []
      console.log('获取到的风险数据:', apiRiskData)

      // 获取当前类型的风险数据模板
      const currentRiskData = [...riskData.value[pipeType]]

      // 根据code精确匹配更新数据
      for (let i = 0; i < currentRiskData.length; i++) {
        const riskItem = currentRiskData[i]

        // 通过code精确匹配
        const matchedItem = apiRiskData.find(item => {
          // 将code转换为字符串进行比较，因为API可能返回字符串或数字
          return String(item.code) === String(riskItem.code)
        })

        if (matchedItem) {
          console.log(`Code匹配成功: ${matchedItem.code} -> ${riskItem.name}`)
          if (pipeType === 'pipeline') {
            // 管线使用length字段，转换为公里
            // riskItem.value = parseFloat((matchedItem.length || 0) / 1000).toFixed(2)
            riskItem.value = matchedItem.length
          } else {
            // 场站使用count字段
            riskItem.value = parseInt(matchedItem.count || 0)
          }
          console.log(`更新${riskItem.name}数据:`, riskItem.value)
        } else {
          // 如果没有匹配到，尝试通过名称匹配
          const nameMatch = apiRiskData.find(item =>
            item.name === riskItem.name ||
            (item.name && item.name.includes(riskItem.name.replace('风险', '')))
          )

          if (nameMatch) {
            console.log(`名称匹配成功: ${nameMatch.name} -> ${riskItem.name}`)
            if (pipeType === 'pipeline') {
              riskItem.value = parseFloat((nameMatch.length || 0) / 1000).toFixed(2)
            } else {
              riskItem.value = parseInt(nameMatch.count || 0)
            }
            console.log(`更新${riskItem.name}数据:`, riskItem.value)
          } else {
            console.log(`未找到匹配项: ${riskItem.name}, code: ${riskItem.code}`)
            riskItem.value = 0
          }
        }
      }

      // 直接替换整个数组，确保响应式更新
      riskData.value[pipeType] = currentRiskData
      console.log('更新后的riskData:', JSON.parse(JSON.stringify(riskData.value[pipeType])))

      // 强制更新图表
      await nextTick()
      if (chartInstance) {
        console.log('更新图表...')
        updateChart()
      } else {
        console.log('图表实例不存在，重新初始化图表')
        // 如果图表实例不存在，重新初始化
        initChart()
      }
    }
  } catch (error) {
    console.error('获取风险数据失败:', error)
  }
  console.log(`${pipeType}风险数据处理完成`)
}

// 监听管线类型变化
watch(selectedPipeType, (newValue) => {
  fetchData(newValue)
})

// 监听风险数据变化，确保图表及时更新
watch(riskItems, (newValue) => {
  console.log('风险数据发生变化:', newValue)
  if (chartInstance) {
    nextTick(() => {
      updateChart()
    })
  }
}, { deep: true })

onMounted(async () => {
  await nextTick()
  // 先初始化图表，确保图表容器准备就绪
  initChart()
  // 然后获取数据并更新图表
  await fetchData(selectedPipeType.value)
})
</script>

<style scoped>
.panel-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.com-select {
  margin-right: 20px;
}

.content-wrapper {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 100%;
}

.risk-chart {
  width: 190px;
  height: 190px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-container {
  width: 190px;
  height: 190px;
  background-image: url('@/assets/images/screen/gas/guanwangfengxian.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.risk-total {
  font-size: 24px;
  font-weight: bold;
  color: #22CBFF;
  margin-bottom: 4px;
}

.unit {
  font-size: 12px;
  color: #85A5C3;
}

.risk-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  display: flex;
  align-items: center;
  width: 225px;
  height: 34px;
  background: linear-gradient(270deg, rgba(48, 71, 104, 0.5) 0%, #304768 50%, rgba(48, 71, 104, 0.5) 100%);
  border: 1px solid;
  opacity: 1;
  border-image: linear-gradient(270deg, rgba(171, 204, 255, 0), rgba(171, 204, 255, 0.5), rgba(171, 204, 255, 0)) 1 1;
  padding: 0 15px;
}

.risk-indicator {
  width: 9px;
  height: 8px;
  transform: skew(-20deg);
  margin-right: 8px;
}

.risk-name {
  width: 70px;
  color: #D3E5FF;
  font-size: 14px;
}

.risk-value {
  color: #ffffff;
  font-size: 14px;
  width: 80px;
  text-align: right;
  margin-left: auto;
}

.unit-text {
  color: #85A5C3;
  font-size: 12px;
}

/* 媒体查询适配不同分辨率 */
@media screen and (min-width: 1920px) and (max-width: 2560px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (max-width: 1919px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-width: 2561px) {
  .left-top-panel {
    height: 310px;
  }
}

/* 添加全屏模式(1080px高度)的适配 */
@media screen and (min-height: 1056px) and (max-height: 1080px) {
  .left-top-panel {
    height: 310px;
  }
}

@media screen and (min-height: 940px) and (max-height: 1055px) {
  .left-top-panel {
    height: 310px;
  }

  .panel-content {
    padding: 10px;
  }
}

@media screen and (min-height: 900px) and (max-height: 940px) {
  .left-top-panel {
    height: 252px;
  }

  .panel-content {
    padding: 8px;
  }

  .risk-chart {
    width: 180px;
    height: 180px;
  }

  .chart-container {
    width: 180px;
    height: 180px;
  }
}
</style>